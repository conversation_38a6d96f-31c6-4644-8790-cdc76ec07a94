name: data-access-policies

on:
  push:
    branches:
      - main
      - dev
  pull_request_target:
    branches:
      - dev
    
      
permissions:
  id-token: write
  pull-requests: write
  checks: write
  contents: write

jobs:
  policies-sync-ci:
    runs-on: [ self-hosted, 1v, spot, ZAnalytics,acc-infraprod,arch-x64 ]
    env:
      OPA_VERSION: "1.1.0"
      YQ_VERSION: "4.45.4"

    steps:
      - if: github.event_name == 'pull_request_target'
        name: Checkout PR Code
        uses: actions/checkout@v3
        with:
          ref: refs/pull/${{ github.event.pull_request.number }}/merge

      - if: github.event_name != 'pull_request_target'
        name: Checkout pushed code
        uses: actions/checkout@v3

      - name: Download and install OPA CLI
        run: |
          echo "Downloading OPA CLI version ${OPA_VERSION}..."
          
          # Detect architecture and map to OPA's naming convention
          ARCH=$(uname -m)
          if [ "$ARCH" = "aarch64" ]; then
            OPA_ARCH="arm64"
          else
            OPA_ARCH="amd64"
          fi
          echo "Detected architecture: $ARCH, using OPA architecture: $OPA_ARCH"
          
          # Download appropriate binary based on architecture
          curl -L -o opa_linux_static https://github.com/open-policy-agent/opa/releases/download/v${OPA_VERSION}/opa_linux_${OPA_ARCH}_static
          
          echo "Making OPA CLI executable..."
          chmod +x ./opa_linux_static
          
          echo "Moving OPA CLI to /usr/local/bin/opa ..."
          mv ./opa_linux_static /usr/local/bin/opa
          
          echo "Verifying OPA installation..."
          opa version
      - name: Download and install yq CLI
        run: |
          echo "Downloading yq CLI version ${YQ_VERSION}..."
          
          # Detect architecture and map to yq's naming convention
          ARCH=$(uname -m)
          case "$ARCH" in
            "x86_64")
              YQ_ARCH="amd64"
              ;;
            "aarch64")
              YQ_ARCH="arm64"
              ;;
            "armv7l")
              YQ_ARCH="arm"
              ;;
            *)
              echo "Unsupported architecture: $ARCH"
              exit 1
              ;;
          esac
          echo "Detected architecture: $ARCH, using yq architecture: $YQ_ARCH"
          
          # Download appropriate binary based on architecture
          curl -L -o yq_linux_${YQ_ARCH} https://github.com/mikefarah/yq/releases/download/v${YQ_VERSION}/yq_linux_${YQ_ARCH}
          
          echo "Making yq CLI executable..."
          chmod +x ./yq_linux_${YQ_ARCH}
          
          echo "Moving yq CLI to /usr/local/bin/yq ..."
          mv ./yq_linux_${YQ_ARCH} /usr/local/bin/yq
          
          echo "Verifying yq installation..."
          yq --version
      
      - name: Run YAML Lint
        run: |
          pip install jsonschema && python3 validate_rules.py
        shell: bash
  
      - name: Generate Bundle
        run: |
          chmod +x ./generate_bundle.sh
          ./generate_bundle.sh
        shell: bash
      
      - name: Check Bundle
        run: |
          opa inspect bundle.tar.gz
        shell: bash
      
      - name: Run OPA Test
        run: |
          chmod +x ./test_policy.sh
          ./test_policy.sh
        shell: bash

      - name: Configure AWS Credentials
        if: github.event_name != 'pull_request_target'
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::125719378300:role/github-runner-role
          aws-region: ap-southeast-1

      - name: Upload to S3
        if: github.event_name != 'pull_request_target'
        shell: bash
        run: |
          if [[ $GITHUB_REF_NAME = "main" ]] 
          then 
            aws s3 cp bundle.tar.gz s3://data-access-policies/bundle.tar.gz
          else
            aws s3 cp bundle.tar.gz s3://data-access-policies/dev/bundle.tar.gz
          fi
