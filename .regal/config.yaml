rules:
  performance:
    with-outside-test-context:
      level: warning
  style:
    todo-comment:
      level: ignore
    rule-length:
      max-line-length: 200
    line-length:
      max-line-length: 200
      level: warning
    external-reference:
      level: ignore
    messy-rule:
      level: ignore
    prefer-some-in-iteration:
      level: error
      ignore-nesting-level: 1
    opa-fmt:
      level: error
      ignore:
        files:
          - "*_test.rego"
    prefer-snake-case:
      level: warning
    avoid-get-and-list-prefix:
      level: warning
  bugs:
    inconsistent-args:
      level: warning
    argument-always-wildcard:
      level: warning
    leaked-internal-reference:
      level: warning
  custom:
    naming-convention:
      level: error
      conventions:
        - pattern: '^policies(\.[a-z_\.]+)?$|^rules(\.[a-z_\.]+)?$|^tools(\.[a-z_\.]+)?$|^utils(\.[a-z_\.]+)?$'
          targets:
            - package
  idiomatic:
    directory-package-mismatch:
      level: ignore
  testing:
    test-outside-test-package:
      level: ignore
  imports:
    unresolved-import:
      level: error
      except-imports:
        - tools.trinodb.*
        - data.configurations.global.tools.trinodb.operation_privilege_mapping
        - data.configurations.global.lob_tenant_mapping
        - data.configurations
        - data.configurations.global.sources
        - data.configurations.global.rules
        - data.configurations.global.catalog_tenant_mapping

capabilities:
  from:
    engine: opa
    version: v1.1.0

ignore:
  files:
    - "*_test.rego"

project:
  roots:
    - policies
    - rules
    - tools
    - utils