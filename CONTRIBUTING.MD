## Getting Started - Contributing Configurations

New contributors will primarily add or modify YAML configuration files within the `configurations/` directory to define or update access rules.

### Prerequisites

-   Basic understanding of Git and GitHub.
-   Familiarity with YAML syntax.
-   (Optional but Recommended) OPA installed locally to run tests. See [OPA Installation Guide](https://www.openpolicyagent.org/docs/latest/get-started/#installation).
-   Access to this Git repository and permissions to create branches/Pull Requests.

### Contribution Workflow

We follow a standard Git workflow:

```mermaid
graph TD
    subgraph "Local Development & Testing"
        direction TB
        A([Clone Repository]) --> B["Create New Branch <br/> (e.g., feature/add-blinkit-rule)"];
        B --> C["Add/Modify Config Files <br/> in configurations/"];
        C --> D["Add/Modify Test Cases <br/> in tools/trinodb/tests/ or tests/"];
        D --> E["Run Tests Locally <br/> (sh ./test_policy.sh)"];
        E -- "✅ All Tests Pass" --> F;
        E -- "❌ Tests Fail" --> C;
    end

    subgraph "Code Submission, Review & Merge"
        direction TB
        F["Commit Changes <br/> with Clear Message"];
        F --> G["Push Branch to Remote"];
        G --> H["Create Pull Request (PR)"];
        H --> I["Code Review & Discussion"];
        I -- "👍 Approved" --> J["Merge PR to Dev Branch"];
        I -- "🔄 Changes Requested" --> F;
        J -- "✅ Approved" --> K["D2M"];
        %% Loops back to commit after addressing feedback
    end

    subgraph "Deployment"
        direction TB
        K --> L([Deployment ]);
    end

    %% Styling
    classDef default fill:#FFFFFF,stroke:#4B5563,stroke-width:2px,color:#1F2937;
    classDef startEnd fill:#EFF6FF,stroke:#3B82F6,stroke-width:2px,rx:10px,ry:1
```

### What to Contribute & Where

1.  **Tenant-Specific Policy Rules**:
    *   **What**: Rules that apply only to a specific tenant (e.g., Zomato, Blinkit). This includes permissions for catalogs, schemas, tables, columns, row filters, column masks, etc., for users/groups within that tenant.
    *   **Where**: `configurations/<tenant_name>/rules/data.yaml`
    *   **Example**: Granting a group in Blinkit `SELECT` access to a specific table.

2.  **Global Policy Rules**:
    *   **What**: Rules that apply universally across all tenants. These are typically for broad access controls, system-level permissions, or default behaviors.
    *   **Where**: `configurations/global/rules/data.yaml`
    *   **Example**: Defining default impersonation rules for service accounts.

3.  **Global Mappings**:
    *   **Catalog-to-Tenant Mapping**:
        *   **What**: Defines which tenant a data catalog belongs to. Essential for routing policies correctly.
        *   **Where**: `configurations/global/catalog_tenant_mapping/data.yaml`
    *   **LOB-to-Tenant Mapping**:
        *   **What**: Maps Line of Business (LOB) names to tenants. Used for attribute-based access.
        *   **Where**: `configurations/global/lob_tenant_mapping/data.yaml`

4.  **Filter Templates (Tenant-Specific)**:
    *   **What**: Reusable templates for row-level security filters.
    *   **Where**: `configurations/<tenant_name>/templates/data.yaml`
    *   **Example**: A template that filters data based on `current_user`'s region.

5.  **Test Cases**:
    *   **What**: New or updated test cases to validate your configuration changes. Tests are written in Rego.
    *   **Where**:
        *   For Trino-specific features: `tools/trinodb/tests/your_test_file_test.rego`
        *   For general policy logic: `rules/tests/your_test_file_test.rego`
    *   **Why**: Ensures your changes work as expected and don't break existing functionality.

### Step-by-Step Contribution Process

1.  **Clone the Repository**:
    ```bash
    git clone <repository_url>
    cd data-access-policies
    ```

2.  **Create a New Branch**:
    ```bash
    git checkout -b feature/your-descriptive-branch-name
    ```

3.  **Add/Modify Configuration Files**:
    *   Navigate to the appropriate directory under `configurations/`.
    *   Edit existing `data.yaml` files or add new entries following the established structure. See examples below.

4.  **Add/Modify Test Files (Crucial!)**:
    *   For every non-trivial configuration change, add or update corresponding test cases.
    *   Examine existing files in `tools/trinodb/tests/` for examples.
    *   Tests typically define an `input` representing a request and assert the expected `allow` decision or filter/mask expression.

5.  **Run Tests Locally**:
    *   Execute the test script from the root of the repository:
        ```bash
        ./test_policy.sh
        ```
    *   Ensure all tests pass. If not, debug your configurations or tests.

6.  **Commit and Push Changes**:
    *   Stage your changes:
        ```bash
        git add configurations/your_modified_file.yaml
        git add tools/trinodb/tests/your_new_test_file.rego
        ```
    *   Commit with a clear, descriptive message:
        ```bash
        git commit -m "feat: Add rule for X in tenant Y and corresponding tests"
        # Or fix: "fix: Corrected Z permission for group A"
        ```
    *   Push your branch to the remote repository:
        ```bash
        git push origin feature/your-descriptive-branch-name
        ```

7.  **Create a Pull Request (PR)**:
    *   Go to the GitHub repository page.
    *   You should see a prompt to create a PR from your recently pushed branch.
    *   Fill in the PR template, explaining your changes and linking to any relevant issues.
    *   Assign reviewers.

8.  **Address Feedback**:
    *   Reviewers may provide feedback or request changes.
    *   Make necessary updates, commit, and push them to your branch. The PR will update automatically.

9.  **Merge**:
    *   Once approved and all checks pass, your PR will be merged into the main branch by a maintainer.

### Examples of Configuration File Structures

#### Tenant Policy Rule (`configurations/<tenant>/rules/data.yaml`)

```yaml
# Example from configurations/blinkit/rules/data.yaml
- type: catalog # Rule type (catalog, schema, table, column, row_filter, column_mask, etc.)
  entity_category: data_entity # Category of the entity (e.g., data_entity, query_engine_entity)
  user: ".*" # Regex for user email, ".*" means any user
  group: "data-analyst-blinkit" # Specific group this rule applies to
  catalog: "blinkit_derived" # Catalog name (regex can be used)
  schema: "finance" # Schema name (regex can be used)
  table: "daily_sales_summary" # Table name (regex can be used)
  allow: ["SELECT""] # Privileges granted
  # tenant: "blinkit" # Implicitly Blinkit due to file location, can be explicit for cross-tenant
```

#### Global Mapping (`configurations/global/catalog_tenant_mapping/data.yaml`)

```yaml
# Example from configurations/global/catalog_tenant_mapping/data.yaml
# This file maps catalog names (or patterns) to one or more tenant names.
zomato_catalog:
  - zomato
blinkit_iceberg_staging: blinkit # A catalog named 'blinkit_iceberg_staging' belongs to 'blinkit' tenant
common_reference: global # 'common_reference' catalog is considered global
```

#### Filter Template (`configurations/zomato/templates/data.yaml`)

```yaml
# Example from configurations/zomato/templates/data.yaml
row_level_filters:
  supply_kams: # Name of the filter template
    # Template string. ${res_id_col} will be replaced by the column name needing filtering.
    # current_user is an OPA-provided variable representing the user making the query.
    template: "${res_id_col} in (select res_id from jumbo_derived.res_access_control_mapping where email = current_user)"
    default_value: "res_id" # Default column name if not specified in the rule
    description: "Filter for supply KAMs to see only their assigned restaurants."
```
To use this template in a rule:
```yaml
# In configurations/zomato/rules/data.yaml
- type: row_filter
  entity_category: data_entity
  group: "supply_kam_group"
  catalog: "zomato_data"
  schema: "restaurants"
  table: "restaurant_details"
  filter_name: "supply_kams" # Refers to the template name
  filter_on_column: "restaurant_identifier_column" # This will replace ${res_id_col}
  allow: true
```
