# DATA ACCESS POLICIES MIGRATION GUIDE

## Executive Summary

This document outlines the complete migration process from the legacy sequential rule evaluation system (`old_rules.json`) to the new OPA-based policy system with OR logic evaluation. **CRITICAL**: The migration must account for OPA's `default allow := true` configuration to prevent security breaches.

## Migration Overview

The migration script (`migration_script_final.py`) implements a **3-phase process** to safely convert 441+ original JSON rules to OPA YAML format while preserving security semantics and handling rule conflicts through user/group exclusion patterns.

### Key Features Implemented

1. **Entity Scoping Validation**: Prevents global deny rules that would block all access
2. **User/Group Exclusion Logic**: Uses `user_negated` and `group_negated` flags instead of complex regex
3. **Malformed Rule Filtering**: Automatically skips incomplete rules (missing user/group/privileges)
4. **Comprehensive Statistics**: Tracks migration progress and rule transformations
5. **Safety Checks**: Multiple validation layers to prevent security breaches

## Migration Requirements

### 1. Functional Requirements

#### 1.1 Rule Preservation
- **MUST** preserve all original rules from `old_rules.json` (varies by environment)
- **MUST** maintain authorization semantics for all users and groups
- **MUST** convert sequential "first-match-wins" logic to OR-based "any-match-allows" logic
- **MUST** preserve both allow and deny rules with appropriate conflict resolution
- **MUST** filter out malformed rules (missing essential fields)

#### 1.2 Security Requirements
- **CRITICAL**: Account for OPA's `default allow := true` configuration
- **MUST** preserve all deny rules to maintain security boundaries
- **MUST** implement user/group exclusions from deny rules when they have specific allow rules
- **MUST** validate deny rules have proper entity scoping (catalog/schema/table)
- **MUST** prevent global deny rules that would block all access
- **MUST** maintain "block everything else" security model for users without specific permissions
- **MUST** prevent unintended data access during migration

#### 1.3 Performance Requirements
- **SHOULD** complete migration within reasonable time (< 5 minutes)
- **SHOULD** handle large datasets (400+ rules) efficiently
- **SHOULD** optimize YAML serialization for large rule sets
- **SHOULD** provide comprehensive logging and statistics

### 2. Input/Output Specifications

#### 2.1 Input Format (`old_rules.json`)
```json
{
  "catalogs": [...],
  "schemas": [...],
  "tables": [
    {
      "user": "string",
      "group": "string", 
      "catalog": "string",
      "schema": "string",
      "action": "allow|deny",
      "privileges": ["SELECT", "INSERT", ...]
    }
  ]
}
```

#### 2.2 Output Format (YAML)
```yaml
- type: table
  entity_category: data_entity
  user: "string"
  user_negated: true  # NEW: Indicates user pattern is negated
  group: "string"
  group_negated: true # NEW: Indicates group pattern is negated
  catalog: "string"
  schema: "string"
  table: "string"
  privileges: ["SELECT"] # Empty list [] for deny rules
```

### 3. Migration Process (3-Phase Implementation)

#### 3.1 Phase 1: Rule Collection and Classification
```
1. Parse JSON input file
2. Identify allow vs deny rules:
   - action: "deny" → deny rule
   - privileges: [] → deny rule
   - missing privileges field → deny rule
   - otherwise → allow rule
3. Build user/group specific allow maps
4. Filter malformed rules (missing essential fields)
```

#### 3.2 Phase 2: Allow Rule Processing
```
1. Convert all allow rules to YAML format
2. Add default SELECT privilege if none specified
3. Preserve all entity scoping (catalog/schema/table)
4. Maintain user/group specifications
```

#### 3.3 Phase 3: Deny Rule Processing with Exclusions
```
1. Validate deny rules have entity scoping (CRITICAL)
2. Generate exclusion patterns for conflicting users/groups
3. Apply user_negated/group_negated flags
4. Skip global deny rules that would block everything
5. Implement conflict resolution logic
```

### 4. Evaluation Logic Transformation

#### 4.1 Original Sequential Logic
```
Rules processed top-to-bottom:
1. Allow user_x access to table_a    → MATCH → Allow + Stop
2. Deny user_x access to everything  → NOT EVALUATED
Result: user_x can access table_a only
```

#### 4.2 New OR Logic with Exclusions
```
All rules evaluated independently:
1. Allow user_x access to table_a    → Allow
2. Deny everything except user_x     → Deny (user_x excluded via user_negated)
Result: user_x can access table_a only (same outcome)
```

## Critical Implementation Details

### 1. Entity Scoping Validation

**CRITICAL SAFETY FEATURE**: The migration script enforces that deny rules MUST have explicit entity scoping:

```python
# Deny rules must specify at least one of:
catalog_specified = rule_data.get('catalog') and rule_data.get('catalog') != '*'
schema_specified = rule_data.get('schema') and rule_data.get('schema') != '*'  
table_specified = rule_data.get('table') and rule_data.get('table') != '*'

is_entity_scoped = catalog_specified or schema_specified or table_specified

if not is_entity_scoped:
    logger.warning("Skipping deny rule without entity scope")
    continue  # Prevents global deny rules
```

### 2. User/Group Exclusion Pattern Generation

Instead of complex negative lookahead regex, the script uses clean exclusion flags:

```yaml
# OLD APPROACH (complex regex)
user: "(?!^(user_a|user_b)$).*"

# NEW APPROACH (clean flags)  
user: "(user_a|user_b)"
user_negated: true
```

### 3. Malformed Rule Detection

The script automatically filters out incomplete rules:

```python
# Rules missing essential fields are skipped:
# - No user AND no group specified
# - No privileges field in allow rules
# - No entity scoping in deny rules
```

### 1. OPA Default Allow Configuration

**CRITICAL ISSUE**: OPA system configured with `default allow := true`

```rego
# From rules/main.rego line 14
default allow := true
```

#### Impact:
- **Without explicit deny rules**: Users get access to ALL data
- **If deny rules are skipped**: Massive security breach
- **Essential requirement**: ALL deny rules must be preserved

### 2. Rule Conflict Resolution

#### 2.1 Sequential vs OR Logic Conflicts
- **Sequential**: First match wins, later rules ignored
- **OR Logic**: All rules evaluated, conflicts must be resolved explicitly

#### 2.2 Common Conflict Pattern
```
Original Sequential:
1. Allow user_x specific tables  → user_x gets specific access
2. Deny user_x everything       → NOT evaluated (first match won)

OR Logic Problem:
1. Allow user_x specific tables  → user_x gets specific access
2. Deny user_x everything       → user_x gets denied (CONFLICT!)
```

#### 2.3 Resolution Strategy
Use negative regex patterns to exclude users/groups from deny rules:
```yaml
- type: table
  user: "(?!^(user_x)$).*"  # Excludes user_x from this deny rule
  action: deny
  privileges: [...]
```