# Data Access Policies - Zomato Analytics

**Last Updated:** May 28, 2025

## Overview

Welcome to the Zomato Analytics Data Access Policies repository! This project implements a robust, modular, and production-grade data access control system, primarily designed for Trino and adaptable for other data access tools. It leverages Open Policy Agent (OPA) to enforce fine-grained access controls based on declarative policies defined in Rego and configured through YAML files.

The core goal is to provide a centralized, auditable, and flexible way to manage who can access what data, under which conditions, across various tenants and data sources within Eternal's analytical ecosystem.

## Contributing
See [CONTRIBUTING](CONTRIBUTING.md) on how to contribute and alter configurations.

## Key Features

-   **Centralized Policy Management**: Define all data access rules in one place.
-   **Fine-Grained Control**: Supports user, group, and regex-based access control.
-   **Rich Policy Capabilities**:
    -   Catalog, schema, table, and column-level permissions.
    -   Row-Level Security (RLS).
    -   Column Masking.
    -   Procedure/Function privilege management.
    -   User Impersonation.
    -   System-level operations control.
-   **Multi-Tenancy**: Manage policies for different business units (tenants) like Zomato, Blinkit, Hyperpure.
-   **Cross-Tenant Authorization**: Securely manage data sharing and access across tenants.
-   **Dynamic & Attribute-Based Routing**: Policies can leverage user attributes (department, LOB, etc.) and request context.
-   **Extensible Configuration**: Policies are driven by easy-to-understand YAML rule definitions.
-   **Comprehensive Testing**: Includes a framework for testing policies and configurations.
-   **Performance Optimized**: Designed with OPA's partial evaluation capabilities in mind for efficient execution.

## System Architecture

The system uses OPA as the decision engine. When a data access request occurs (e.g., a Trino query), the calling application queries OPA. OPA evaluates the request against the loaded Rego policies and YAML configurations to make an allow/deny decision, or to return data like row filters or column masks.

```mermaid
flowchart TD
 subgraph subGraph0["Policy & Data Sources"]
    direction LR
        opal_server["OPAL Server"]
        github["GitHub: data-access-policies (YAML)"]
        opal_client["OPAL Client / OPA (Sidecar)"]
        rds["AWS RDS (MySQL)"]
  end
 subgraph subGraph2["Query Engines"]
    direction TB
        trino["Trino/Spark"]

  end
    github -- Static Policies & Data --> opal_server
    rds -- Dynamic Data --> opal_client
    opal_server -- Policy Diffs via Redis --> opal_client
    opal_server -- Policy Bundle --> opal_client
    trino -- Data Access Request <br> (Actor X, Action Y) --> opal_client
    opal_client -- Authorization Decision --> trino

     github:::dataSource
     opal_server:::opal
     rds:::dataSource
     opal_client:::opal
     trino:::queryEngine
    classDef default fill:#f9f9f9,stroke:#333,stroke-width:2px
    classDef queryEngine fill:#e6f3ff,stroke:#007bff,stroke-width:2px
    classDef dataSource fill:#fff0e6,stroke:#ff7f00,stroke-width:2px
    classDef opal fill:#e6ffe6,stroke:#009900,stroke-width:2px
```

## Policy Evaluation Flow

1.  **Request**: A user or service initiates an action (e.g., SQL query).
2.  **OPA Invocation**: The data platform (e.g., Trino authorizer plugin) sends an input JSON to OPA, describing the user, resource, action, and context.
3.  **Policy & Configuration Loading**: OPA uses the compiled bundle of Rego policies (`*.rego`) and YAML configuration files (`configurations/**/*.yaml`).
4.  **Rule Evaluation**:
    *   `rules/main.rego` is the primary entry point.
    *   It determines the applicable tenant(s) and global rules.
    *   `rules/core.rego` implements core logic for matching rules based on user, groups, entity types, etc.
    *   Tenant-specific rules, global rules, and user attribute-based rules are aggregated.
5.  **Decision**: OPA returns a decision (e.g., `allow: true/false`, row filter expressions, column mask expressions).
6.  **Enforcement**: The data platform enforces the decision.

```mermaid
graph LR
    A([Data Access Request]) -- Authenticated Request --> B{OPA Policy Evaluation};

    B -- Input: User, Resource, <br/> Action, Context --> C[Load Policies <br/> & Configurations];

    subgraph "Evaluation Steps"
        direction LR
        C --> D[Identify Tenant& Global Rules];
        D --> E[Aggregate  Applicable Rules];
        E --> F[Evaluate Core Logic <br/> ];
    end

    F -- Raw OPA Decision <br/> (Allow/Deny, Filters, Masks) --> G[Enforce Decision];
    G -- Action Taken --> H([Access Status: <br/> Granted/Denied/Modified]);

    %% Styling
    classDef startEnd fill:#E0E7FF,stroke:#4F46E5,stroke-width:2px,rx:10px,ry:10px,padding:10px;
    classDef decision fill:#FEF3C7,stroke:#F59E0B,stroke-width:2px,padding:10px;
    classDef process fill:#D1FAE5,stroke:#10B981,stroke-width:2px,padding:10px;
    classDef enforcement fill:#FEE2E2,stroke:#EF4444,stroke-width:2px,padding:10px;
    classDef subgraphStyle fill:#F3F4F6,stroke:#9CA3AF,stroke-width:1px,color:#1F2937;

    class A,H startEnd;
    class B decision;
    class C,D,E,F process;
    class G enforcement;
    classDef evalSteps fill:#F9FAFB,stroke:#D1D5DB,stroke-width:2px;
    class subgraphStyle evalSteps;
```

## Directory Structure

Understanding the directory structure is key to contributing:

-   `README.md`: This file.
-   `configurations/`: Contains all YAML-based policy configurations. **This is where most contributions will be made.**
    -   `global/`: Configurations that apply across all tenants.
        -   `rules/data.yaml`: Global policy rules (e.g., default catalog access, impersonation).
        -   `catalog_tenant_mapping/data.yaml`: Maps data catalogs to their respective tenants.
        -   `lob_tenant_mapping/data.yaml`: Maps Lines of Business (LOBs) to tenants.
        -   `sources/data.yaml`: Configuration for data sources.
        -   `tools/trinodb/`: Trino-specific global configurations.
            -   `operation_privilege_mapping/data.yaml`: Maps Trino operations to required privileges.
            -   `operations/data.yaml`: Lists Trino operations grouped by scope.
    -   `<tenant_name>/` (e.g., `zomato/`, `blinkit/`, `hyperpure/`): Tenant-specific configurations.
        -   `rules/data.yaml`: Policy rules specific to this tenant.
        -   `templates/data.yaml`: (Optional) Tenant-specific templates, e.g., for row-level filters.
-   `policies/`: Contains global policy logic (less frequently changed by general contributors).
    -   `global/main.rego`: Main Rego file for global policies.
-   `rules/`: Core Rego policy files. **Changes here require a deep understanding of OPA and Rego.**
    -   `main.rego`: Main entry point for policy evaluation, routes to tenant/global rules.
    -   `core.rego`: Core logic for matching rules, evaluating permissions.
    -   `constants.rego`: Defines constants used in policies.
    -   `user_attributes.rego`: Logic for handling user attribute-based rules.
    -   `README.md`: Detailed explanation of the Rego policy structure and logic.
-   `tests/`: Contains test inputs for policy validation.
-   `tools/`: Tool-specific policy logic and tests (e.g., for Trino).
    -   `trinodb/main.rego`: Main Rego entry point for Trino-specific authorization.
    -   `trinodb/tests/`: Comprehensive Rego tests for Trino operations.
-   `utils/`: Utility Rego files providing helper functions.
-   `test_policy.sh`: Script to run all OPA tests.
-   `build_bundle.sh`: Script to build the OPA bundle (`bundle.tar.gz`).


## Core Capabilities & Real-World Examples

This system can enforce a wide variety of access controls:

1.  **Catalog/Schema/Table Access Control**:
    *   **What**: Granting or denying permissions like `SELECT`, `INSERT`, `CREATE TABLE`, `ALTER SCHEMA` on specific data entities.
    *   **Example**: Allow the `marketing_analysts` group to `SELECT` from `zomato.analytics.customer_segments` table.
    ```yaml
    # In configurations/zomato/rules/data.yaml
    - type: table
      entity_category: data_entity
      group: "marketing_analysts"
      catalog: "zomato"
      schema: "analytics"
      table: "customer_segments"
      privileges: ["SELECT"]
    ```

2.  **Row-Level Security (RLS)**:
    *   **What**: Restricting users to see only specific rows in a table based on their attributes or context.
    *   **How**: Define a rule of `type: row_filter`. The policy engine returns an SQL `WHERE` clause fragment.
    *   **Example**: Sales representatives can only see customer data for their assigned region.
    ```mermaid
    graph TD
        UserQuery["User (Sales Rep - North) queries 'SELECT * FROM customers'"] --> OPA
        subgraph OPA Evaluation
            IdentifyUser["Identify User: 'sales_rep_A'"]
            GetUserAttrs["Get User Attributes: {region: 'North'}"]
            FindMatchingRule["Find RLS Rule for 'customers' table, group 'sales_reps'"]
            GenerateFilter["Generate Filter: 'region_column = 'North''"]
        end
        OPA -- Filter Expression --> Trino
        Trino -- Injects Row Filter --> ModifiedQuery["SELECT * FROM customers WHERE region_column = 'North'"]
        ModifiedQuery --> FilteredData["Return only North region customers"]
    ```
    ```yaml
    # In configurations/zomato/rules/data.yaml
    - type: row_filter
      entity_category: data_entity
      group: "sales_reps" # Applies to all users in this group
      catalog: "sales_db"
      schema: "public"
      table: "customers"
      # 'filter_expression' uses user attributes available in OPA's input.
      # Assuming input.user_attributes.region contains the user's region.
      filter_expression: "region_column = '${user_attributes.region}'"
      allow: true
    ```

3.  **Column Masking**:
    *   **What**: Obscuring sensitive data in columns (e.g., hashing emails, showing only last 4 digits of a phone number).
    *   **How**: Define a rule of `type: column_mask`. The policy engine returns an SQL expression to transform the column.
    *   **Example**: Non-HR users see a masked version of PII columns in the `employees` table.
    ```yaml
    # In configurations/global/rules/data.yaml (or tenant-specific)
    - type: column_mask
      entity_category: data_entity
      group: "analysts" # Exclude HR group to see actual data
      catalog: "hr_data"
      schema: "sensitive"
      table: "employees"
      column: "email_address"
      # 'masking_expression' uses the column name itself (represented by ${column_name})
      # and SQL functions available in Trino.
      masking_expression: "regexp_replace(${column_name}, '^.*@', '****@')" # Shows only domain
      allow: true
    - type: column_mask
      entity_category: data_entity
      group: "analysts"
      catalog: "hr_data"
      schema: "sensitive"
      table: "employees"
      column: "phone_number"
      masking_expression: "'XXX-XXX-' || substr(${column_name}, -4)" # Shows last 4 digits
      allow: true
    ```

4.  **User Impersonation**:
    *   **What**: Allowing specific users or service accounts to execute queries as another user.
    *   **Example**: A BI service account `bi_redash` can run queries on behalf of end-users.
    ```yaml
    # In configurations/global/rules/data.yaml
    - type: impersonation
      entity_category: query_engine_entity
      original_user: "bi_redash" # The user attempting to impersonate
      new_user: "(^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+)" # Regex for allowed target users
      allow: true
    ```

5.  **Function/Procedure Access**:
    *   **What**: Controlling who can execute specific functions or stored procedures.
    *   **Example**: Allow `data_engineers` group to execute procedures in the `utils` schema.
    ```yaml
    # In configurations/zomato/rules/data.yaml
    - type: function # 'procedure' is often treated as 'function' type
      entity_category: query_engine_entity
      group: "data_engineers"
      catalog: "utility_catalog"
      schema: "utils"
      procedure: "calculate_aggregates" # Name of the procedure/function
      privileges: ["EXECUTE"]
    ```

6.  **Cross-Tenant Data Sharing**:
    *   **What**: Allowing users from one tenant (e.g., Zomato) to access specific resources in another tenant (e.g., Blinkit), under strict controls.
    *   **How**: Rules in the resource-owning tenant's configuration can specify a `source_tenant` attribute.
    *   **Example**: Allow Zomato's central analytics team to query specific Blinkit tables.
    ```yaml
    # In configurations/blinkit/rules/data.yaml
    - type: table
      entity_category: data_entity
      # This rule applies to users from 'zomato' tenant when they query Blinkit resources.
      # The user's actual group membership within Zomato would be evaluated.
      group: "zomato_central_analytics_team" # Group name as defined in Zomato's identity system
      catalog: "blinkit_mart"
      schema: "sales"
      table: "daily_summary"
      allow: ["SELECT"]
      tenant: ["zomato"] # Specifies that users from 'zomato' tenant can be allowed by this rule
                         # This 'tenant' field in a rule usually refers to the source tenant of the user.
                         # The file being in 'blinkit' config implies the resource is in Blinkit.
    ```
    *Note: The exact mechanism for cross-tenant (source_tenant vs. rule's tenant field) depends on `rules/main.rego` logic.*

## Advanced Features & Patterns

-   **Filter Templates**: Reusable SQL snippets for row-level security, defined in `configurations/<tenant>/templates/data.yaml` and referenced by name in rules. This promotes consistency and simplifies rule writing.
-   **Regex-Based Matching**: Usernames, group names, catalog/schema/table names in rules can often be specified as regular expressions for flexible matching. We follow [Google's RE 2 Flavour](https://github.com/google/re2/wiki/Syntax) for Regex Syntax (Please note some features such as negative lookahead isn't available).
-   **Environmental Context**: Policies can leverage contextual information passed in the OPA input, such as Trino version (`input.context.softwareStack.trinoVersion`), client tags, etc., to make dynamic decisions.

## Testing Framework

A comprehensive testing strategy is crucial for maintaining the integrity of access policies.

### How to Run Tests

Execute the `test_policy.sh` script from the root of the repository:
```bash
./test_policy.sh
```
This script runs all `*_test.rego` files using `opa test`. It will show detailed output, including which tests passed or failed.

### Test Organization

-   Tests for Trino-specific operations are located in `tools/trinodb/tests/`. Each file typically focuses on a category of operations (e.g., `catalog_operations_test.rego`, `row_filters_test.rego`).
-   More general policy logic tests can be found in `rules/tests/`.

### Writing New Tests

When you add or change a configuration, you **must** add or update tests.
1.  Create a new file (e.g., `tools/trinodb/tests/my_new_feature_test.rego`) or add to an existing relevant test file.
2.  Import necessary modules:
    ```rego
    package tools.trinodb.tests.my_new_feature_test # Or appropriate package

    import data.tools.trinodb.main # Main entry point for Trino policies
    import rego.v1
    ```
3.  Define test rules (functions starting with `test_`):
    ```rego
    test_my_feature_allows_access_for_group_x if {
        # 1. Define the input context for the OPA query
        request_context := {
            "is_test": true, # Flag for test-specific behavior if any
            "user": {"id": "<EMAIL>", "groups": ["group_x"]},
            "action": {
                "operation": "SelectFromColumns", # Trino operation
                "resource": {
                    "entity_type": "table",
                    "catalog_name": "my_catalog",
                    "schema_name": "my_schema",
                    "table_name": "my_table",
                    "columns": ["col1", "col2"]
                }
            },
            "context": {
                "softwareStack": {"trinoVersion": "4xx"}
                # Add other relevant context
            }
        }

        # 2. Define mock configurations specific to this test case
        # This allows testing your rule in isolation.
        mock_rules_config := [
            {
                "type": "table", "entity_category": "data_entity", "group": "group_x",
                "catalog": "my_catalog", "schema": "my_schema", "table": "my_table",
                "allow": ["SELECT"]
            }
            # Add other necessary mock rules or mappings
        ]

        # 3. Call the policy decision point with the input and mock data
        # 'main.allow' is a common decision point for Trino access.
        # Use 'with data.configurations.mock_rules as ...' to inject your test-specific rules.
        # You might need to mock other data paths like catalog_tenant_mapping, etc.
        result := main.allow with input as request_context
                           with data.configurations.global.rules as mock_rules_config
                           # If your rule is tenant-specific, mock its path:
                           # with data.configurations.my_tenant.rules as mock_tenant_rules_config

        # 4. Assert the expected outcome
        result == true
    }

    test_my_feature_denies_access_for_group_y if {
        request_context := { /* ... similar input, but for group_y ... */ }
        mock_rules_config := [ /* ... */ ]

        result := main.allow with input as request_context
                           with data.configurations.global.rules as mock_rules_config
        result == false
    }
    ```
4.  Refer to existing test files for more complex examples, especially for row filters and column masks where you assert the expression content.

## Policy Writing Best Practices

-   **Clarity and Simplicity**: Write rules that are easy to understand and maintain.
-   **Specificity**: Be as specific as possible with user/group and resource matching to avoid overly permissive rules.
-   **Use Regex Wisely**: While powerful, complex regex can be hard to debug and impact performance. Prefer exact matches where possible.
-   **Comments**: Add comments to your YAML configurations and Rego (if modifying) to explain the purpose of rules, especially complex ones.
-   **Thorough Testing**: Every rule change should be accompanied by tests covering allow and deny scenarios.
-   **Least Privilege**: Grant only the necessary permissions required for a user or group to perform their tasks.
-   **Regular Review**: Periodically review existing rules to ensure they are still relevant and appropriate.

## Support & Documentation

-   For detailed information on the Rego policy logic, evaluation flow, and advanced policy development, refer to `rules/README.md`.
-   If you have questions or need assistance, please reach out to the repository maintainers or the Zomato Analytics platform team.

---

Thank you for contributing to a secure and well-governed data ecosystem at Eternal!

