package ancillary.policies.global.main

import data.configurations.global.catalog_tenant_mapping as catalog_tenant_mapping
import data.configurations.global.lob_tenant_mapping as lob_tenant_mapping
import data.utils.constants

import rego.v1

line_of_business := object.get(input, ["context", "line_of_business"], "all")

# Given input.lob, return deduplicated set of catalogs belonging to tenants for the lob.

# METADATA
# description: Common entrypoint for all catalogs fetch
# entrypoint: true
catalogs_for_lob contains some_catalog if {
	line_of_business == "all"
	some catalog
	catalog_tenant_mapping[catalog] != null
	not catalog in constants.catalogs_to_skip
	some_catalog := catalog
}

catalogs_for_lob contains some_catalog if {
	line_of_business != "all"
	tenants := lob_tenant_mapping[line_of_business]
	tenants != null

	some catalog
	catalog_tenants := catalog_tenant_mapping[catalog]
	catalog_tenants != null
	not catalog in constants.catalogs_to_skip

	# At least one tenant in catalog_tenants matches a LOB tenant
	catalog_tenants[_] == tenants[_]
	some_catalog := catalog
}
