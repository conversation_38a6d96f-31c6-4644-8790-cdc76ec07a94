package ancillary.tests.ancillary

import data.ancillary.policies.global.main
import rego.v1

test_catalogs_fetch_for_all_lob if {
	request_context := {"context": {"line_of_business": "all"}}
	catalogs = main.catalogs_for_lob with input as request_context
		with data.configurations.global.catalog_tenant_mapping as {
			"zomato": ["blinkit"],
			"zomato_iceberg": [
				"zomato",
				"hyperpure",
			],
			"system": [
				"blinkit",
				"zomato",
				"district",
				"hyperpure",
				"feedingindia",
				"nugget",
			],
		}
		with data.configurations.global.lob_tenant_mapping as {
			"Quick Commerce": ["blinkit"],
			"Zomato + Blinkit Corporate": [
				"zomato",
				"blinkit",
			],
			"Eternal Corporate": [
				"blinkit",
				"zomato",
				"district",
				"feedingindia",
				"hyperpure",
			],
		}
	catalogs == {"zomato", "zomato_iceberg"}
}

test_catalogs_fetch_for_quick_commerce if {
	request_context := {"context": {"line_of_business": "Quick Commerce"}}
	catalogs = main.catalogs_for_lob with input as request_context
		with data.configurations.global.catalog_tenant_mapping as {
			"zomato": ["blinkit"],
			"zomato_iceberg": [
				"zomato",
				"hyperpure",
			],
			"system": [
				"blinkit",
				"zomato",
				"district",
				"hyperpure",
				"feedingindia",
				"nugget",
			],
		}
		with data.configurations.global.lob_tenant_mapping as {
			"Quick Commerce": ["blinkit"],
			"Zomato + Blinkit Corporate": [
				"zomato",
				"blinkit",
			],
			"Eternal Corporate": [
				"blinkit",
				"zomato",
				"district",
				"feedingindia",
				"hyperpure",
			],
		}
	catalogs == {"zomato"}
}
