- catalog: hive
  schema: hyperpure_etls
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: hive
  schema: hpexperiments
  max_lease: 3m
  primary_owner: aniket.kuk<PERSON><EMAIL>
  owners:
    - <EMAIL>
    
- catalog: hive
  schema: (hp_wms.*|hp_pod.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: hive
  schema: (hp_users.*|hp_consumer.*|hp_tickets.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>
    
- catalog: hive
  schema: hp_sales.*
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: hive
  schema: hp_payments.*
  max_lease: 1w
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: hive
  schema: hyperpure_etls.*
  table: (hp_product_margins.*)
  max_lease: 1w
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: hive
  schema: dynamodb.*
  table: (hp.*|prod_hp.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: hive
  schema: insights_etls.*
  table: (hp_.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: hive
  schema: jumbo2.*
  table: (hyperpure_.*|hp_.*|cart_updates|cart_updates_v2|search_tracking|autosuggest_tracking|bin_inventory_updates|cart_tracking|pageview_tracking|po_grn_mapping_events|product_inventory_updates|product_warehouse_price_mapping_events|purchase_order_events|stock_transfer_order_events)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: hive
  schema: jumbo_dashboard.*
  table: (hp_.*|hyblink_.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: hive
  schema: jumbo_derived.*
  table: (hp_.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>
# Remove all rules above this, interim rules


# Platform specifc
- catalog: .*
  schema: (.*dynamodb.*|.*mongo.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>

- catalog: .*
  schema: (access_controller|airflow|.*logs.*|archiver|arroyo)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>

# Billing Specific
- catalog: .*
  schema: (.*billing.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>

- catalog: .*
  schema: .*
  table: (.*billing.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>

# Zomato Specific

- catalog: zomato
  schema: (.*etls.*|jumbo_dashboard|jumbo_derived|jumbo_external)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>


- catalog: .*
  schema: (zcd_etls)
  max_lease: 3m
  priority: 1
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

# Hyperpure Specific
- catalog: zomato
  schema: hyperpure_etls
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: zomato
  schema: hpexperiments
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>
    
- catalog: zomato
  schema: (hp_wms.*|hp_pod.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: zomato
  schema: (hp_users.*|hp_consumer.*|hp_tickets.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>
    
- catalog: zomato
  schema: hp_sales.*
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: zomato
  schema: hp_payments.*
  max_lease: 1w
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: zomato
  schema: hyperpure_etls.*
  table: (hp_product_margins.*)
  max_lease: 1w
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: zomato
  schema: dynamodb.*
  table: (hp.*|prod_hp.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: zomato
  schema: insights_etls.*
  table: (hp_.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: zomato
  schema: jumbo2.*
  table: (hyperpure_.*|hp_.*|cart_updates|cart_updates_v2|search_tracking|autosuggest_tracking|bin_inventory_updates|cart_tracking|pageview_tracking|po_grn_mapping_events|product_inventory_updates|product_warehouse_price_mapping_events|purchase_order_events|stock_transfer_order_events)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: zomato
  schema: jumbo_dashboard.*
  table: (hp_.*|hyblink_.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

- catalog: zomato
  schema: jumbo_derived.*
  table: (hp_.*)
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>

# Blinkit Specific dwh approvers
- catalog: blinkit.*
  max_lease: 3m
  primary_owner: <EMAIL>
  owners:
    - <EMAIL>
    - <EMAIL>
