package approvers.policies.global.main

import data.utils.custom_regex as utils
import data.utils.generic

catalog := object.get(input, ["context", "catalog"], null)
schema := object.get(input, ["context", "schema"], null)
table := object.get(input, ["context", "table"], null)

# Collect all matching rules at table level
table_level_rules := {rule |
	some rule in data.approvers.configurations.rules
	utils.match_entire(rule.catalog, catalog)
	object.get(rule, "schema", null) != null
	utils.match_entire(rule.schema, schema)
	object.get(rule, "table", null) != null
	utils.match_entire(rule.table, table)
}

# Collect all matching rules at schema level
schema_level_rules := {rule |
	some rule in data.approvers.configurations.rules
	utils.match_entire(rule.catalog, catalog)
	object.get(rule, "schema", null) != null
	utils.match_entire(rule.schema, schema)
	object.get(rule, "table", null) == null
}

# Collect all matching rules at catalog level
catalog_level_rules := {rule |
	some rule in data.approvers.configurations.rules
	utils.match_entire(rule.catalog, catalog)
	object.get(rule, "schema", null) == null
	object.get(rule, "table", null) == null
}

approvers_for_request := res if {
	# Table-level: requires match on all (catalog+schema+table)
	count(table_level_rules) > 0
	priority_rules := generic.fetch_priority_rule(table_level_rules)
	count(priority_rules) > 0
	some rule in priority_rules
	res := {
		"primary_owner": rule.primary_owner,
		"owners": rule.owners,
		"lease_time": rule.max_lease,
		"level": "table",
	}
} else := res if {
	# Schema-level: match catalog + schema, no table provided
	count(schema_level_rules) > 0
	priority_rules := generic.fetch_priority_rule(schema_level_rules)
	count(priority_rules) > 0
	some rule in priority_rules
	res := {
		"primary_owner": rule.primary_owner,
		"owners": rule.owners,
		"lease_time": rule.max_lease,
		"level": "schema",
	}
} else := res if {
	# Catalog-level: match only on catalog
	count(catalog_level_rules) > 0
	priority_rules := generic.fetch_priority_rule(catalog_level_rules)
	count(priority_rules) > 0
	some rule in priority_rules
	res := {
		"primary_owner": rule.primary_owner,
		"owners": rule.owners,
		"lease_time": rule.max_lease,
		"level": "catalog",
	}
}

default approvers_for_request := {}
