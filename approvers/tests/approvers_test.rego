package approvers.tests.approvers

import data.approvers.policies.global.main
import rego.v1

test_approvers_for_catalog_fallback if {
	request_context := {"context": {
		"catalog": "district",
		"schema": "random_schema",
		"table": "dining_orders",
	}}
	approvers = main.approvers_for_request with input as request_context
		with data.approvers.configurations.rules as [
			{
				"catalog": "district",
				"max_lease": "1d",
				"primary_owner": "<EMAIL>",
				"owners": ["<EMAIL>"],
			},
			{
				"catalog": "district",
				"schema": "district_etls",
				"max_lease": "3d",
				"primary_owner": "<EMAIL>",
				"owners": ["<EMAIL>"],
			},
		]
	approvers == {
		"lease_time": "1d",
		"level": "catalog",
		"owners": ["<EMAIL>"],
		"primary_owner": "<EMAIL>",
	}
}

test_approvers_for_schema_matching if {
	request_context := {"context": {
		"catalog": "district",
		"schema": "district_etls",
		"table": "dining_orders",
	}}
	approvers = main.approvers_for_request with input as request_context
		with data.approvers.configurations.rules as [
			{
				"catalog": "district",
				"max_lease": "1d",
				"primary_owner": "<EMAIL>",
				"owners": ["<EMAIL>"],
			},
			{
				"catalog": "district",
				"schema": "district_etls",
				"max_lease": "3d",
				"primary_owner": "<EMAIL>",
				"owners": ["<EMAIL>"],
			},
		]
	approvers == {
		"lease_time": "3d",
		"level": "schema",
		"owners": ["<EMAIL>"],
		"primary_owner": "<EMAIL>",
	}
}

test_approvers_for_table_regex_match if {
	request_context := {"context": {
		"catalog": "district",
		"schema": "district_etls",
		"table": "dining_orders",
	}}
	approvers = main.approvers_for_request with input as request_context
		with data.approvers.configurations.rules as [
			{
				"catalog": "district",
				"max_lease": "1d",
				"primary_owner": "<EMAIL>",
				"owners": ["<EMAIL>"],
			},
			{
				"catalog": "district",
				"schema": "district_etls",
				"table": "dining.*",
				"max_lease": "3d",
				"primary_owner": "<EMAIL>",
				"owners": ["<EMAIL>"],
			},
		]
	approvers == {
		"lease_time": "3d",
		"level": "table",
		"owners": ["<EMAIL>"],
		"primary_owner": "<EMAIL>",
	}
}

test_approvers_priority_rule_match if {
	request_context := {"context": {
		"catalog": "district",
		"schema": "district_etls",
		"table": "dining_orders",
	}}
	approvers = main.approvers_for_request with input as request_context
		with data.approvers.configurations.rules as [
			{
				"catalog": "district",
				"schema": "district_etls",
				"table": "dining_orders",
				"max_lease": "1d",
				"primary_owner": "<EMAIL>",
				"owners": ["<EMAIL>"],
			},
			{
				"catalog": "district",
				"schema": "district_etls",
				"table": "dining.*",
				"max_lease": "3d",
				"priority": 1,
				"primary_owner": "<EMAIL>",
				"owners": ["<EMAIL>"],
			},
		]
	approvers == {
		"lease_time": "3d",
		"level": "table",
		"owners": ["<EMAIL>"],
		"primary_owner": "<EMAIL>",
	}
}
