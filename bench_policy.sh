#!/bin/bash
# bench_policy.sh - Benchmark OPA policy evaluation
set -e

INPUT=${1:-input.json}
POLICY_DIR=tools/trinodb

echo "Benchmarking OPA policy (single eval)..."
echo opa eval --format=pretty  --bundle ./ --input $INPUT 'data.tools.trinodb.main.allow'
opa eval --format=pretty --bundle ./ --input $INPUT 'data.tools.trinodb.main.allow'

echo "Running OPA bench (microbenchmark)..."
opa bench --bundle ./ --input $INPUT 'data.tools.trinodb.main.allow'

echo "Running OPA eval with detailed metrics..."
opa eval --format=pretty --bundle ./ --input $INPUT --metrics 'data.tools.trinodb.main.allow'

echo "Running OPA eval with performance explanation..."
opa eval --format=pretty --bundle ./ --input $INPUT --explain=notes 'data.tools.trinodb.main.allow'
