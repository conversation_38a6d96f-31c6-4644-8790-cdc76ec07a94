- type: catalog
  entity_category: data_entity
  allow: all
- type: schema
  entity_category: data_entity
  catalog: .*staging
  owner: true
- type: schema
  entity_category: data_entity
  user: (dwh_admin)
  catalog: (blinkit.*)
  owner: true
- type: table
  entity_category: data_entity
  user: jumbo-admin
  privileges:
  - DELETE
  - GRANT_SELECT
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  group: pseudo_admin
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: carbon_tech
  group: greening_india
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (feedingindia|feedingindia_stag)
  schema: (feedingindiadfpdb|figrandcentraldb|feedingindiaweb|dwh_etls|figrandcentraldb_cdc)
  group: feedingindia
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (feedingindia|feedingindia_stag)
  schema: fin_etls
  group: fi_finance
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (feedingindia|feedingindia_stag)
  schema: (jumbo_playground|staging|.*_etls)
  user: (<EMAIL>)
  privileges:
  - DELETE
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: (feedingindia|feedingindia_stag)
  user: (<EMAIL>|zdp-common-python-etls)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: access_controller
  table: zoho_mapping
  group: access_controller_admin
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh)
  table: (fact_sales_order_item_details)
  user: (<EMAIL>)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blackhole)
  privileges:
  - DELETE
  - GRANT_SELECT
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: blinkit.*staging
  group: (blinkit.*)
  privileges:
  - DELETE
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  user: (dwh_admin)
  privileges:
  - DELETE
  - GRANT_SELECT
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: lake_(payments|auth|logistics|grofers_db|crm|cms|oms_bifrost|user|locus_v2)
  table: (gr_user_merchant|logistics_dark_store_projection|oms_cart_address|auth_block_phone|gr_coordinate|contact_synced|gr_address|contact_synchronizer|gr_merchant|gr_order_address|logistics_task_call_detail|gr_location_tracking|logistics_address|gr_support_user_response|gr_locality|oms_cart|pre_auth_wallet|auth_block_phone)
  user: (common_airflow|etl_airflow|blinkit_replenishment|cdp_application)
  privileges:
  - GRANT_SELECT
  - SELECT
  columns:
  - name: facebook_access_token
    allow: true
    mask: zsha1(facebook_access_token)
  - name: phone_raw
    allow: true
    mask: zsha1(phone_raw)
  - name: password
    allow: true
    mask: zsha1(password)
  - name: google_access_token
    allow: true
    mask: zsha1(google_access_token)
  - name: formatted_address
    allow: true
    mask: zsha1(formatted_address)
  - name: secret_key
    allow: true
    mask: zsha1(secret_key)
  - name: wallet_auth_token
    allow: true
    mask: zsha1(wallet_auth_token)
  - name: phone_number
    allow: true
    mask: zsha1(phone_number)
  - name: customer_phone
    allow: true
    mask: zsha1(customer_phone)
  - name: email_id
    allow: true
    mask: zsha1(email_id)
  - name: sms_number
    allow: true
    mask: zsha1(sms_number)
  - name: merchant_password
    allow: true
    mask: zsha1(merchant_password)
  - name: phone
    allow: true
    mask: zsha1(phone)
  - name: email
    allow: true
    mask: zsha1(email)
  - name: device_token
    allow: true
    mask: zsha1(device_token)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (.*_etls|dwh|interim|lake_events|playground|viz|segment_computed_traits|dwh_hpl)
  user: (etl_airflow)
  privileges:
  - DELETE
  - GRANT_SELECT
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  user: (etl_airflow|cdp_application)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (pricing_v3|lake_pricing_v3|oms_bifrost|lake_oms_bifrost)
  table: (pricing_domain_productchangelog|oms_order_item)
  group: (blinkit_sensitive_access)
  columns: []
  privileges:
  - GRANT_SELECT
  - SELECT
  priority: 1
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (dwh|category_etls|supply_etls|ars_etls|storeops_etls|ba_etls|logistics_data_etls|ds_etls|seller_etls|serviceability_etls|lake_events|lake_cms|cms|lake_vms|vms|lake_po|po|lake_retail|retail|lake_vendor_console|vendor_console|crates|lake_crates|lake_ars|ars|lake_ims|ims|lake_pos|pos|lake_rpc|rpc|lake_warehouse_location|warehouse_location|lake_dse_datakart|dse_datakart|lake_transit_server|transit_server|lake_storeops|storeops|lake_wms|wms|lake_seller|seller|interim)
  user: (blinkit_backend_application)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (serviceability|lake_serviceability|oms_bifrost|lake_oms_bifrost|trade_finance|lake_trade_finance)
  table: (ser_store_polygons|oms_cart|oms_cart_address|biz_txn|biz_txn_line)
  user: (blinkit_backend_application)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (pricing_v3|lake_pricing_v3)
  table: (rule_management_masterrule|rule_management_attributemasterrule|pricing_domain_superstore|pricing_domain_productchangelog|pricing_domain_prices|pricing_domain_city|pricing_domain_product|attribute_management_attributemasterrule)
  user: (blinkit_backend_application)
  privileges:
  - SELECT
- type: table 
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh|oms_bifrost|pricing_v3|ims|pos|rpc)
  table: (fact_sales_order_details|fact_sales_order_details_ext|fact_sales_order_item_details|fact_sales_order_item_details_ext|fact_sales_invoice_item_details|agg_daily_brand_item_product_sales|logs_weighted_landing_price|business_performance_daily_consumer_metrics|business_performance_daily_conversion_metrics|oms_order|oms_suborder|oms_order_item|oms_suborder_item|oms_cart|oms_cart_address|pricing_domain_productchangelog|pricing_domain_product|ims_inventory|ims_inventory_log|ims_inventory_stock_tax|ims_inventory_stock_details|pos_invoice|pos_invoice_product_details|pos_invoice_summary|product_tax)
  group: blinkit_non_sensitive_access
  privileges: []
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (dynamodb)
  table: (blinkit_store_inventory_service_oi_rt_view|blinkit_store_inventory_service_oi_snapshot|inventory_service)
  user: (blinkit_backend_application)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_wms|hp_wms_cdc)
  table: (bin|bin_inventory_logs)
  user: (blinkit_backend_application)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (interim|playground)
  user: (common_airflow|blinkit_replenishment)
  privileges:
  - DELETE
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (.*_etls|dwh|lake_events|viz|segment_computed_traits|dwh_hpl)
  user: (common_airflow)
  privileges:
  - DELETE
  - INSERT
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (ars_etls)
  user: (blinkit_replenishment)
  privileges:
  - DELETE
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh|dwh_hpl|bistro_etls|fin_etls)
  table: (fin_db_daily_margin_ss_rg|fin_daily_item_outlet_revenue_metrics|fin_daily_item_revenue_metrics|fact_sales_order_details_bistro|fact_sales_order_item_details_bistro|order_campaign_attribution|user_properties_lifetime_base_bistro|user_properties_daily_base_bistro|fact_sales_order_item_details_hpl|fact_sales_order_details_hpl|business_performance_daily_consumer_metrics|fact_sales_order_item_details|fact_sales_order_details|metrics_supply_chain_view_board|user_properties_lifetime_base|user_properties_daily_base|agg_product_sales_detail)
  group: (blinkit_sensitive_access)
  columns: []
  priority: 1
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (ba_etls|category_etls|consumer_etls|consumer_intelligence_etls|ds_etls|marketing_etls|plp_etls|supply_etls)
  table: (sla_orders|sla_outlet_losses|tableau_sales_wow|monthly_gmv_bucket_carts|jan_festive_sales|dod_nu_conversion|bistro_kitchen_kpt_data|bistro_kitchen_station_data|bistro_order_eta_data|bistro_nu_dau_wau_mau_metrics|search_ranking_personalisation_metrics|ci_widget_metrics_v2|pdp_performance|pdp_rails_performance_overall_level|npr_overall_performance|tiyc_overall_performance|tiyc_keyterm_performance|tiyc_keyterm_city_performance|tiyc_widget_position|good_customer_classification_v1|order_campaign_attribution|bank_offers|chritmas_audience|plp_merchant_product_sorting_rm_score_new|fnv_city_item_data|fnv_daily_metrics|city_assortment_ranking|city_assortment_ranking_log|ds_b_order_data_hex_mapping_hex_view|ds_enriched_dau_data_hex_view)
  priority: 1
  group: (blinkit_sensitive_access)
  columns: []
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  user: (blinkit_superset)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg_staging|blinkit_staging)
  user: (blinkit_adhoc_testing)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (lake_b2b_oms|b2b_oms)
  table: (invoice|invoice_item)
  group: (blinkit_b2b_finance_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (dwh)
  table: (superset_redash_access_map|category_superset_redash_access_map|fnv_city_superset_redash_access_map)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (storeops_etls|logistics_data_etls|locus_v2|lake_locus_v2|supply_etls)
  group: (blinkit_store_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (dwh|serviceability_etls|supply_etls|ba_etls|consumer_etls|lake_serviceability|serviceability|lake_ars|ars|lake_ims|ims|lake_ops_management|ops_management|lake_retail|retail|lake_rpc|rpc|lake_wms|wms|lake_transit_server|transit_server|lake_storeops|storeops|lake_po|po|lake_pos|pos|cms|lake_cms|ds_etls|cd_etls|lake_logistics|logistics|lake_oms_bifrost|oms_bifrost|lake_location|location|lake_inventory_lm|inventory_lm)
  table: (dim_keywords_ptype_mapping|fact_supply_order_partner_details|fact_supply_order_picker_details|agg_daily_search_keyword_conversion_metrics|agg_daily_consumer_conversion_details|agg_daily_diff_loss|agg_daily_lost_order_metrics|agg_hourly_outlet_role_instore_employee_login|bl_calendar_events_holidays|bl_city_state_mapping|city_zone_mapping|dim_item_product_offer_mapping|dim_item_variant_mapping|dim_merchant|dim_merchant_outlet_facility_mapping|dim_outlet|dim_product|e3_store_go_live_planning_live_stores_master|fact_inventory_transfer_details|fact_invoice_item_details|fact_sales_invoice_item_details|fact_sales_order_details|fact_sales_order_item_details|fact_supply_chain_order_details|flat_invoice_consignment_details|flat_invoice_item_billed_details|flat_invoice_item_dn_details|flat_invoice_item_grn_details|flat_invoice_item_rsto_details|flat_order_item_returns|flat_sto_item_details|imd_logistics_3pl_data|logs_weighted_landing_price|sm_and_manager_contact_details|block_reasons_hourly|btpo_merchant_level|btpo_rider_events_v2|serviceability_block_reason|surge_seen_hourly|city_hourly_weighted_availability|city_hourly_weighted_availability_logs|city_item_weights|fleet_master_data|fleet_metrics|fleet_trips|fnv_milk_perishables_dump_raw|fnv_outlet_item_data|fresh_warehouse_store_details|hourly_inventory_snapshots|hourly_storage_util|item_details|item_factor|outlet_details|store_hourly_weighted_availability|store_hourly_weighted_availability_logs|dn_b2b_creations|item_details|outlet_details|transfer_loss_v3|thirty_mins_orders|ser_disruption_request|ser_disruption_schedule|disruption_v2|disruption_v2_log|physical_facility_storage_capacity|physical_facility_storage_capacity_log|ims_bad_inventory_update_log|ims_inventory|ims_inventory_landing_price|ims_inventory_log|ims_item_inventory|ims_item_inventory_log|break_time|business_request|business_request_approval|candidate_profile|ops_management_transaction_logs|overtime|payouts_ledger|roster_planner|roster_suggestion|shift|shift_roles|slot_bookings|slots|task_management_task|training|training_challenge|training_level|training_level_mapping|training_user_progress|user|user_management_userreferral|user_management_userrole|user_management_usersites|console_location|console_outlet|console_outlet_logistic_mapping|warehouse_facility|warehouse_outlet_mapping|item_category_details|item_details|item_product_mapping|product_category|product_facility_master_assortment|product_product|dispatch_consignment|dispatch_container|outbound_billing_item|outbound_container|outbound_invoice|outbound_item|reverse_consignment|reverse_consignment_container|reverse_consignment_document|reverse_consignment_item|reverse_trip|reverse_trip_log|shipments_shipmentallocationstatelog|transit_consignment|transit_consignment_container|transit_consignment_document|transit_consignment_item|transit_consignment_state_logs|transit_fleet_plan|transit_node|transit_projection_consignment|transit_projection_trip|transit_projection_trip_duration|transit_projection_trip_event_timeline|transit_trip_metadata|activity|discrepancy_logs|er|er_container|er_container_item|erline|inventory_update_log|item_activity|item_details|location|location_inventory|order_complaint|site|user_details|invoice|invoice_event_log|invoice_payment_details|po_grn|po_item_inbound|po_schedule|purchase_order|sto|sto_items|sto_state|sto_state_transition_log|sto_type|discrepancy_note|discrepancy_note_product_detail|discrepancy_reason|pos_invoice|pos_invoice_payment_details|pos_invoice_po|pos_invoice_product_details|pos_invoice_product_lp|pos_invoice_type|gr_product_category_mapping|ser_store_polygons|job_run|physical_facility_outlet_mapping|ds_poi_polygons|ds_poi_polygons_all|ds_poi_manually_created|master_cancel_table|inventory_metrics_purchase_mis|oms_order|logistics_shipment_label_metadata|item_outlet_tag_mapping|console_state|warehouse_user|put_list|item_exceptions|job_application|job_vacancy|referral_leads|return_loss_real_time|festival_city_hourly_forecasting_data_v3|employee_tenant_map|agg_hourly_consumer_conversion_details|ims_inventory_update_type|gr_product|inventory_dump_rough|ims_bad_update_reason|flat_order_item_refunds|flat_cancelled_order_item_unused_refunds|selfie_verification_attempts|slot_level_optim_results_v9_1_v2|oms_order_event|product_brand|user_zone|ams_cluster|layout_component|outlet_layout|input_metrics_merchant_daily|consignment_metrics)
  group: (blinkit_store_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit)
  schema: (category_etls|viz)
  table: (city_state_zone_mapping|dwh_daily_ptype_inventory_sales_data)
  group: (blinkit_store_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit)
  schema: (ds_etls)
  table: (store_ops_roster_hourly)
  group: (blinkit_store_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato)
  schema: (dynamodb|jumbo_derived|carthero_prod|accounting_production|driver_service|mongo_driver_onboarding_service_production|blinkit_jumbo2)
  table: (blinkit_ops_user_onboarding_service|blinkit_inventory_service_location|delivery_drivers_dt|delivery_driver_service_mappings|driver_store_mappings|driver_tag_mappings|driver_tags|users|localities|zones|cities|mongo_v3_onboarding_applications|storeops_app_events|store_onboarding_app_events|taskmanagement_service_events|delivery_drivers|unified_ticket_events)
  group: (blinkit_store_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato)
  schema: (external_chat|jumbo2)
  table: (channels|unified_external_ticket_events)
  group: (blinkit_store_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (ops_management|lake_ops_management|dwh)
  table: (dim_keywords_l0_mapping|break_time|business_request|business_request_approval|cash_discrepancy_logs|cash_task|cash_transaction_logs|consolidated_shift|external_event|leave_management|master_site|notification_client|ops_management_transaction_logs|overtime|profile_answer|profile_questions|resignation_management|roster_planner|roster_suggestion|shift|shift_roles|site_pendency|slot_bookings|slots|task_approval|task_management_activeworker|task_management_task|training|training_challenge|training_level|training_level_mapping|training_user_progress|user_management_userrole|user_management_usersites|user_sessions)
  group: (blinkit_store_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato)
  schema: (driver_service|mongo_prod_logistics_communication_service)
  table: (store_mapping_movements|communication_entity_statistics|videos|series)
  group: (blinkit_store_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (cd_etls_daily_cancel|consumer_etls_last_15_days_non_converting_searches)
  group: (blinkit_store_metrics)
  privileges:
  - SELECT
  filter: (city_name IN (select city_name from blinkit_iceberg.dwh.superset_redash_access_map
    mt where current_user = mt.email_id and active_flag)) OR ('Overall' = (select
    city_name from blinkit_iceberg.dwh.superset_redash_access_map mt where current_user
    = mt.email_id and city_name = 'Overall' and active_flag))
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (storeops_etls_.*_rls_outlet_id|logistics_data_etls_.*_rls_outlet_id)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
  filter: outlet_id in (select outlet_id from dwh.superset_redash_access_map where
    email_id = current_user) or -1 = (select min(outlet_id) from dwh.superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (storeops_etls_superset_od_slots|serviceability_etls_block_reasons|storeops_etls_realtime_picker_utilisation|storeops_etls_receiver_daily_esto_transfer_loss_invoice_item_details|storeops_etls_sender_daily_esto_transfer_loss_invoice_item_details|storeops_etls_daily_esto_transfer_loss|storeops_etls_agg_hourly_outlet_instore_metrics|storeops_etls_agg_hourly_city_instore_metrics|logistics_data_etls_lm_cpo_v1_based_daily|logistics_data_etls_weekly_fod_outlet_cohort|storeops_etls_order_level_complaint_rca|logistics_data_etls_rider_ob_funnel_view|serviceability_etls_surge_reasons|serviceability_etls_surge_seen_hourly|logistics_data_etls_dh_c2a_surge_bifercation|logistics_data_etls_lm_cpo_metric|logistics_data_etls_operation_metrics|logistics_data_etls_dp_live_logins|logistics_data_etls_logins_delta|storeops_etls_employee_metrics_daily|storeops_etls_employee_metrics_hourly|storeops_etls_instore_metrics_daily_level|storeops_etls_instore_metrics_hourly_level|dwh_bv_scm_deviation_view_tableau|dwh_bv_scm_metrics_tableau|logistics_data_etls_rider_weekly_churn|logistics_data_etls_store_distance_rca|logistics_data_etls_dp_work_details|logistics_data_etls_rider_onboarding|storeops_etls_new_employee_metrics|storeops_etls_store_wastage|storeops_etls_instore_return_loss_metrics|storeops_etls_instore_refund_metrics|storeops_etls_transfer_loss_metrics|storeops_etls_instore_loss_metrics|logistics_data_etls_hourly_time_legs|logistics_data_etls_lm_ops|storeops_etls_location_impact_on_store_ppi|storeops_etls_location_with_high_ppi|storeops_etls_sku_with_bad_ppi|storeops_etls_employee_level_ppi|storeops_etls_complaint_summary_order_level|storeops_etls_complaint_summary_product_level|storeops_etls_test_location_cohorts|logistics_data_etls_hourly_supply_metrics|logistics_data_etls_store_hourly_orders_dp_logins|storeops_etls_goals_sm_cm_weekly|storeops_etls_goals_sm_cm_daily|storeops_etls_packaged_grn|storeops_etls_hp_fresh_grn|logistics_data_etls_blinkit_store_offers_cohorts_login|logistics_data_etls_rider_daily_performance_summary|logistics_data_etls_rider_ob_funnel_bifurcation|logistics_data_etls_b_store_rider_cohorts_weekly_performance|logistics_data_etls_fc_fleet_view|logistics_data_etls_fact_rider_weekly_quality_metrics|logistics_data_etls_fact_rider_weekly_eph|storeops_etls_cluster_level_performance|consumer_etls_store_level_opa_wow|consumer_etls_store_level_opb_wow|storeops_etls_category_hourly_putaway|storeops_etls_fnv_ptype_summary|storeops_etls_daily_agg_outlet_fnv_data|storeops_etls_daily_outlet_sla_tracker|logistics_data_etls_daily_rider_nps_tag_level_summary|logistics_data_etls_daily_rider_nps_summary|consumer_etls_store_level_business_health_metrics|consumer_etls_store_level_bhm_wow|storeops_etls_hourly_store_crates_details|storeops_etls_hourly_employee_summary|storeops_etls_open_ds_variance|logistics_data_etls_weekly_last_mile_fod_funnel|storeops_etls_instore_headcount_hiring_summary|storeops_etls_goals_sm_cm_monthly|storeops_etls_agg_weekly_outlet_instore_metrics|storeops_etls_agg_hourly_outlet_instore_metrics|storeops_etls_agg_hourly_storeops_realtime_metrics|storeops_etls_agg_hourly_storeops_realtime_metrics_till_hour)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
  filter: outlet_id in (select outlet_id from dwh.superset_redash_access_map where
    email_id = current_user) or -1 = (select min(outlet_id) from dwh.superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (consumer_etls_ds_wh_city_level_projections_dod)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
  filter: ( outlet_id IN (select outlet_id from blinkit_iceberg.dwh.superset_redash_access_map
    mt where current_user = mt.email_id and active_flag) AND city_name IN (select
    city_name from blinkit_iceberg.dwh.superset_redash_access_map mt where current_user
    = mt.email_id and active_flag) ) OR ( -1 = (select MIN(outlet_id) from blinkit_iceberg.dwh.superset_redash_access_map
    mt where current_user = mt.email_id and active_flag) AND 'Overall' = (select city_name
    from blinkit_iceberg.dwh.superset_redash_access_map mt where current_user = mt.email_id
    and city_name = 'Overall' and active_flag) ) OR ( 0 = (select MIN(outlet_id) from
    blinkit_iceberg.dwh.superset_redash_access_map mt where current_user = mt.email_id
    and active_flag and outlet_id <> -1) AND 'Overall' = (select city_name from blinkit_iceberg.dwh.superset_redash_access_map
    mt where current_user = mt.email_id and city_name = 'Overall' and active_flag)
    AND level = 'Pan_india' )
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (consumer_etls_city_level_hourly_projections|consumer_etls_hourly_projections_actuals_view1)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
  filter: (city IN (select city_name from blinkit_iceberg.dwh.superset_redash_access_map
    mt where current_user = mt.email_id and active_flag)) OR ('Overall' = (select
    city_name from blinkit_iceberg.dwh.superset_redash_access_map mt where current_user
    = mt.email_id and city_name = 'Overall' and active_flag)) OR ('Overall' = (select
    city_name from blinkit_iceberg.dwh.superset_redash_access_map mt where current_user
    = mt.email_id and city_name = 'Overall' and active_flag))
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (storeops_etls_.*|logistics_data_etls_.*|dwh_absolute_hourly_inventory_availability_perishables|dwh_absolute_hourly_inventory_availability_fnv|ss_hygiene_data_summary|ss_hygiene_data_detail|ss_hygiene_data_cash_difference)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato)
  schema: (blinkit_jumbo2)
  table: (blinkit_delivery_partner_appsflyer_events)
  group: (blinkit_store_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz|dwh|zomato|lumber_events|bistro_etls)
  table: (cd_etls_.*|dim_product|dim_merchant|fact_sales_order_details|fact_sales_order_item_details|fact_sales_order_details_bistro|fact_sales_order_item_details_bistro|one_support_zomato_agents|dynamodb_payments_auth_cd_view|bots_api_user_bot_journey)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (supply_etls_frozen_city_daily_wtd_avail|supply_etls_frozen_outlet_daily_wtd_avail|supply_etls_frozen_city_item_daily_wtd_avail|supply_etls_frozen_city_hourly_wtd_avail|supply_etls_frozen_outlet_hourly_wtd_avail|supply_etls_store_hourly_weighted_availability|consumer_etls_society_tracker|consumer_etls_rca_weekly_view|supply_etls_frozen_backend_tea_tagging|supply_etls_frozen_backend_po|supply_etls_frozen_backend_view_summary|consumer_etls_sccd_asp|consumer_etls_p0_metrics_dau|consumer_etls_sccd_l0_conv|consumer_etls_user_bucket_dau|consumer_etls_superset_city_keyword_conversion_weekly|consumer_etls_growth_dashboard_1|consumer_etls_city_level_business_health_metrics|consumer_etls_city_level_store_metrics|consumer_etls_city_level_business_health_metrics_bounds|consumer_etls_sccd_null_searches|consumer_etls_sccd_l0_upscaled_conv|consumer_etls_sccd_l0_cr_conv|consumer_etls_sccd_ptype_cr_conv|consumer_etls_sccd_item_sales|consumer_etls_sccd_ptype_avail|consumer_etls_sccd_ptype_hourly_avail|consumer_etls_sccd_ptype_conv|consumer_etls_sccd_l0_wtd_avail)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: blinkit
  schema: (marketing_etls|de_etls)
  table: (collab_catalogue_cities|flywheel_invalid_events_log|merchant_product_mapping)
  group: blinkit_flywheel
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit|blinkit_iceberg)
  schema: (de_etls)
  table: (user_slack_info)
  group: zomato_de_etls
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit|blinkit_iceberg)
  schema: (de_etls)
  table: (user_slack_info)
  user: jumbo-alert-bridge
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit|blinkit_iceberg|blinkit_iceberg_staging|blinkit_staging)
  schema: (supply_etls)
  group: (blinkit_supply_derived_metrics|blinkit_category_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (category_etls)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (dwh|po|lake_po|rpc|lake_rpc|retail|lake_retail|vms|lake_vms|lake_ars|ars|logistics_data_etls|snorlax|lake_snorlax|lake_storeops|storeops|seller|lake_seller)
  table: (product_facility_master_assortment|item_tag_mapping|warehouse_facility|agg_daily_search_keyword_conversion_metrics|dim_search_keyword_l0category|dim_product|dim_keywords_l0_mapping|dim_keywords_l1_mapping|dim_keywords_l2_mapping|dim_keywords_ptype_mapping|dim_merchant|physical_facility_outlet_mapping|item_product_mapping|vms_vendor_facility_alignment|job_run|cart_projection|default_cpd|order_complaint|dim_item_product_offer_mapping|seller_product_mappings)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (infra_expansion_etls_.*)
  group: (blinkit_expansion_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (fbg_mtd_lmtd_ytd_sales|fbg_daily_sales|category_etls_fbg_daily_sales_weekly_rls_l0l1cat|category_etls_staples_daily_sales_rls_l0l1cat|category_etls_blinkit_pricing_health_insights_rls_l0l1cat|warehouse_etls_pro_funnel)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
  filter: concat(CAST(l0_id AS VARCHAR), '_', CAST(l1_id AS VARCHAR)) IN ( SELECT
    concat(CAST(l0_category_id AS VARCHAR), '_', CAST(l1_category_id AS VARCHAR))
    FROM dwh.category_superset_redash_access_map WHERE email_id = current_user ) OR
    -1 = (select min(l0_category_id) from dwh.category_superset_redash_access_map
    where email_id = current_user) OR manufacturer IS NULL OR manufacturer ='-' OR
    manufacturer IN (SELECT manufacturer FROM blinkit_iceberg.dwh.category_superset_redash_access_map
    WHERE email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (category_etls_.*_rls_l0l1cat)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
  filter: concat(CAST(l0_id AS VARCHAR), '_', CAST(l1_id AS VARCHAR)) IN ( SELECT
    concat(CAST(l0_category_id AS VARCHAR), '_', CAST(l1_category_id AS VARCHAR))
    FROM dwh.category_superset_redash_access_map WHERE email_id = current_user ) OR
    -1 = (select min(l0_category_id) from dwh.category_superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (ba_etls_fin_daily_pnl|ba_etls_fin_daily_margin)
  group: (blinkit_default|blinkit_category_metrics)
  privileges:
  - SELECT
  filter: ('-1' = (select min(category) from dwh.finance_category_superset_redash_access_map
    WHERE email_id=current_user) AND concat(cast(l0_category_id as varchar),cast(l1_category_id
    as varchar)) IN (select concat(cast(l0_category_id as varchar),cast(l1_category_id
    as varchar)) FROM dwh.finance_category_superset_redash_access_map WHERE email_id=current_user))
    OR ('-1-1' = (select concat(cast(min(l0_category_id) as varchar),cast(min(l1_category_id)
    as varchar)) FROM dwh.finance_category_superset_redash_access_map WHERE email_id=current_user)
    AND category in (select category FROM dwh.finance_category_superset_redash_access_map
    WHERE email_id=current_user)) OR ('-1-1-1' = (select concat(cast(min(l0_category_id)
    as varchar),cast(min(l1_category_id) as varchar),cast(min(category) as varchar))
    FROM dwh.finance_category_superset_redash_access_map WHERE email_id=current_user))
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz|category_etls)
  table: (staples_daily_sales_this_month|staples_mtd_sales_this_month|category_etls_sales_dashboard_monthly_tab_data|category_etls_fbg_daily_sales|category_etls_daily_sales|category_etls_fact_sales_order_item_details|category_etls_pricing_domain_productchangelog|merchant_product_pricing_57614|tableau_daily_category_sales|tableau_mtd_lmtd_category_sales|tableau_monthly_category_sales)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
  filter: concat(CAST(l0_id AS VARCHAR), '_', CAST(l1_id AS VARCHAR)) IN ( SELECT
    concat(CAST(l0_category_id AS VARCHAR), '_', CAST(l1_category_id AS VARCHAR))
    FROM dwh.category_superset_redash_access_map WHERE email_id = current_user ) OR
    -1 = (select min(l0_category_id) from dwh.category_superset_redash_access_map
    where email_id = current_user) OR manufacturer IS NULL OR manufacturer ='-' OR
    manufacturer IN (SELECT manufacturer FROM blinkit_iceberg.dwh.category_superset_redash_access_map
    WHERE email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz|category_etls)
  table: (cd_etls_product_complaints_wow_l2|cd_etls_customer_comment_store_date|cd_etls_product_complaints_category_trend|cd_etls_cd_product_store_dashboard_wow_pid_store|cd_etls_product_store_dashboard_wow_pid_store_complaint_type|cd_etls_product_complaints_last_30_d_complaint_type|cd_etls_product_complaints_wow_l2_complaint_type|cd_etls_product_complaints_last_30_d)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (category_etls_.*|supply_etls_.*)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (supply_etls_store_hourly_weighted_availability|supply_etls_frozen_city_daily_wtd_avail|supply_etls_frozen_city_hourly_wtd_avail|supply_etls_frozen_outlet_daily_wtd_avail|supply_etls_frozen_outlet_daily_wtd_avail|supply_etls_frozen_city_item_daily_wtd_avail)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (category_etls)
  group: (blinkit_category_default)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit)
  schema: (dynamodb)
  table: (blinkit_projection_inventory_service_rt_view)
  group: (blinkit_category_inventory)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit|blinkit_hudi)
  schema: (lake_retail|retail)
  table: (console_outlet|console_company_type)
  group: (blinkit_hyperpure_sensitive_metrics|blinkit_category_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (po|lake_po|retail|lake_retail|rpc|lake_rpc|ars|lake_ars|cms|lake_cms|oms_bifrost|lake_oms_bifrost|dwh|supply_etls|category_etls|seller|lake_seller|vms|lake_vms|ims|lake_ims|viz)
  table: (tot_margin|vendor_config|physical_facility_outlet_mapping|console_outlet|console_merchant|console_location|item_details|dim_item_product_offer_mapping|outlet_details|product_product|dim_merchant_outlet_facility_mapping|dim_product|item_product_mapping|warehouse_facility|product_brand|product_manufacturer|item_outlet_tag_mapping|item_category_details|edi_integration_partner_outlet_mapping|ams_city_cluster_mapping|ams_cluster|console_state|edi_integration_partner_item_mapping|item_facility_state_request|console_business_type|physical_facility|warehouse_outlet_mapping|product_facility_master_assortment|product_master_assortment_substate_reasons|console_outlet_cms_store|item_tag_mapping|e3_live_darkstores|console_merchant|outlet|dim_merchant|gr_product|gr_product_category_mapping|gr_category|gr_product_type|oms_merchant|gr_merchant|gr_merchant_additional_info|gr_locality|gr_virtual_to_real_merchant_mapping|gr_merchant_product_mapping|view_gr_merchant|view_gr_locality|freebie_categories|product_category_type_mapping|gr_product_attribute_mapping|gr_attribute|seller_product_mappings|auth_user|bulk_facility_outlet_mapping|ims_inventory_update_type|consumer_etls_jan_festive_be_fe_inv|consumer_etls_jan_festive_be_inv|category_etls_festive_sales_daily|category_etls_festive_sales_daily|category_etls_festive_daily_conversion|category_etls_festive_daily_conversion|item_tag_mapping_log|gr_merchant_product_tag_mapping|property_mappings|gr_brand)
  group: (blinkit_category_default)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (viz|category_etls)
  table: (category_etls_fact_sales_order_item_details|category_etls_fact_sales_order_details|users_products_fod|category_etls_daily_product_sales)
  group: (blinkit_category_sales)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (rpc|lake_rpc|pricing_v3|lake_pricing_v3|ims|lake_ims|category_etls)
  table: (tot_margin|catalog_domain_kvi_item|ims_item_inventory_log|merchant_product_pricing_57614|pricing_domain_prices|gr_product_price_history|pricing_domain_product|pricing_domain_productchangelog|outlet_item_variant_wlp|attribute_management_brandsfundapproval|attribute_management_brandssheetfile|bundles_and_combos_approval|bundles_and_combos_domain_bundlebrandfundsheetfile|ims_inventory_landing_price|pricing_domain_city|pricing_domain_superstore|rule_management_agendarulerm|rule_management_agendarulermitemid|rule_management_masterrule)
  group: (blinkit_category_margin)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (adserver|lake_adserver|lake_events|dwh|category_etls|search_etls|ads_etls)
  table: (pid_impressions_and_atc|mobile_impression_data|mobile_event_data|agg_daily_search_keyword_conversion_metrics|agg_hourly_search_keyword_conversion_metrics|agg_hourly_consumer_conversion_details|keyword_city_product_additions|city_keyword_conversion|dim_keywords_l0_mapping|dim_keywords_l1_mapping|dim_keywords_l2_mapping|dim_keywords_ptype_mapping|ptype_keyword_mapping|campaign_daily_data|campaign|advertiser|ads_input_searches|ads_keyword_searches|sub_campaign|rules_keyword|recommendations_campaign_data|search_campaign_data|plp_campaign_data|search_impressions_rudder|search_impressions_web|plp_impressions_rudder
    |plp_impressions_web|agg_daily_reach_campaign_data_rudder|ads_final_attribution_visi_updated|ads_final_attribution_rudder|ads_final_attribution_web|agg_daily_consumer_conversion_details)
  group: (blinkit_category_marketing)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh|crates|lake_crates|snorlax|lake_snorlax|ims|lake_ims|pos|lake_pos|po|lake_po|rpc|lake_rpc|vms|lake_vms|ars|lake_ars|warehouse_location|lake_warehouse_location|consumer|dwh|category_etls|supply_etls|warehouse_etls|ars_etls)
  table: (logs_weighted_landing_price|pos_invoice|pos_invoice_product_details|pg_forecast_cat_input|inventory_metrics_backend_master_assortment_log|dwelling_inventory_base|vms_vendor_merchant_mapping|ordering_facility_item_inventory_update_log|facility_item_inventory_update|facility_item_details|inventory_metrics_purchase_mis|item_facility_state_request|vms_vendor_facility_alignment|vms_vendor|default_cpd|outlet_item_aps_derived_cpd|product_master_assortment_substate|inventory_metrics_tea_tagging|warehouse_outlet_mapping|rtv_elg_inputs|vms_vendor_alignment|facility|vms_vendor_city_mapping|vms_vendor_city_tax_info|bulk_facility_transfer_days|moq_qmax_outlet_list|inventory_metrics_open_sto|rpc_daily_availability|job_run|final_indent|warehouse_item_location|warehouse_storage_location|packaged_goods_inventory_and_availability|packaged_goods_inventory_and_availability_log|date_hourly_sales_details|date_wise_consumption|agg_hourly_consumer_conversion_details|ars_qmax|ims_good_inventory|ims_sto_details|ims_sto_item|ims_inventory_stock_details|discrepancy_note|discrepancy_note_product_detail|purchase_order|po_schedule|purchase_order_items|purchase_order_status|purchase_order_state|po_grn|hp_wms|ims_item_inventory|ims_inventory_log|ims_item_blocked_inventory|ims_inventory_landing_price|frontend_cycle_sto_quantity|transfers_optimization_results_v2|item_min_max_quantity_log_v2|ims_entity_vendor_inventory|transfer_tag_rules|iov_fill_rate_buffer_doi|vms_vendor_new_pi_logic|purchase_order_relations|bulk_po_indent_item|physical_facility|ims_inward_invoice|delivery_type|category_etls_facility_item_details_pi_logic_s1|category_etls_rtv_sor_for_v3|absolute_hourly_inventory_availability_fnv|absolute_hourly_inventory_availability_packaged_goods|absolute_hourly_inventory_availability_perishables|hourly_inventory_snapshots|invoice|invoice_payment_mapping|invoice_payment_details|ims_inventory|inventory_metrics_open_po|edi_asn_response|packaged_good_item_outlet_avg_ps|fact_inventory_transfer_details|asn_response|edi_integration_partner_po_invoice_mapping|supply_event_info|daily_orders_and_availability|bulk_process_event_planner|edi_transaction)
  group: (blinkit_category_inventory)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_wms)
  table: (purchase_order|purchase_order_items|po_grn_item|po_grn_mapping)
  group: (blinkit_category_inventory)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (logistics_data_etls)
  table: (cart_projections)
  group: (blinkit_category_projection)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (cd_etls|storeops_etls)
  table: (complaint_details_reso|master_table|complaint_session_customer_comments|item_wise_reso)
  group: (blinkit_category_complaints)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh)
  table: (agg_daily_diff_loss)
  group: (blinkit_category_dump)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (dwh_agg_daily_search_conversion_keyword|consumer_etls_society_tracker)
  group: (blinkit_growth_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit|blinkit_hudi)
  schema: (lake_ims|ims|lake_po|po)
  table: (ims_item_inventory_log)
  group: (blinkit_hyperpure_sensitive_metrics)
  privileges:
  - SELECT
  filter: ( outlet_id IN (SELECT distinct id FROM lake_retail.console_outlet WHERE
    company_type_id IN (SELECT distinct id FROM lake_retail.console_company_type WHERE
    id IN (767, 9, 16, 4))))
- type: table
  entity_category: data_entity
  catalog: (blinkit|blinkit_hudi)
  schema: (lake_po|po)
  table: (edi_integration_partner_item_mapping|edi_integration_partner_purchase_order_details)
  group: (blinkit_hyperpure_sensitive_metrics)
  privileges:
  - SELECT
  filter: ( integration_partner in ('HYPERPURE'))
- type: table
  entity_category: data_entity
  catalog: (blinkit|blinkit_hudi)
  schema: (lake_po|po)
  table: (purchase_order_items)
  group: (blinkit_hyperpure_sensitive_metrics)
  privileges:
  - SELECT
  filter: ( item_id IN (SELECT item_id FROM lake_po.edi_integration_partner_item_mapping
    WHERE integration_partner in ('HYPERPURE')))
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (warehouse_etls|supply_etls|ba_etls|rpc|lake_rpc|warehouse_location|lake_warehouse_location|ims|lake_ims|retail|lake_retail|vms|lake_vms|wms|lake_wms|crates|lake_crates|ars|lake_ars|po|lake_po|reports|lake_reports|eveprod|lake_eveprod)
  group: (blinkit_warehouse_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (lake_po|po|dwh|lake_warehouse_location|warehouse_location)
  table: (po_grn|purchase_order|purchase_order_items|agg_hourly_backend_outlet_item_inventory|fact_inventory_transfer_details|warehouse_zone_id_mapping|warehouse_item_location)
  group: (blinkit_warehouse_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (blinkit_jumbo2)
  table: (warehouse_.*)
  group: (blinkit_warehouse_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (dwh)
  table: (warehouse_superset_redash_access_map)
  group: (blinkit_warehouse_metrics|blinkit_store_metrics|blinkit_store_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (warehouse_etls_.*_rls_facility_id|supply_etls_.*_rls_facility_id)
  group: (blinkit_default|blinkit_warehouse_metrics|blinkit_store_metrics)
  privileges:
  - SELECT
  filter: facility_id IN ( SELECT facility_id FROM dwh.warehouse_superset_redash_access_map
    WHERE email_id = current_user ) OR -1 = (select min(facility_id) from dwh.warehouse_superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit)
  schema: (supply_etls|retail|lake_retail|storeops_etls|ops_management|lake_ops_management|cd_etls)
  table: (vuelogix_ticket_logs|e3_live_darkstores|console_outlet|tracking_event|fact_daily_instore_mandays|shift|user|frozen_iot_energy_meter_logs|frozen_iot_controller_logs|master_table)
  group: (blinkit_admin_infra_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (iot|lake_iot|lake_ops_management|ops_management)
  group: (blinkit_admin_infra_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (logistics_data_etls|logistics_insights)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (logs_etls)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (bistro_etls|ds_etls|dwh|consumer_etls|cd_etls)
  table: (fact_supply_chain_order_details_bistro|logs_order_ts_v2|rider_cluster_mapping_v3_1|fact_supply_chain_order_details|logs_.*|merchant_hourly_forecasting_v2|rs_master_table)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (adserver|ads_etls|lake_adserver)
  group: (blinkit_ads_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (supply_etls_vuelogix_tickets|supply_etls_ideabyte_fleet_temp_graph|supply_etls_ideabyte_fleet_temperature|supply_etls_frozen_wh_temperature|supply_etls_frozen_backend_tea_tagging|supply_etls_frozen_backend_po|supply_etls_frozen_backend_view_summary)
  group: (blinkit_default|blinkit_warehouse_metrics)
  privileges:
  - SELECT
  filter: facility_id IN ( SELECT facility_id FROM dwh.warehouse_superset_redash_access_map
    WHERE email_id = current_user ) OR -1 = (select min(facility_id) from dwh.warehouse_superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (consumer_etls_ds_wh_city_level_projections_dod)
  group: (blinkit_warehouse_metrics)
  privileges:
  - SELECT
  filter: level = 'Warehouse'
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz|warehouse_etls|category_etls)
  table: (warehouse_etls_.*|warehouse_etls_rsto_.*|facility_item_inventory_update)
  group: (blinkit_default|blinkit_warehouse_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz|warehouse_etls)
  table: (supply_etls_festive_metrics_temp_viz_.*|supply_etls_be_inventory_metrics_.*|warehouse_hourly_facility_ds_zone_level_view|warehouse_hourly_facility_zone_level_view|warehouse_hourly_facility_ds_level_view|wh_analytics_picking_v2|wh_analytics_putaway|wh_analytics_unloading|selective_qc_control_limits|selective_qc_picker_score|selective_qc_store_score|warehouse_employee_logs|warehouse_picker_incentive_points|warehouse_picker_incentive_points_day_on_day_level|warehouse_shift_timings|wh_analytic_metrics_daily_level|wh_analytic_metrics_facility_ids|wh_analytic_picker_metrics_zone|wh_analytic_metrics_inventory_capacity|supply_etls_cpu_view|supply_etls_fleet_master_data_view)
  group: (blinkit_default|blinkit_warehouse_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (seller_etls|seller|lake_seller)
  group: (blinkit_seller_hub_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: category_etls
  table: freebie_categories
  group: (blinkit_seller_hub_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (dwh|supply_etls)
  table: (fact_inventory_transfer_details|outlet_details|dim_product)
  group: (blinkit_seller_hub_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (.*seller.*)
  group: (blinkit_seller_hub_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit)
  schema: (supply_etls|ds_etls|dwh|logistics_data_etls|ars_etls)
  table: (fnv_warehouse.*|fnv_store.*|cart_projections|city_hour_weights|city_store_weights|city_item_weights|hourly_inventory_snapshots|fresh_warehouse_store_details|fnv_indent_ordering|facility_item_min_max_quantity|facility_item_min_max_quantity_log|demand_forecast_item_ordering_min_max_quantity|demand_forecast_item_ordering_min_max_quantity_log|demand_forecast_item_min_max_quantity|demand_forecast_item_min_max_quantity_log|demand_forecast_item_ordering_with_feedback|demand_forecast_item_ordering_with_feedback_log|dim_product|fact_sales_order_item_details|dim_merchant_outlet_facility_mapping|ds_enriched_dau_data|fnv_mandi_closure_indent|e3_new_darkstores|fnv_outlet_item_data|dark_stores_fnv_assortment|fact_sales_order_details|agg_daily_search_keyword_conversion_metrics|dim_item_product_offer_mapping|agg_hourly_outlet_item_inventory|fnv_indent_ordering_t_plus_n|demand_forecasting_mview_frontend_outlet_item_daily_demand|demand_forecasting_base_feedback_post_processing_log|perishable_forecast_cpd_logs|ars_fnv_.*|demand_forecasting_global_ft_base)
  group: (blinkit_inventory_fresh_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit|blinkit_hudi)
  schema: (ims|lake_ims|rpc|lake_rpc|po|lake_po|retail|lake_retail|cms|lake_cms|sauron|lake_sauron|dynamodb|ars)
  table: (ims_item_inventory|ims_item_inventory_log|ims_inventory_update_type|product_product|item_category_details|product_facility_master_assortment|item_outlet_tag_mapping|physical_facility_outlet_mapping|edi_integration_partner_item_mapping|console_outlet|console_location|ims_inventory_log|ims_order_details|bulk_facility_outlet_mapping|warehouse_outlet_mapping|item_product_mapping|ims_item_blocked_inventory|gr_product_category_mapping|change_requests|blinkit_store_inventory_service_oi_rt_view_v2|growth_factor|fixed_indent|item_cpd_replication|warehouse_transition|product_brand)
  group: (blinkit_inventory_fresh_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit)
  schema: (viz)
  table: (category_etls_fnv_hourly_metrics_view|category_etls_fnv_inventory_view|category_etls_fnv_dump_view|category_etls_fnv_store_level_data|category_etls_fnv_store_ptype_level_data)
  group: (blinkit_inventory_fresh_metrics)
  privileges:
  - SELECT
  filter: city IN (SELECT city FROM dwh.fnv_city_superset_redash_access_map WHERE
    email_id = current_user) OR '-1' = (select min(city) from dwh.fnv_city_superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit)
  schema: (viz)
  table: (supply_etls_store_hourly_weighted_availability)
  group: (blinkit_inventory_fresh_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi|zomato)
  schema: (dwh|cms|lake_cms|segment_computed_traits|dynamodb)
  table: (dim_product|gr_product_type|gr_category|karma_score_v4_trino|gr_merchant|gr_merchant_product_mapping|merchant_product_property_mapping|blinkit_ratings_and_reviews)
  group: (blinkit_neutrality_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (cd_etls)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (consumer_etls_bistro_rating|consumer_etls_bistro_reviews)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (cms_historian|lake_cms_historian|rpc|lake_rpc|retail|lake_retail|po|lake_po|ars|lake_ars|offer_master|lake_offer_master|lake_events|ims|lake_ims|supply_etls|search_etls|category_etls|dwh)
  table: (item_details|product_product|item_product_mapping|product_product_category|auth_user|historian_model_history|dim_product|mobile_impression_data|ims_inventory|console_outlet|console_outlet_cms_store|physical_facility_outlet_mapping|ims_offer_inventory_log|pid_impressions_and_atc|product_brand|product_manufacture|offer|store_offer|offer_funded|dim_item_product_offer_mapping|purchase_order|purchase_order_items|po_schedule|final_indent|purchase_order_status|inventory_metrics_open_po|historian_model_history|mobile_event_data|agg_daily_search_keyword_conversion_metrics|dim_keywords_l1_mapping|meta_entity_product_relationship|agg_daily_search_keyword_conversion_metrics|dim_keywords_ptype_mapping|dim_keywords_l1_mapping|dim_keywords_l0_mapping|keyword_searches_conversion_model_boost|dim_keywords_l2_mapping|city_keyword_pid_impression_atc)
  group: (blinkit_content_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (supply_etls|warehouse_etls)
  group: (blinkit_replenishment_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (supply_etls_.*)
  group: (blinkit_replenishment_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (dwh|category_etls|vms|lake_vms|lake_rpc|rpc|lake_retail|retail|category_etls|lake_ars|ars|lake_po|po|lake_seller|seller|lake_rpc|rpc|seller_etls|lake_transit_server|transit_server|ims|lake_ims|ars_etls|vendor_console|lake_vendor_console)
  table: (inventory_metrics_open_po|vms_vendor_new_pi_logic|outlet_details|item_details|vms_vendor_facility_alignment|vms_vendor|vms_vendor_city_mapping|vms_vendor_city_tax_info|packaged_goods_inventory_and_availability|packaged_goods_inventory_and_availability_log|dim_product|item_product_mapping|console_outlet|warehouse_facility|console_location|facility_item_inventory_update|job_run|purchase_order|inventory_metrics_purchase_mis|final_indent|seller_product_mappings|ordering_facility_item_inventory_update|facility_item_details|item_tag_mapping|item_category_details|manufacturer_outlet_entity_vendor|product_manufacturer|default_cpd|inventory_metrics_tea_tagging|iov_fill_rate_buffer_doi|seller_inventory|transfers_optimization_results_v2|dim_item_product_offer_mapping|hourly_inventory_snapshots|outlet_item_aps_derived_cpd|physical_facility_outlet_mapping|warehouse_outlet_mapping|transit_consignment|product_facility_master_assortment|product_product|daily_orders_and_availability|event_dod_inv_buildup|item_outlet_tag_mapping|agg_hourly_outlet_item_inventory|fact_sales_order_item_details|dim_merchant_outlet_facility_mapping|dim_outlet|dim_item_product_offer_mapping|dim_product|ims_item_inventory|fact_inventory_transfer_details|frontend_cycle_sto_quantity|flat_sto_item_details|multibusiness_report_temp|purchase_indent_remarks_for_v3|purchase_indent_remarks_for_v3_a|purchase_indent_remarks_for_v3_b|category_etls_facility_item_details_pi_logic_s1|product_master_assortment_substate|console_business_type|product_master_assortment_substate|frontend_facility_vehicle_constraints|ims_sto_details|console_outlet_logistic_mapping|backend_facility_slot_details|console_business_type|ims_sto_item|backend_facility_slot_details|critical_sku_storage_details|auth_user|purchase_order_event_log|appointment_po_mapping|appointment|appointment_slot_mapping|po_reservation_log|facility_day_level_capacity|appointment_log|user_details|category_etls_rtv_sor_for_v3|appointment_po_mapping)
  group: (blinkit_replenishment_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit|blinkit_hudi)
  schema: (lake_po|po)
  table: (purchase_order_items)
  group: (blinkit_replenishment_metrics)
  privileges:
  - SELECT
  filter: ( item_id IN (SELECT item_id FROM lake_po.edi_integration_partner_item_mapping
    WHERE integration_partner in ('HYPERPURE')))
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (cms|lake_cms|lake_product_knowledge_metadata|product_knowledge_metadata)
  group: (blinkit_content_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (dwh|dynamodb|cd_etls|cms|lake_cms|retail|lake_retail|po|lake_po|supply_etls)
  table: (hourly_inventory_snapshots|physical_facility_outlet_mapping|gr_virtual_to_real_merchant_mapping|console_outlet_cms_store|gr_product|dim_product|blinkit_ratings_and_reviews|bistro_os_agent_sessions|bistro_item_wise_reso|dim_merchant|bistro_master_table)
  group: (blinkit_bistro_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz|consumer_etls)
  table: (.*bistro.*)
  group: (blinkit_bistro_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (bistro_etls|kitchen|lake_kitchen)
  group: (blinkit_bistro_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato)
  schema: (blinkit_jumbo2)
  table: (.*bistro.*)
  group: (blinkit_bistro_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato)
  schema: (gatekeeper)
  table: (access_mapper)
  group: (blinkit_fiternal_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (lake_crm)
  table: (crm_user)
  user: (common_airflow|etl_airflow|blinkit_replenishment|cdp_application)
  privileges:
  - GRANT_SELECT
  - SELECT
  columns:
  - name: phone
    allow: true
    mask: zsha1(phone)
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh)
  table: (dim_customer_address_enriched)
  user: (common_airflow|etl_airflow|blinkit_replenishment)
  privileges:
  - GRANT_SELECT
  - SELECT
  columns:
  - name: name
    allow: true
    mask: zsha1(name)
  - name: line1
    allow: true
    mask: zsha1(line1)
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (default|arroyo|access_controller|gatekeeper|jumbo_derived)
  table: (presto_query_logs|trino_otel_collector_sink|zoho_mapping|access_mapper|z_b_user_id_mapping)
  user: (dwh_admin|etl_airflow)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (default)
  table: (zomans_from_hr_sheet)
  user: (dwh_admin|common_airflow)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (blinkit_superset_db)
  user: (dwh_admin|etl_airflow)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (lake_events|dwh|consumer_etls|grofers_db)
  table: (mobile_event_data|dim_product|grofers_app_device_uuid|gr_user_sha256)
  user: (<EMAIL>)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived|zomato4|dynamodb|carthero_prod|driver_service|realtime_enriched_store|hp_wms|hyperpure_etls|hp_consumer|hp_pod|ztransactions|chatdbv2|wallet|flywheel_etls)
  table: (user_payment_entities_data|payments_auth|shipment_legs|orders|driver_logins|users|cities|localities|driver_store_mappings|delivery_drivers|delivery_driver_service_mappings|users_home_location_base|users_app_open_base|zomato_order_history|z_blinkit_expansion_data|restaurants|user_service_prod|gold_members_daywise|number_verification|blinkit_cart|user_properties|blinkit_crm_complaints|order_item|hp_cdc_wastage|buyer_order_requests|order_product|product_issue|upi_vpas|agent_business_mappings|wallets|wallet_users|user_entity_mappings|billing_gateway_settlements_v2|blinkit_meta_category)
  user: (dwh_admin|etl_airflow|cdp_application)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (accounting_production|logs_dashboard_etls)
  table: (driver_touch_point_reports|accounting_reports|trip_pay)
  user: (dwh_admin|etl_airflow)
  privileges:
  - GRANT_SELECT
  - SELECT
  filter: merchant_category = 'BLINKIT'
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (blinkit_jumbo2)
  user: (dwh_admin|etl_airflow|cdp_application)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  user: (common_airflow|etl_airflow)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (dynamodb|jumbo_derived)
  table: (prod_user_preferences_service|social_graph_base)
  privileges:
  - SELECT
  columns:
  - name: contact_name
    allow: true
    mask: "'RESTRICTED'"
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (pricing_v3|lake_pricing_v3)
  table: (pricing_domain_productchangelog)
  group: (blinkit_default|blinkit_zomato_core_metrics|blinkit_store_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
  columns:
  - name: new_wlp
    allow: true
    mask: '0'
  - name: brand_fund_value
    allow: true
    mask: '0'
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (oms_bifrost|lake_oms_bifrost)
  table: (oms_order_item)
  group: (blinkit_default|blinkit_zomato_core_metrics|blinkit_store_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
  columns:
  - name: product_meta
    allow: true
    mask: "'XXXX-XXXX'"
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (ars|lake_ars)
  table: (outlet_item_aps_derived_cpd)
  group: (blinkit_store_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh|dwh_hpl|bistro_etls)
  table: (fact_sales_order_details_bistro|fact_sales_order_item_details_bistro|fact_sales_order_details_hpl|fact_sales_order_item_details_hpl|business_performance_daily_consumer_metrics|metrics_supply_chain_view_board|fact_sales_order_item_details|fact_sales_order_details|dim_customer|dim_merchant_polygon)
  group: (blinkit_default|blinkit_zomato_core_metrics|blinkit_store_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
  columns:
  - name: rm
    allow: true
    mask: '0'
  - name: percentage_retained_margin
    allow: true
    mask: '0'
  - name: total_weighted_landing_price
    allow: true
    mask: '0'
  - name: total_retained_margin
    allow: true
    mask: '0'
  - name: unit_retained_margin
    allow: true
    mask: '0'
  - name: unit_weighted_landing_price
    allow: true
    mask: '0'
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: lake_(payments|auth|logistics|grofers_db|crm|cms|oms_bifrost|user|ops_management|velocity|locus_v2)
  table: (gr_user_merchant|logistics_dark_store_projection|oms_cart_address|auth_block_phone|gr_coordinate|contact_synced|gr_address|contact_synchronizer|gr_merchant|gr_order_address|logistics_task_call_detail|gr_location_tracking|logistics_address|gr_support_user_response|gr_locality|oms_cart|pre_auth_wallet|candidate_profile)
  user: (^(?!pii_redash$).*)
  group: (blinkit_default|blinkit_zomato_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
  columns:
  - name: facebook_access_token
    allow: true
    mask: zsha1(facebook_access_token)
  - name: phone_raw
    allow: true
    mask: zsha1(phone_raw)
  - name: password
    allow: true
    mask: zsha1(password)
  - name: google_access_token
    allow: true
    mask: zsha1(google_access_token)
  - name: formatted_address
    allow: true
    mask: zsha1(formatted_address)
  - name: secret_key
    allow: true
    mask: zsha1(secret_key)
  - name: wallet_auth_token
    allow: true
    mask: zsha1(wallet_auth_token)
  - name: phone_number
    allow: true
    mask: zsha1(phone_number)
  - name: customer_phone
    allow: true
    mask: zsha1(customer_phone)
  - name: email_id
    allow: true
    mask: zsha1(email_id)
  - name: sms_number
    allow: true
    mask: zsha1(sms_number)
  - name: merchant_password
    allow: true
    mask: zsha1(merchant_password)
  - name: phone
    allow: true
    mask: zsha1(phone)
  - name: email
    allow: true
    mask: zsha1(email)
  - name: phone_number_primary
    allow: true
    mask: zsha1(phone_number_primary)
  - name: phone_number_secondary
    allow: true
    mask: zsha1(phone_number_secondary)
  - name: aadhar_number
    allow: true
    mask: zsha1(aadhar_number)
  - name: pan_number
    allow: true
    mask: zsha1(pan_number)
  - name: permanent_address
    allow: true
    mask: zsha1(permanent_address)
  - name: temporary_address
    allow: true
    mask: zsha1(temporary_address)
  - name: device_token
    allow: true
    mask: zsha1(device_token)
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (lake_crm)
  table: (crm_user)
  user: (^(?!pii_redash$).*)
  group: (blinkit_default)
  privileges:
  - GRANT_SELECT
  - SELECT
  columns:
  - name: phone
    allow: true
    mask: zsha1(phone)
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh)
  table: (dim_customer_address_enriched)
  user: (^(?!pii_redash$).*)
  group: (blinkit_default)
  privileges:
  - GRANT_SELECT
  - SELECT
  columns:
  - name: name
    allow: true
    mask: zsha1(name)
  - name: line1
    allow: true
    mask: zsha1(line1)
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (dynamodb_iceberg)
  user: (bi_common)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (fin_etls)
  group: (blinkit_finance_pnl_etls)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (lake_events|viz)
  table: (order_lifecycle_events|realtime_current_day_sales_data)
  group: (blinkit_realtime)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (btransactions)
  group: (blinkit_btransactions_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (zomato)
  table: (btransactions_upi_vpas)
  group: (blinkit_btransactions_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (lake_po|lake_vendor_console|dwh|lake_grofers_db|grofers_db)
  table: (invoice|invoice_event_log|invoice_payment_mapping|invoice_payment_details|dim_merchant|gr_gstin|gr_user_gstin_mapping)
  group: (blinkit_finance_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (blinkit_aurora_billing_db)
  table: (invoice_details|invoice_meta|invoice_order_mapping)
  group: (blinkit_cbs_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (ds_etls)
  table: (e3_live_store_master_v1|e3_ib_ob_planning|e3_closed_stores|competitor_locs|store_onboarding|store_scouting|ds_live_store_polygons|ds_b_opd_at_hex9|ds_b_dau_at_hex9|ds_b_order_data_hex_mapping|ds_qsr_count|ds_store_polygon_metrics|ds_z_dau_at_hex9|ds_z_opd_at_hex9|dau_last_2_weeks_raw_data|nsa_pings_at_city|nsa_pings_at_city_hex|zoh_v_userloc_hexid_map|locus_new_store_scouting|pms_v3_master_daily_snapshot|pms_v3_processed|cluster_metrics_for_new_stores|ds_live_polygon_snapshot|ds_longtail_ord_at_hex9|ds_polygon_intersection_info|ds_z_mtu_at_hex9|e3_uploaded_polygon_metrics|final_polygon_metrics_for_new_stores|locus_scouting_metrics|locus_scouting_polygons|locus_targets_metrics|locus_targets_polygons|locus_visualize_scouting_polygons|onboarding_hex_eta|scouting_targets_metrics|scouting_targets_polygons|e3_uploaded_polygons_cluster|ds_store_polygon_overlap_info|ds_live_store_hex_mapping)
  group: (blinkit_ds_restricted_tables)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh|lake_grofers_db|grofers_db)
  table: (user_properties_daily_base|fact_sales_order_details|gr_user_address_mapping)
  group: (blinkit_zomato_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (zomato)
  table: (users_app_open_base_view|users_home_location_base_view|zoh_v|zomato4_restaurants_view|user_service_prod_view|user_properties_view)
  group: (blinkit_zomato_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived)
  table: (user_properties|users_home_location_base|catalogue_name_cuisine|masthead_campaigns|crystal_masthead|o2_restaurant_attributes|o2_dish_tagging|hyperlocal_cell_base|promo_order_history)
  group: (blinkit_zomato_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: jumbo2
  table: reward_credit_events
  group: (blinkit_zomato_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_write)
  table: (final_user_zs_2|final_updated_bz_base)
  group: (blinkit_zomato_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (menuservicedb)
  table: (catalogue|catalogue_tag|sub_category_entity|sub_category|category_service)
  group: (blinkit_zomato_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (zomato_ml)
  table: (addressid_to_hid_mapping)
  group: (blinkit_zomato_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (dynamodb)
  table: (blinkit_crm_complaints)
  group: (blinkit_category_complaints)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (dynamodb)
  table: (blinkit_crm_complaints)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato)
  schema: (jumbo2)
  table: (poi_walkers|walker_orders)
  group: (blinkit_bistro_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived|zomato4)
  table: (zomato_order_history|restaurants|o2_otr_base|user_properties)
  group: blinkit_flywheel
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (flywheel_etls)
  group: blinkit_flywheel
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh)
  table: (user_properties_lifetime_base)
  group: (district)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (iot|lake_iot|rpc|lake_rpc|supply_etls)
  table: (tracking_event|tracking_device|fnv_cc_suggestive_price|product_product|item_category_details|tracking_device_entity_mapping)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (lake_events)
  table: (mobile_event_data|mobile_impression_data)
  group: district_blinkit_events
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  group: (blinkit_default)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  user: (blinkit_backend_application)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (default)
  table: (presto_query_logs)
  group: (blinkit_default)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2)
  table: (track_service_vicinity_events)
  group: (blinkit_default)
  privileges:
  - GRANT_SELECT
  - SELECT
  filter: source_id = 17110
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (blinkit_superset_db)
  group: (blinkit_default)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_external)
  table: (cred_cohort_sftp|simpl_cohort_sftp)
  group: (blinkit_btransactions_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2)
  table: (sms_tracking)
  group: (blinkit_btransactions_metrics)
  privileges:
  - SELECT
  filter: tenant = 'BLINKIT'
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (blinkit_jumbo2)
  table: (store_onboarding_app_events)
  group: (blinkit_replenishment_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (market_intelligence_etls|jumbo_write)
  table: (hid_address_blinkit_mapping_v5|real_estate_data_1)
  group: (blinkit_market_intelligence_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (blinkit_jumbo2)
  table: (unified_chat_messages|unified_chat_events|crm_ai_usecases)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2|jumbo_derived)
  table: (unified_ticket_events|firefly_logs|sms_tracking|one_support_ticket_sessions|unified_external_ticket_events)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2|external_chat)
  table: (unified_external_ticket_events|channels)
  group: (blinkit_admin_infra_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2)
  table: (hermes_driver_match_stats|hermes_iteration_orders)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
  filter: metadata['tenant'] = 'blinkit'
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (blinkit_etls|blinkit_jumbo2|blinkit_jumbo3|blinkit_billing|blinkit_hp_wms|blinkitchatdb|mongo_blinkitchat|mongo_blinkit_tickets_db|extzomatopromodb|blinkit_external|realtime_enriched_store|blinkitreviewsdb)
  group: (blinkit_default)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (zexperiments)
  table: (o2_product_experiments)
  group: (blinkit_default)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato)
  schema: (dynamodb|order_fulfillment_service_walker)
  table: (instructions|orders)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: tenant = 'BLINKIT'
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (dynamodb)
  table: (user_service_prod)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: source_tenant = 'BLINKIT'
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (dynamodb)
  table: (corrupt_records|blinkit_inventory_service_location|blinkit_cart|prod_ext_promo_service|blinkit_promo_campaigns|karma_service|blinkit_crm_complaints|blinkit_ops_task_management|blinkit_promo_service|blinkit_brand_awards|osrm_cache|prod_blinkit_entity_graph_service|prod_blinkit_cbilling_service|location_service_prod)
  group: (blinkit_default)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (zomatoreviewsdb)
  table: (user_rating_inline)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: experience_id = 16
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2|jumbo3)
  table: (jevent)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: ename IN ('O2CrystalBlinkitScratchCardSnippetImpressionEvent','O2CrystalCarouselBlinkitImpression','O2CrystalBlinkitLockedScratchCardSnippetImpressionEvent','O2CrystalBlinkitLockedScratchCardSnippetTapped','O2CrystalBlinkitScratchCardScratched','O2CrystalCarouselBlinkitTapped','O2CrystalBlinkitScratchCardSnippetTapped','EmailLoginFeedBlinkitInstallViewed','EmailLoginFeedBlinkitInstallTapped','GPSLocationFetchedFailed','BlinkitTagImpression','BlinkitTagTap','rain_status_override_logs','climacell_weather_data', 'JumboEnameDeeplinkOpened', 'deeplink_search')
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (zomato4|dynamodb)
  table: (merchants|prod_merchant_service)
  group: (blinkit_finance_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived)
  table: (salience_res_level)
  group: (blinkit_finance_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (logs_dashboard_etls)
  table: (trip_pay)
  group: (blinkit_finance_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (carthero_prod)
  table: (delivery_drivers|orders|user_rating_reason_mappings|disputes)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (blinkitchatdb)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (blinkit_etls)
  table: (blinkit_session_csat_details|blinkit_cd_bot_builder)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (chatdbv2)
  table: (issue_meta|agents|businesses|agent_site_mappings|support_session_meta|vendors|tl_agent_mappings|custom_ticket_status)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_external)
  table: (city_region_mapping|sam_chains_new|driver_fc_mapping_new|ob_coach_program_master_data_v1|blinkit_complaints_details|surge_segg|ob_vendor_mapping_v1|b_drc_rate_card_v1|b_gigs_input_ver2|b_cpk_skew_input_ver2|b_exception_input_ver2|b_experiment_input_ver2|b_stars_input_ver2|b_weekly_cpk_input_ver2|logs_calendar|property_source_destination_distance)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived)
  table: (z_base_audience_for_blinkit_mt)
  group: (blinkit_zomato_user_audience)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (archiver)
  table: (prod_order_fulfillment_service)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (archiver)
  group: (zomato_archiver_read)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (support_service_prod)
  table: (fleet_coach_meeting_locations_mapping|fleet_coach_leaves|fleet_coach_blocked_riders|user_ratings|survey_records|user_rating_tags_mapping|tags|survey_data|entity_tags_mapping|fleet_coaches|fleet_coach_meetings|fleet_coach_meeting_drivers_mapping|driver_fleet_coach_call_mappings|driver_fleet_coach_mapping|fleet_coach_task_driver_mapping|fleet_coach_task_mapping|fleet_coach_zone_mappings)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (accounting_production)
  table: (driver_touch_point_reports|accounting_reports|driver_platform_service_fees|driver_settlement_ledgers|driver_accounting_event_ledgers|driver_rate_card_infos|driver_financial_details|driver_wallets|driver_tips|driver_cash_ledgers|driver_invoices|restricted_payout_drivers|driver_tag_mappings|driver_referrals|driver_tags|driver_accounting_work_summaries)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (insights_etls)
  table: (rider_daily_metrics|rider_hourly_metrics_v2|fleet_coach_performance_metrics|weather_device_enriched_data|blinkit_rbr_post_ob_data|blinkit_rbr_pre_ob_data)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (dynamodb)
  table: (trip_statistics|prod_driver_service|prod_lead_management_service)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (mongo_driver_onboarding_service_production|mongo_support_service_prod|mongo_rule_engine_prod|mongo_prod_feature_toggle)
  table: (notes|mongo_v3_onboarding_applications|user_grievances|rule_configs|feature_toggle|gigs_order_pay_configs|feature)
  group: (blinkit_zomato_logistic_metrics|blinkit_store_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2)
  table: (appsflyer_events)
  group: (blinkit_default|blinkit_store_core_metrics)
  privileges:
  - SELECT
  filter: app_id = 'app.blinkit.onboarding'
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (mongo_carthero_order_details|mongo_carthero_mongodb_prod)
  table: (smart_society_callbacks|mongo_smart_societies|order_details|mongo_driver_order_details|deleted_records_for_mongo_smart_societies)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (driver_shift)
  table: (driver_slot_opt_ins|slots_daily_capacities|slots|driver_daily_slot_opt_ins|driver_star_gig_summaries)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (support_service_prod_v2|support_service_prod_v1)
  table: (application_properties|driver_fleet_coach_call_mappings|driver_fleet_coach_mapping|fleet_coach_app_releases|fleet_coach_blocked_riders|fleet_coach_leaves|fleet_coach_meeting_drivers_mapping|fleet_coach_meeting_locations_mapping|fleet_coach_meeting_zones_mapping|fleet_coach_meetings|fleet_coach_task_driver_mapping|fleet_coach_task_mapping|fleet_coach_tasks|fleet_coach_zone_mappings|fleet_coaches|note_tag_mapping|note_tags|notes|subnotes|survey_tag_ratings|surveys|survey_tags)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2|realtime_enriched_store)
  table: (unified_ticket_events|rider_dispute_events|weather_aggregated_rain_status|rider_weekly_payouts|driver_offer_payouts|logs_user_grievances|driver_pings|hermes_matcher_states|hermes_iteration_orders|hermes_iteration_drivers|hermes_driver_match_stats|hermes_batching_rejections|driver_cash_limits|app_error_metrics|driver_logins|raw_driver_events|rider_trip_earnings|driver_wallets|driver_accounting_ledgers|driver_accounting_reports|driver_slot_events|driver_selfie_events|driver_excess_cashes|delivery_driver_actions|b2b_order_events|cell_logs_events|hermes_cell_stats|weather_status_events|driver_session_events|hermes_cell_speed_stats|rider_qc_events|rider_lifecycle_events|driver_offer_progress_tracking|weather_action_events|logistics_accounting_event_ledger|ping_tracking|logistics_.*|model_rain_reporting)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato)
  schema: (zintegrations)
  table: (blinkit.*|bistro.*)
  group: (blinkit_default)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (zintegrations)
  table: (freshdesk_logs)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (driver_service)
  table: (address_details|driver_store_mappings|driver_tags|grooming_details|tags|driver_assets|assets|driver_languages|languages|audit_histories|driver_store_migration_infos)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived)
  table: (driver_tag|rider_profile_login_cohort|fc_dp_mapping_base|b_rc_plan_v1|store_driver_daily_slot_summary|rider_daily_accounting_summary|driver_login_10_min|driver_pings_10_min|rider_nps_inputs|delivery_drivers|slot_daily_driver_data|slot_daily_zone_data|rider_level_churn|csat|slot_driver_data|dp_frozen_medal_data|one_support_ticket_base|one_support_ticket_sessions|zb_order_level_data|assignment_history|delivery_drivers_dt|rider_busy_time|rider_profile_nps_data|batching_metrics|instant_driver_store_mapping|driver_cash_limit_v1|delivery_drivers_ob|one_support_ticket_base_v2|cell_serviceability_performance|cell_to_zone_mapping|btpo|rain_orders_base|time_service_logs|hermes_enriched_driver_match_stats|abs_logs_reporting_base|blinkit_base_rain|raw_driver_events_location_speeding_stories)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (carthero_stats_prod)
  table: (telecom_logs)
  group: (telecom_logs)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (carthero_onboarding_prod)
  table: (qc_details|onboarding_applications|background_verification_profiles|aadhaar_details|versions|qc_items)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (leads_master)
  table: (leads_master|leads_master_old)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (logistics_bonus)
  table: (bonuses|slabs|spins|prize_items|inventories|super_coin_transactions|super_coin_actions|super_coin_balance)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (carthero_prod)
  table: (driver_insurance_policies|orders|user_referrals|versions|localities|zones|cities|feature_toggles|features|action_histories|action_reasons|users|driver_sessions|orders|shipment_legs|driver_work_summaries|shipment_leg_trips|dispute_payloads|disputes|driver_documents|active_storage_attachments|active_storage_blobs|delivery_drivers|delivery_trips|driver_additional_details|user_feedback_details|issue_types|issue_sub_types|driver_lifecycle_edit_reasons|carriers|user_feedbacks|delivery_driver_service_mappings|user_referral_leads|user_addresses|auth_details|driver_cash_transactions|driver_assets_issuances|onboarding_statistics|driver_runnr_requests|rider_performance_audits|driver_alerts|payment_transactions|shipments|diy_campaigns|states|driver_signup_requests|driver_cash_deposit_transactions|user_ratings|driver_daily_insurances|user_rating_reasons|actions|driver_tags|driver_tag_mappings|vehicle_details|driver_details|driver_sos_requests)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (referral_partner_prod)
  table: (referrals)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (mongo_driver_accounting_engine)
  table: (driver_order_summaries|central_billing_mongo_rider_payouts)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (driver_service)
  table: (driver_store_mappings)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: category_name = 'BLINKIT'
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived)
  table: (driver_login_hours|driver_login_10_min)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: is_blinkit_rider = 1
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (logs_dashboard_etls)
  table: (b_gigs_capacity_planning|trip_pay)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (accounting_production|logs_dashboard_etls)
  table: (driver_touch_point_reports|accounting_reports|trip_pay)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: merchant_category = 'BLINKIT'
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (location)
  table: (delivery_subzones)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (blinkit_etls)
  table: (b_gigs_milestone_ver1|blinkit_rider_.*|rider_daily_accounting_summary|b_hourly_order_projection)
  group: (blinkit_zomato_logistic_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (carthero_prod)
  table: (delivery_driver_service_mappings)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: driver_service_id = 13
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (zomato4|jumbo2|jumbo_external)
  table: (weather_device_localities|weather_station_events|weather_signal_events|google_weather_api_predictions|weather_prediction_status|city_region_mapping)
  group: (blinkit_default)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (weather_etls)
  group: (blinkit_default)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived)
  table: (zomato_order_history)
  group: (blinkit_zomato_ml_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (zomato_ml)
  table: (dish_tree|dna_customer|user_veg_score_mapping)
  group: (blinkit_zomato_ml_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (zomato4)
  table: (users|users_saved_address)
  group: (blinkit_zomato_ml_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (location_etls)
  table: (address_confidence_daily)
  group: (blinkit_zomato_ml_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived)
  table: (z_blinkit_expansion_data|o2_otr_base|gold_members_daywise|zomato_order_history|users_home_location_base|user_centric_shutdown|users_home_page_base|search_base|catalogue_dish_orders_funnel)
  group: (blinkit_zomato_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (zomato4)
  table: (number_verification|restaurant_meta_settings|restaurant_delivery_map)
  group: (blinkit_zomato_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (arroyo)
  table: (firehose_online_ordering_events)
  group: (blinkit_zomato_core_metrics)
  privileges:
  - GRANT_SELECT
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2)
  table: (hyperpure_order_events|product_warehouse_price_mapping_events|hyperpure_ticket_events|product_inventory_updates|purchase_order_events|po_grn_mapping_events|hyperpure_entity_events|bin_inventory_updates|stock_transfer_order_events|hp_scm_event_tracking)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_wms)
  table: (return_order|mandi_product|product_variant_mapping|asset_types|item_asset_mapping|order_asset_mapping|product_warehouse_config|po_schedule_item|city|bin_inventory_details|additional_bo_charge|product_warehouse_price_mapping_audit_logs|seller_catalogue_audit_logs|stock_transfer_order_status_history|stock_transfer_order|stock_transfer_order_item|product_warehouse_price_mapping|inventory|purchase_order|seller_catalogue|purchase_order_items|seller_outlet|po_grn_item|po_grn_mapping_history|inventory_history|po_item_batches|purchase_order_status_history|product|bin_inventory_logs|picklist_status_history|invoice|orders|picklist|order_item|category|order_status_history|picklist_item|warehouse|po_grn_mapping|po_grn_mapping_status_history|purchase_order_payment|po_item_discrepancy_qty_reasons|city|mandi_price_input|mandi_warehouse_meta|mandi_vendor|platform_vendor_mapping|external_platform|external_price_input|external_vendor_warehouse_mapping|external_product_mapping|external_vendor_warehouse_mapping|putaway_list_item|putaway_status_history|quality_check_task|quality_check_task_status_history|event|putaway_list|quality_check_task_item|quality_check_matrix|entity_image|po_schedule|picklist_reference_mapping|picklist_item_history|task_management|inventory_recon|inventory_recon_history|bin|product_return_note_items|wms_config|wms_config_history|sto_item_issue|product_return_note|vehicle_schedule_items|quality_check_task)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_pod)
  table: (event|orders|order_flags|route|order_wave|vehicle|outlet|route_solutions|product_issue|order_product|reason|delivery_partner|consignment|consignment_status_history|qr_code_entity_mapping|delivery_agent|vehicle_da_device_handshake|temperature_device|dock_scan_entity_mapping_audit|general_settings|warehouse)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hyperpure_etls)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_consumer)
  table: (buyer_order_requests|outlet|account)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_payments)
  table: (orders|creditnote|creditnotereasons|creditnoteproductdetails|invoice_detail|additional_charge|creditaccount|creditprovider|creditmargindetails|creditperiod|orderpayment|refund|wallettransactions|wallet)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_ticket)
  table: (ticket_actions|ticket_type_actions|tickets|ticket_types|ticket_tags)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_consumer_cdc)
  table: (buyer_order_requests)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_payments_cdc)
  table: (creditnote)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_pod_cdc)
  table: (order_product|orders|product_issue|reason)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_ticket_cdc)
  table: (ticket_actions)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_wms_cdc)
  table: (bin_inventory_logs|category|order_item|order_status_history|orders|product|bin_inventory_details)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_wms_invoice)
  table: (wms_invoice)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (hp_users)
  table: (users)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (dynamodb)
  table: (da_location)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_dashboard)
  table: (hp_fnv_cc_warehouse)
  group: (blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (dynamodb)
  table: (prod_hp_order_service)
  group: (blinkit_default|blinkit_zomato_hyperpure_metrics)
  privileges:
  - SELECT
  filter: type in ('BLINKIT', 'ETERNAL_REPLENISHMENT')
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (dynamodb)
  table: (prod_hp_order_service)
  group: (bi_common|dwh_admin|etl_airflow)
  privileges:
  - SELECT
  filter: type in ('BLINKIT', 'ETERNAL_REPLENISHMENT')
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived)
  table: (z_one_support_session_csat_details|one_support_chat_sessions)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: business_id in (41,24)
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived)
  table: (one_support_ticket_base_v2)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: business_id in ('13')
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_derived)
  table: (z_b_user_id_mapping|z_b_uid_mapping)
  group: (blinkit_default)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (mongo_chat)
  table: (production_hour_map)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: businessid in (2,24,41)
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2)
  table: (chat_agent_metrics)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: business = 'Blinkit Delivery Partner Support'
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2)
  table: (karma_events)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: karma_entity = 'BLINKIT_USER'
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo2)
  table: (live_order_service_events)
  group: (blinkit_default)
  privileges:
  - SELECT
  filter: event_name in ('O2CrystalBottomBannerTapped', 'O2CrystalBottomSheetImpression',
    'O2CrystalMastheadShoppableStoriesItemTapped', 'O2CrystalMastheadStaticImpression',
    'O2CrystalMastheadStaticTapped', 'O2CrystalButtonTapped', 'O2CrystalMastheadImageImpression',
    'O2CrystalMastheadImageTapped', 'O2CrystalCarouselTapped', 'O2CrystalBottomBannerImpression',
    'O2CrystalCarouselImpression', 'O2CrystalMastheadVideoTapped', 'O2CrystalMastheadVideoImpression',
    'O2CrystalMastheadShoppableStoriesImpression', 'O2CrystalMastheadShoppableStoriesTapped')
    and (event_data[3] in ('blinkit_ad', 'O2CrystalGroferAdBannerExistingUser', 'O2CrystalGroferAdBannerNonExistingUser',
    '3366635', '2026', '3416456', '3434107', '3454576', '3478367', '3536968', '3578723',
    '3570982', '3454576', '3539216', '3523008', '3560544', '3589736', '3591257') or
    event_data[11] = 'blinkit_new_store_bottom_sheet' or event_data[22] = 'blinkit_bottom_sheet_bottom_button')
- type: table
  entity_category: data_entity
  catalog: (system)
  schema: (metadata)
  table: (materialized_views|table_comments)
  user: (bi_tableau|bi_common|dwh_admin|etl_airflow)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (jumbo_dashboard)
  table: (information_schemas|b_iceberg_information_schemas)
  group: (blinkit_default)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (marketing_etls|flywheel_etls)
  table: (appsflyer_install_funnel_base|b_transact_score_znotb_z_active)
  group: (blinkit_zomato_marketing_metrics|blinkit_zomato_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (zomato.*)
  schema: (revenue_etls)
  table: (blinkit_finance_zerp)
  group: (blinkit_revenue_finance)
  privileges:
  - SELECT
- type: table 
  entity_category: data_entity
  catalog: (zomato.*)
  schema: revenue_etls
  table: blinkit_finance_zerp
  group: blinkit_finance_pnl_etls
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (revenue_etls)
  table: (bizfin_food_rescue_pnl)
  group: food_rescue
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (hp_pod|hp_pod_cdc|hyperpure_etls)
  group: hyperpure_core
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo_write|jumbo_ttl_30days|jumbo_transient|jumbo_playground|staging)
  privileges:
  - DELETE
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: district
  schema: (jumbo_playground|staging|.*_etls)
  user: (<EMAIL>)
  privileges:
  - DELETE
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: (district.*)
  schema: (district_etls|dynamodb)
  table: (going_out_user_properties|prod_ed_user_auth)
  group: (blinkit_district_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (district.*)
  schema: (district_etls)
  table: (district_appsflyer_install_funnel_base_v1)
  group: (blinkit_district_marketing_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: district
  schema: (district_finance_etls|district_etls)
  group: district_finance_all
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (aurora_billing_db|billing_db|cbilling_etls)
  table: (invoice_details|billing_ledger|invoice_meta|entity_invoice_mapping|payout_details_v2)
  group: district_finance_all
  privileges:
  - SELECT
  filter: business_id in ('ZM_EVENTS', 'ZM_CL', 'ZM_CLPRO',  'ZM_TR', 'ZM_SHOP', 'ZM_ADS')
- type: table
  entity_category: data_entity
  catalog: district
  schema: (district_finance_etls)
  group: (district_finance|district_finance_external)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: default
  table: primary_access_control
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: district
  schema: (access_controller|district_etls)
  table: (cid_policy_map|cinema_level_details)
  group: (rls_district_supply|district_.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: district
  schema: (movies_etls)
  table: (movies_orders_history)
  group: rls_district_supply
  privileges:
  - SELECT
  filter: cast(cinemaid as varchar) in (SELECT cast(entity_id as varchar) cinemaid
    FROM district.access_controller.cid_policy_map WHERE dt <> '' AND dt = (SELECT
    max(dt) FROM district.access_controller.cid_policy_map WHERE dt >= date_format(CURRENT_DATE
    - interval '7' DAY, '%Y%m%d')) AND entity_type = 'paytm_cid' AND entity_id IS
    NOT NULL AND email = CURRENT_USER)
- type: table
  entity_category: data_entity
  catalog: district
  schema: (jumbo2)
  table: (movies_events)
  group: (rls_district_supply)
  privileges:
  - SELECT
  filter: dt = date_format(CURRENT_DATE, '%Y%m%d') and cast(theatre_id as varchar)
    in (SELECT cast(entity_id as varchar) cinemaid FROM district.access_controller.cid_policy_map
    WHERE dt <> '' AND dt = (SELECT max(dt) FROM district.access_controller.cid_policy_map
    WHERE dt >= date_format(CURRENT_DATE - interval '7' DAY, '%Y%m%d')) AND entity_type
    = 'paytm_cid' AND entity_id IS NOT NULL AND email = CURRENT_USER)
- type: table
  entity_category: data_entity
  catalog: district
  schema: (jumbo2)
  table: (homepage_tracking)
  group: (rls_district_supply)
  privileges:
  - SELECT
  filter: dt = date_format(CURRENT_DATE, '%Y%m%d') and entity_type in ('movie', 'cinema')
    and cast(entity_id as varchar) in (SELECT cast(entity_id as varchar) cinemaid
    FROM district.access_controller.cid_policy_map WHERE dt <> '' AND dt = (SELECT
    max(dt) FROM district.access_controller.cid_policy_map WHERE dt >= date_format(CURRENT_DATE
    - interval '7' DAY, '%Y%m%d')) AND entity_type = 'paytm_cid' AND entity_id IS
    NOT NULL AND email = CURRENT_USER)
- type: table
  entity_category: data_entity
  catalog: district
  schema: (jumbo2)
  table: (cinema_session_events)
  group: (rls_district_supply)
  privileges:
  - SELECT
  filter: dt = date_format(CURRENT_DATE, '%Y%m%d') and cast(paytm_cinema_id as varchar)
    in (SELECT cast(entity_id as varchar) cinemaid FROM district.access_controller.cid_policy_map
    WHERE dt <> '' AND dt = (SELECT max(dt) FROM district.access_controller.cid_policy_map
    WHERE dt >= date_format(CURRENT_DATE - interval '7' DAY, '%Y%m%d')) AND entity_type
    = 'paytm_cid' AND entity_id IS NOT NULL AND email = CURRENT_USER)
- type: table
  entity_category: data_entity
  catalog: district
  schema: (movies)
  table: (cinema_details|cinema_cancellation_config|cinema_item_metadata|cinema_resource|city_cinema_mapping)
  group: (rls_district_supply)
  privileges:
    - SELECT
  filter: cast(paytm_cinema_id as varchar) in (SELECT cast(entity_id as varchar) cinemaid 
    FROM district.access_controller.cid_policy_map WHERE dt <> '' AND 
    dt = (SELECT max(dt) FROM district.access_controller.cid_policy_map WHERE 
    dt >= date_format(CURRENT_DATE - interval '7' DAY, '%Y%m%d')) AND entity_type = 'paytm_cid' AND 
    entity_id IS NOT NULL AND email = CURRENT_USER)
- type: table
  entity_category: data_entity
  catalog: district
  schema: (district_etls|jumbo2)
  table: (cinema_level_details|movies_events|entity_attribute_values|entity_attribute_values|cinema_session_events|appsflyer_events|homepage_tracking|going_out_user_properties)
  group: (rls_district_supply)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: (jumbo_external)
  table: (district_cities|city_region_mapping)
  group: (rls_district_supply)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: district
  schema: (movies)
  group: (rls_district_supply)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: district
  user: (<EMAIL>|ml_jobs|rating-jobs|tableau-etl|zml-segments|common-python-etls|zexperiments|ml|dataplatform-python-client|jumbo_rscripts|zml-jobs|zdp-common-python-etls|zexperiments-etl)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  group: (admin|data_platform)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: jumbo_external
  table: (city_region_mapping)
  group: district
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (rbac_dining_etls)
  table: res_.*
  group: rls_dining_city_admin
  privileges:
  - SELECT
  filter: cast(city_id as varchar) in (select cast(entity_id as varchar) from default.primary_access_control
    where entity_type = 'DINING_CITY_ID' and dt = (select max(dt) from default.primary_access_control
    where dt >= date_format(current_date - INTERVAL '8' DAY, '%Y%m%d')) and email
    = current_user)
- type: table
  entity_category: data_entity
  schema: (rbac_dining_etls)
  table: res_.*
  group: rls_dining_supply
  privileges:
  - SELECT
  filter: cast(res_id as varchar) in (select cast(entity_id as varchar) from default.primary_access_control
    where entity_type = 'DINING_RES_ID' and dt = (select max(dt) from default.primary_access_control
    where dt >= date_format(current_date - INTERVAL '8' DAY, '%Y%m%d')) and email
    = current_user)
- type: table
  entity_category: data_entity
  catalog: (district|district_iceberg)
  group: (district.*|dining_out.*|events.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo2|jumbo3)
  table: order_address_updates
  group: order_address
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (revenuedb_temp)
  group: revenue
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (default)
  table: presto_query_logs|druid_query_logs
  group: (dwh|grc)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (grc_etls)
  group: grc
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (dp_etls|route53_resolver_logs|vpc_flow_logs)
  group: (data_platform|sre)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: default
  table: presto_query_logs
  privileges:
  - SELECT
  filter: case when arrays_overlap(current_groups(), ARRAY ['admin', 'dwh', 'grc', 'gen_ai']) THEN 1 = 1 else user = current_user end
- type: table
  entity_category: data_entity
  catalog: hive
  schema: dwh_etls
  table: daily_sql_reporting_performance
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (hive|zomato)
  schema: dashboard_redash
  table: (queries|users)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: default
  table: druid_query_logs
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (hive_staging|zomato_staging|staging)
  user: (.*.php.*|<EMAIL>|ml_jobs|rating-jobs|tableau-etl|zml-segments|common-python-etls|zexperiments|ml|dataplatform-python-client|jumbo_rscripts|zml-jobs|zdp-common-python-etls|zexperiments-etl)
  privileges:
  - DELETE
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  user: (.*.php.*|<EMAIL>|ml_jobs|rating-jobs|tableau-etl|zml-segments|common-python-etls|zexperiments|ml|dataplatform-python-client|jumbo_rscripts|zml-jobs|zdp-common-python-etls|zexperiments-etl)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (lending_etls)
  group: lending
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo2)
  table: unified_chat_messages
  group: chat_int
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (competition_etls|market_intelligence_etls)
  group: competition
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (market_intelligence_etls)
  table: (dining_.*)
  group: (dining_out.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: karma_etls
  group: karma_etls
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (dynamodb|jumbo_derived|zomato4|location_etls)
  table: (user_service_prod|user_service_prod_audit_log|users_saved_address|users_saved_address_associations|duplicate_address|poi_all_addresses)
  group: locations
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: zomato_ml
  table: (diamond_order_level_data|ds_segments_internal|ds_segments_internal_v2)
  group: ds_net_margin
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: rider_phone_devices
  group: rider_phone
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (dynamodb|jumbo2|blinkit_jumbo2)
  table: (prod_telecom_service|telecom_call_tracking|telecom_ivr_tracking)
  group: telecom_pii
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: jumbo2
  table: stitch_events
  group: stitch
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: jumbo2
  table: stitch_events
  group: stitch
  group_negated: true
  privileges: []
- type: table
  entity_category: data_entity
  schema: (insights_etls)
  table: (res_o2_payout_details)
  group: priviledged_insights_group
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (zomato4|jumbo_derived|revenue_etls|insights_etls)
  table: (rate_card_components|daily_merchant_details_snapshot|o2_daily_rate_cards|bizfin_rate_card|onboarding_source|refund_mismatches|ba_hub_analytics|mx_epicentre_pnl|res_o2_payout_details)
  group: ratecard_cv
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo_external)
  table: (projected_orders)
  group: ratecard_cv
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: o2_daily_rate_cards
  group: rls_city_leadership
  privileges:
  - SELECT
  filter: res_id in (select res_id from jumbo_derived.res_access_control_mapping where
    email = current_user and res_id in (select res_id from jumbo_derived.o2_restaurant_attributes
    as a where (regional_sam_flag = 1 OR am_id is not null) and a.sam_flag = 0 and
    o2_active_flag = 1 and res_id is not null))
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: (zomato_order_history|promo_order_history|mx_epicentre_orders)
  group: rls_city_leadership
  privileges:
  - SELECT
  filter: res_id in (select res_id from jumbo_derived.res_access_control_mapping where
    email = current_user)
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: (experiment_service_zoh_meta|order_level_net_margin)
  group: (rls_city_leadership|rls_common_all|rls_logistics_all)
  privileges:
  - SELECT
  columns:
  - name: commission
    allow: true
    mask: '0'
  - name: logs_cost
    allow: true
    mask: '0'
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: (l0_consumer_metrics_v2)
  group: (rls_city_leadership|rls_common_all|rls_logistics_all)
  privileges:
  - SELECT
  columns:
  - name: total_commission
    allow: true
    mask: '0'
  - name: total_dc_realized
    allow: true
    mask: '0'
  - name: total_logs_cost
    allow: true
    mask: '0'
  - name: total_gmv_upa
    allow: true
    mask: '0'
  - name: total_net_margin
    allow: true
    mask: '0'
  - name: total_ord_share
    allow: true
    mask: '0'
  - name: total_zvd
    allow: true
    mask: '0'
  - name: zvdpo
    allow: true
    mask: '0'
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: (onboarding_source|refund_mismatches)
  privileges:
  - SELECT
  columns:
  - name: overall_comm
    allow: true
    mask: '0'
  - name: applicable_commission
    allow: true
    mask: '0'
  - name: base_comms
    allow: true
    mask: '0'
  - name: kitchen_commission
    allow: true
    mask: '0'
  - name: kitchen_comms
    allow: true
    mask: '0'
  - name: log_commission_rate
    allow: true
    mask: '0'
  - name: log_comms
    allow: true
    mask: '0'
  - name: net_comm_amt
    allow: true
    mask: '0'
  - name: pg_apply_flag
    allow: true
    mask: '0'
  - name: pg_charge
    allow: true
    mask: '0'
  - name: pg_charge_rate
    allow: true
    mask: '0'
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: incremental_cmpo
  group: city_admin
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: superset_dataset_views
  table: (pnl_o2_finance_.*|pnl_o2_finance)
  group: pnl_o2_finance
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: superset_dataset_views
  table: pnl_o2_sam_finance
  group: pnl_o2_sam_finance
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: superset_dataset_views
  table: pnl_o2_everyday
  group: pnl_o2_everyday
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (billing_db|aurora_billing_db|blinkit_aurora_billing_db)
  group: (central_billing.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (dynamodb_iceberg)
  table: (prod_zomato_payout_details_v3)
  group: (central_billing.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (aurora_billing_db|billing_db|cbilling_etls)
  table: (invoice_details|billing_ledger|payout_details_v2)
  group: rider_finance
  privileges:
  - SELECT
  filter: business_id in ('ZM_RD')
- type: table
  entity_category: data_entity
  schema: (cbilling_etls|ztransactions)
  table: (payout_details_v2|creditline_ageing|zomato_transactions|capture_transactions)
  group: ads_finance
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (aurora_billing_db|ztransactions|billing_db)
  table: (invoice_details|entity_invoice_mapping|invoice_meta|billing_ledger|payout_order_mapping|payout_details)
  group: ads_finance
  privileges:
  - SELECT
  filter: business_id in ('ZM_ADS')
- type: table
  entity_category: data_entity
  schema: (jumbo2)
  table: (driver_accounting_ledgers)
  group: rider_finance
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (aurora_billing_db|billing_db|cbilling_etls)
  table: (invoice_details|billing_ledger|payout_details_v2)
  group: everyday_finance
  privileges:
  - SELECT
  filter: service_id in ('EVERYDAY')
- type: table
  entity_category: data_entity
  schema: (jumbo_derived|product_analytics_etls|logs_dashboard_etls|rbac_etls|accounting_production|jumbo2|insights_etls)
  table: (logs_overlap_ratecard|logs_rc_automation|veg_logs_rc_automation|logs_overlap_backend|rider_daily_accounting_summary|trip_pay|city_zone_cpo_metric|city_cost_reporting|gigs_15_min_daily_driver_data|revised_slots_daily_driver_data|driver_accounting_event_ledgers|rider_trip_earnings|driver_accounting_ledgers|trip_earnings_flat|dp_hourly_kpi_stats_weekly_v2|dp_hourly_kpi_stats_wtn_v2|logistics_accounting_event_ledger|hermes_next_order_prediction)
  group: (logs_rider_accounting|online_ordering_engineering|zomato_corporate_engineering|blinkit_engineering|eternal_corporate_engineering)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: accounting_production
  group: (logs_rider_accounting|online_ordering_engineering|zomato_corporate_engineering|blinkit_engineering|eternal_corporate_engineering)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo_derived|jumbo2|ads_etls|zomato4|mongo_ads|jumbo_dashboard|zomato_ads)
  table: (o2_restaurant_attributes|restaurants|rider_daily_accounting_summary|time_serviceability_demand_supply_events|campaign_performance|o2_grid_visibility_and_shutdown|restaurant_funnel|restaurant_hourly_shutdown|salience_session_data|zrp_campaigns|campaigns|merchants|supply_res_metrics|ads_sales_quality|time_service_logs|banners|o2_tab_level_zvd_final|o2_growth_user_master|ors_orders|promo_code_usage_daily|campaign_performance_v2|ad_sales_leadlist|restaurant_ratings|bharat_gmv|o2_tab_level_promo_metrics|res_nrl_users|ads_res_saturation|kpt_order_metrics|dsrp_base|o2_cm_data|gdp_supply_snapshot|bnb_logged_in_rt_new|salt_query_rt|trip_earnings_flat|o2_lzero_metrics_agg|tab_level_cost_to_z|city_ceo_growth_enriched|product_funnel)
  group: (rls_city_leadership|rls_common_all)
  privileges:
  - SELECT
  filter: cast(city_id as varchar) in (select cast(entity_id as varchar) from default.primary_access_control
    where entity_type = 'CITY_ID' and dt = (select max(dt) from default.primary_access_control
    where dt >= date_format(current_date - INTERVAL '8' DAY, '%Y%m%d')) and email
    = current_user)
- type: table
  entity_category: data_entity
  schema: (jumbo_derived|logs_dashboard_etls)
  table: (logs_overlap_ratecard|logs_overlap_backend|trip_pay|city_zone_cpo_metric|gigs_15_min_daily_driver_data)
  group: (rls_city_leadership|rls_common_all|rls_logistics_all)
  privileges:
  - SELECT
  filter: cast(zone_id as varchar) IN (SELECT cast(entity_id as varchar) zone_id FROM
    default.primary_access_control WHERE dt <>'' AND dt = (SELECT max(dt) FROM default.primary_access_control
    WHERE dt>= date_format(CURRENT_DATE - interval '2' DAY, '%Y%m%d')) AND entity_type='ZONE_ID'
    AND entity_id IS NOT NULL AND email = CURRENT_USER)
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: rider_daily_accounting_summary
  group: (rls_city_leadership|rls_logistics_all|rls_common_all)
  privileges:
  - SELECT
  filter: cast(runnr_city_id as varchar) in (select cast(entity_id as varchar) from
    default.primary_access_control where entity_type = 'RUNNR_CITY_ID' and dt = (select
    max(dt) from default.primary_access_control where dt >= date_format(current_date
    - INTERVAL '8' DAY, '%Y%m%d')) and email = current_user)
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: (revised_slots_daily_driver_data|hyperlocal_cell_base|hyperlocal_locality_base|dish_conversion_zone_level|dish_conversion_locality_level)
  group: (rls_city_leadership|rls_logistics_all|rls_common_all)
  privileges:
  - SELECT
  filter: cast(zomato_city_id as varchar) in (select cast(entity_id as varchar) from
    default.primary_access_control where entity_type = 'CITY_ID' and dt = (select
    max(dt) from default.primary_access_control where dt >= date_format(current_date
    - INTERVAL '8' DAY, '%Y%m%d')) and email = current_user)
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: supply_menu_mix
  group: (rls_city_leadership|rls_common_all)
  privileges:
  - SELECT
  filter: cast(city as varchar) in (select cast(entity_id as varchar) from default.primary_access_control
    where entity_type = 'CITY_ID' and dt = (select max(dt) from default.primary_access_control
    where dt >= date_format(current_date - INTERVAL '8' DAY, '%Y%m%d')) and email
    = current_user)
- type: table
  entity_category: data_entity
  schema: (jumbo2|jumbo_derived|jumbo_dashboard)
  table: (eta_tracking|ctr_otr_base|o2_otr_base|o2_food_funnel_rca|users_home_location_base|growth_new_lapsed_users)
  group: (rls_city_leadership|rls_common_all|rls_logistics_all)
  privileges:
  - SELECT
  filter: cast(cityid as varchar) in (select cast(entity_id as varchar) from default.primary_access_control
    where entity_type = 'CITY_ID' and dt = (select max(dt) from default.primary_access_control
    where dt >= date_format(current_date - INTERVAL '8' DAY, '%Y%m%d')) and email
    = current_user)
- type: table
  entity_category: data_entity
  schema: (carthero_prod|jumbo2)
  table: (zones|hermes_driver_match_stats)
  group: (rls_city_leadership|rls_logistics_all|rls_common_all)
  privileges:
  - SELECT
  filter: cast(city_id as varchar) IN (SELECT cast(entity_id as varchar) runnr_city_id
    FROM default.primary_access_control WHERE dt <>'' AND dt = (SELECT max(dt) FROM
    default.primary_access_control WHERE dt>= date_format(CURRENT_DATE - interval
    '8' DAY, '%Y%m%d')) AND entity_type='RUNNR_CITY_ID' AND entity_id IS NOT NULL
    AND email = CURRENT_USER)
- type: table
  entity_category: data_entity
  schema: (jumbo_derived)
  table: (delivery_drivers|incentive_summary|trip_earnings_flat)
  group: (rls_city_leadership|rls_logistics_all|rls_common_all)
  privileges:
  - SELECT
  filter: cast(city_id as varchar) IN (SELECT cast(entity_id as varchar) runnr_city_id
    FROM default.primary_access_control WHERE dt <>'' AND dt = (SELECT max(dt) FROM
    default.primary_access_control WHERE dt>= date_format(CURRENT_DATE - interval
    '8' DAY, '%Y%m%d')) AND entity_type='RUNNR_CITY_ID' AND entity_id IS NOT NULL
    AND email = CURRENT_USER)
- type: table
  entity_category: data_entity
  schema: jumbo_derived
  table: (driver_login_hours|driver_pings_10_min|driver_login_10_min|serviceability_performance_metrics_v2)
  group: (rls_city_leadership|rls_logistics_all|rls_common_all)
  privileges:
  - SELECT
  filter: cast(runnr_city_id as varchar) IN (SELECT cast(entity_id as varchar) runnr_city_id
    FROM default.primary_access_control WHERE dt <>'' AND dt = (SELECT max(dt) FROM
    default.primary_access_control WHERE dt>= date_format(CURRENT_DATE - interval
    '8' DAY, '%Y%m%d')) AND entity_type='RUNNR_CITY_ID' AND entity_id IS NOT NULL
    AND email = CURRENT_USER)
- type: table
  entity_category: data_entity
  schema: jumbo2
  table: driver_pings
  group: (rls_city_leadership|rls_logistics_all|rls_common_all)
  privileges:
  - SELECT
  filter: cast(cityid as varchar) IN (SELECT cast(entity_id as varchar) runnr_city_id
    FROM default.primary_access_control WHERE dt <>'' AND dt = (SELECT max(dt) FROM
    default.primary_access_control WHERE dt>= date_format(CURRENT_DATE - interval
    '8' DAY, '%Y%m%d')) AND entity_type='RUNNR_CITY_ID' AND entity_id IS NOT NULL
    AND email = CURRENT_USER)
- type: table
  entity_category: data_entity
  schema: (rbac_etls|rbac_supply_etls)
  table: (city_.*|res_.*)
  group: city_admin
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: rbac_logistics_etls
  table: fc_.*
  group: (city_admin|rls_city_leadership)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: rbac_logistics_etls
  table: fc_fom_.*
  group: (rls_logistics_fc|rls_logistics_all|rls_common_all)
  privileges:
  - SELECT
  filter: cast(fleet_coach_id as varchar) IN (SELECT cast(entity_id as varchar) fc_id
    FROM default.primary_access_control WHERE dt <>'' AND dt = (SELECT max(dt) FROM
    default.primary_access_control WHERE dt>= date_format(CURRENT_DATE - interval
    '8' DAY, '%Y%m%d')) AND entity_type in ('FC_MENTOR_MAPPING','FC_ID','FLEET_COACH_ID')
    AND entity_id IS NOT NULL AND lower(email) = CURRENT_USER)
- type: table
  entity_category: data_entity
  schema: rbac_logistics_etls
  table: fc_.*
  group: (rls_logistics_fc|rls_logistics_all|rls_common_all)
  privileges:
  - SELECT
  filter: cast(fleet_coach_id as varchar) IN (SELECT cast(entity_id as varchar) fc_id
    FROM default.primary_access_control WHERE dt <>'' AND dt = (SELECT max(dt) FROM
    default.primary_access_control WHERE dt>= date_format(CURRENT_DATE - interval
    '8' DAY, '%Y%m%d')) AND entity_type in ('FC_ID','FLEET_COACH_ID') AND entity_id
    IS NOT NULL AND lower(email) = CURRENT_USER)
- type: table
  entity_category: data_entity
  schema: (jumbo_dashboard|location|jumbo_derived|zomato4|jumbo_external)
  table: (live_order_service_z_plus|cells|o2_restaurant_attributes|gold_members_daywise|delivery_subzones|zones|cities|jevent_z_plus|reward_page_events_z_plus|z_plus_campaign_brands|masthead_campaigns|crystal_masthead|crystal_scratch_cards|crystal_targetting_segments|masthead_daily_summary)
  group: zomato_plus
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: rbac_etls
  table: city_supply_metrics
  privileges:
  - SELECT
  filter: cast(cityid as varchar) in (select cast(entity_id as varchar) from default.primary_access_control
    where entity_type = 'CITY_ID' and dt = (select max(dt) from default.primary_access_control
    where dt >= date_format(current_date - INTERVAL '8' DAY, '%Y%m%d')) and email
    = current_user)
- type: table
  entity_category: data_entity
  schema: (rbac_etls|rbac_supply_etls)
  table: (city_.*|cart_breaker_score)
  privileges:
  - SELECT
  filter: cast(city_id as varchar) in (select cast(entity_id as varchar) from default.primary_access_control
    where entity_type = 'CITY_ID' and dt = (select max(dt) from default.primary_access_control
    where dt >= date_format(current_date - INTERVAL '8' DAY, '%Y%m%d')) and email
    = current_user)
- type: table
  entity_category: data_entity
  schema: rbac_logistics_etls
  group: (rls_logistics_all|rls_common_all|rls_city_leadership)
  table: city_driver_accounting_event_ledgers
  priority: 1
  privileges:
  - SELECT
  filter: cast(city_id as varchar) in (select cast(entity_id as varchar) from
    default.primary_access_control where entity_type = 'RUNNR_CITY_ID' and dt = (select
    max(dt) from default.primary_access_control where dt >= date_format(current_date
    - INTERVAL '8' DAY, '%Y%m%d')) and email = current_user)
- type: table
  entity_category: data_entity
  schema: rbac_logistics_etls
  group: (rls_logistics_all|rls_common_all|rls_city_leadership)
  table: city_.*
  privileges:
  - SELECT
  filter: cast(city_id as varchar) in (select cast(entity_id as varchar) from default.primary_access_control
    where entity_type = 'CITY_ID' and dt = (select max(dt) from default.primary_access_control
    where dt >= date_format(current_date - INTERVAL '8' DAY, '%Y%m%d')) and email
    = current_user)
- type: table
  entity_category: data_entity
  schema: zcd_etls
  table: rbac_agent_id_.*
  group: (rls_zcd_associate|rls_zcd_associate_tl)
  privileges:
  - SELECT
  filter: cast(agent_id as varchar) in (select cast(entity_id as varchar) from default.primary_access_control
    where entity_type = 'AGENT_ID' and dt = (select max(dt) from default.primary_access_control
    where dt >= date_format(current_date - INTERVAL '8' DAY, '%Y%m%d')) and email
    = current_user)
- type: table
  entity_category: data_entity
  schema: zcd_etls
  table: rbac_agent_id_.*
  group: online_ordering_customer_delight
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo2|chatdbv2)
  table: (unified_chat_events|agents)
  group: (zcd_associate_rtm)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (default|chatdbv2)
  table: (primary_access_control|agents)
  group: rls_zcd_associate
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (rbac_etls|jumbo_derived)
  table: (res_msa|o2_daily_rate_cards|res_funnel_dropoff_base|res_socialads_insights|res_socialads_nrl|res_socialads_category_share)
  group: rls_supply_am
  privileges:
  - SELECT
  filter: cast(res_id as varchar) in (select cast(res_id as varchar) from jumbo_derived.res_access_control_mapping
    where email = current_user)
- type: table
  entity_category: data_entity
  schema: (jumbo_derived|rbac_supply_etls)
  table: (bnb_logged_in_rt_new|res_salt_adj_bnb_nka|res_salt_adj_bnb_pt|res_comp_item_price)
  group: rls_supply_am
  privileges:
  - SELECT
  filter: cast(z_res_id as bigint) in (select res_id from jumbo_derived.res_access_control_mapping
    where email = current_user)
- type: table
  entity_category: data_entity
  schema: (ads_etls)
  table: (res_campaign_level_details|res_ads_attributes|res_vp_plus_base|res_offline_ads_metrics|res_campaign_level_recommendation)
  group: rls_supply_am
  privileges:
  - SELECT
  filter: res_id in (select res_id from jumbo_derived.res_access_control_mapping where
    email = current_user)
- type: table
  entity_category: data_entity
  schema: (rbac_etls|rbac_supply_etls)
  table: (res_txn_metrics|res_supply_menu_mix|res_smid|res_ora|res_promo_orders|res_campaign_performance|res_ads_br_details|res_ads_billing|res_ba_hub_data_incentive|res_nrl_funnel|res_pox_metrics|res_rejection_reasons|res_ad_invoices_postpaid|res_rate_card_rt|res_bos_keyword|res_ba_hub_data|res_level_festival_projection|res_cuisines_share|res_festival_item_projection_gmv|res_txn_tab|res_brand_pack_subscriptions|res_winback_metrics|res_winback_complaints|res_ba_hub_growth_recommendations|res_wb_cost_sharing|res_bos_menu_scores|res_menu_score|res_catalogue_dish_tag|res_kam_meeting_tracker|res_promo_code_logs|res_funnel_hourly_unique|res_masthead_ads|res_m2o_funnel|res_fssai_status_live_tracker|res_bos_rpi|res_catalogue_quick_tag|res_promo_recommendation|res_distance_based_rc|res_dsz_l0_metrics|res_item_visibility|res_hourly_visibility_stats|res_funnel_metrics_base|res_raredrops_trynew_sales|res_fav_trynew_sales)
  group: rls_supply_am
  privileges:
  - SELECT
  filter: res_id in (select res_id from jumbo_derived.res_access_control_mapping where
    email = current_user)
- type: table
  entity_category: data_entity
  schema: (rbac_etls|rbac_supply_etls)
  table: (res_similar_search)
  group: rls_supply_am
  privileges:
  - SELECT
  filter: entity_id in (select res_id from jumbo_derived.res_access_control_mapping where
    email = current_user)
- type: table
  entity_category: data_entity
  schema: (rbac_etls)
  table: (res_nka_rca)
  group: rls_supply_am
  privileges:
  - SELECT
  filter: brand_name in (select brand_name from jumbo_external.sam_resid_mappings
    where brand_id in (select cast(entity_id as bigint) from default.primary_access_control
    where dt = (select max(dt) from default.primary_access_control where dt >= date_format(current_date
    - INTERVAL '8' DAY, '%Y%m%d')) and ((entity_type = 'BRAND_ID' and email = current_user)
    or designation in ('Administrator', 'Core Admin'))))
- type: table
  entity_category: data_entity
  schema: (jumbo_external|rbac_etls|jumbo_derived|rbac_supply_etls)
  table: (pc_data|res_mx_escalation_snapshot_kam|item_visibility|commission_reduction_exception|flash_sale_salts|flash_sale_salts|res_item_kpt|res_express_funnel_metrics|res_comp_pc|res_addition_rejection_code_upload|res_removal_rejection_code_upload|res_o2_promo_code_upload_log|res_bulk_removal_code_upload|res_bulk_addition_code_upload|res_cart_dropoff_item_lvl)
  group: rls_supply_am
  privileges:
  - SELECT
  filter: cast(res_id as varchar) in (select cast(res_id as varchar) from jumbo_derived.res_access_control_mapping
    where email = current_user)
- type: table
  entity_category: data_entity
  schema: jumbo_external
  table: commission_changes_request_status
  group: rls_supply_am
  privileges:
  - SELECT
  filter: kam_email = current_user
- type: table
  entity_category: data_entity
  schema: (jumbo_derived|jumbo_external|zomato4|default|rbac_etls|mx_etls|ads_etls)
  table: (ba_hub_access_control_data|lp_exclusions|res_ba_mapping_for_incentives|goat_res_dates|client_dashboard_permissions|res_access_control_mapping|kam_cluster_mapping|tableau_rls_mapping|primary_access_control|promo_order_history|res_universal_mapping|res_live_tracker|pc_cart|kam_incentive_portfolio_mapping|active_kams_list_account_management|epicentre_portfolio_res_mapping|sam_resid_mappings|ads_city_targets|ads_kams_target|ads_bos_base_bids|city_region_mapping|supply_res_ba_hub_data|cell_to_zone_mapping|sam_chains_new)
  group: rls_supply_am
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (rbac_supply_etls)
  table: res_comp_item_price
  group: (rls_logistics_fc|rls_city_leadership|rls_common_all|rls_logistics_all)
  privileges:
  - SELECT
  filter: cast(z_res_id as bigint) in (select res_id from jumbo_derived.res_access_control_mapping where email = current_user)
- type: table
  entity_category: data_entity
  schema: (rbac_etls|ads_etls|rbac_supply_etls)
  table: (res_access_control_mapping|res_ad_invoices_postpaid|res_ad_product_flags|res_addition_rejection_code_upload|res_ads_attributes|res_ads_billing|res_ads_br_details|res_ads_competition|res_ads_floor_cpc|res_ads_monthly_buckets|res_ads_monthly_flag|res_ads_renewal_campaigns|res_ads_ud_rca_base|res_aov_percentiles|res_attributes_incremental_rev|res_ba_hub_data|res_bos_keyword|res_bos_menu_scores|res_bos_rpi|res_brand_pack_performance|res_brand_pack_subscriptions|res_brandtile_v2|res_bulk_addition_code_upload|res_bulk_removal_code_upload|res_campaign_level_details|res_dish_cohorts_v2|res_distance_based_rc|res_dsz_l0_metrics|res_eligibility_count|res_eligible_searches|res_express_funnel_metrics|res_festival_item_projection_gmv|res_flexi_commission|res_floor_cpc|res_fssai_status_live_tracker|res_funnel|res_funnel_dropoff_base|res_funnel_hourly_unique|res_funnel_metrics_base|res_grow_max_eligible_list|res_hourly_visibility_stats|res_id_menu_moderation|res_input_db_details|res_item_kpt|res_item_visibility|res_kam_meeting_tracker|res_level_details_daily|res_level_festival_projection|res_level_inventory|res_level_saturation|res_level_sl|res_live_tracker|res_m2o_funnel|res_masthead_ads|res_menu_score|res_msa|res_mx_escalation_snapshot_kam|res_nka_rca|res_nrl_funnel|res_o2_promo_code_upload_log|res_offline_ads_metrics|res_ora|res_pair_searches|res_pox_metrics|res_pox_mterics_kams|res_promo_code_logs|res_promo_diy_rate_card_rt|res_promo_orders|res_promo_recommendation|res_rate_card_rt|res_rejection_reasons|res_removal_rejection_code_upload|res_search_count|res_searches_count|res_similar_search|res_smid|res_socialads_category_share|res_socialads_insights|res_socialads_nrl|res_stepper_offer|res_supply_menu_mix|res_txn_metrics|res_txn_tab|res_vp_plus_base|res_wb_cost_sharing|res_winback_complaints|res_winback_dashboard|res_winback_metrics|res_zvd_cool_down|res_campaign_level_recommendation|res_campaign_performance|res_cart_breaker_base|res_cart_breaker_score|res_cart_dropoff_item_lvl|res_catalogue_dish_tag|res_catalogue_quick_tag|res_comp_pc|res_cpc_exception_calculation|res_cpc_log|res_cuisines_share|res_raredrops_trynew_sales|res_fav_trynew_sales|res_grow_maxx_calculations)
  group: (rls_logistics_fc|rls_city_leadership|rls_common_all|rls_logistics_all|online_ordering_city_operations)
  privileges:
  - SELECT
  filter: cast(res_id as varchar) in (select cast(res_id as varchar) from jumbo_derived.res_access_control_mapping
    where email = current_user)

# TODO: Move this to key_account file
- type: table
  entity_category: data_entity
  schema: (rbac_etls|ads_etls|rbac_supply_etls)
  table: res_.*
  group: online_ordering_key_accounts
  privileges:
  - SELECT
  filter: cast(res_id as varchar) in (select cast(res_id as varchar) from jumbo_external.sam_chains_new)
- type: table
  entity_category: data_entity
  schema: (rbac_etls|ads_etls|rbac_supply_etls)
  table: res_.*
  group: (online_ordering_central_operations|online_ordering_central_supply|online_ordering_business_finance)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (support_service_prod|support_service_prod_v2|mongo_prod_logistics_communication_service)
  group: (online_ordering_city_logistics|online_ordering_central_logistics|online_ordering_city_operations|online_ordering_bharat_logistics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: insights_etls
  table: (dp_hourly_kpi_stats_weekly_v2|dp_hourly_kpi_stats_wtn_v2|city_logs_hygiene_stats_v2|city_kpi_stats_v2|dp_assets_compliance)
  group: (rls_logistics_fc|rls_city_leadership|rls_common_all|rls_logistics_all)
  privileges:
  - SELECT
  filter: cast(zone_id as varchar) in (SELECT cast(entity_id as varchar) zone_id FROM
    default.primary_access_control WHERE dt <>'' AND dt = (SELECT max(dt) FROM default.primary_access_control
    WHERE dt>= date_format(CURRENT_DATE - interval '2' DAY, '%Y%m%d')) AND entity_type='ZONE_ID'
    and entity_id is not null and email = current_user)
- type: table
  entity_category: data_entity
  schema: (support_service_prod)
  table: (survey_data|survey_records)
  group: nps
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo_derived)
  table: (nurture_res_commission)
  group: mx_tech
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (mx_etls)
  group: mx_tech
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (zoho_backup)
  group: zoho_backup
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (blinkit.*|realtime_enriched_store|.*.blinkit.*)
  group: blinkit.*
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (extzomatopromodb)
  group: extpromo
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (blinkit_jumbo2)
  table: (email_tracking|telecom_ivr_tracking|app_request_metrics|app_error_metrics|app_performance_metrics|sendgrid_tracking|ses_tracking|sms_tracking)
  group: (corporate_tech.*|zomato_corporate_engineering.*|eternal_corporate_engineering.*|online_ordering_tech.*|online_ordering_engineering.*|dining_out_engineering.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (blinkit_etls)
  table: (z_b_users_yearly_transactions)
  group: (online_ordering_marketing)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (zomatopromodb)
  table: (promo_codes_source)
  group: promo_raw
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (zomatopromodb)
  group: promo
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (hp_.*|ads_etls|jumbo_derived|jumbo_dashboard|zomato4|hyperpure_.*|dynamodb|hpexperiments|zomatopaydb|sms_service|kitchenservicedb|location|zomato_ads|zomato_ml)
  group: hyperpure.*
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo2|jumbo3)
  table: (cart_tracking|cart_updates|pageview_tracking|product_inventory_updates|jevent|search_tracking|hyperpure_clicks|driver_pings|autosuggest_tracking|restaurant_ratings|sms_tracking|bin_inventory_updates|unified_chat_events|auth_audit_logs|hyperpure_order_events|product_warehouse_price_mapping_events|hyperpure_ticket_events|purchase_order_events|hyperpure_entity_events|po_grn_mapping_events|hp_scm_event_tracking|hp_app_tracking|stock_transfer_order_events|hp_view_tracking|hp_predictions_tracking|hp_negative_margin_price|hyperpure_external_price_input|hp_search_experiment_tracking|bin_inventory_updates|hyperpure_location_tracking|hyperpure_.*|hp_.*)
  group: hyperpure.*
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: zomato_ml
  table: (hp_.*|dish_tree|dish_category_mapping|hyperpure_.*|instant_demand_forecast)
  group: hyperpure.*
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: zexperiments
  table: (o2_growth_experiments)
  group: hyperpure.*
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: jumbo_external
  table: (dsz_city_type_mapping|city_region_mapping|hp_.*|hyperpure_.*)
  group: hyperpure.*
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: mongo_merchant_service
  table: restaurant_merchant_map
  group: hyperpure.*
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (chat|chatdbv2)
  group: hyperpure_customer_delight
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (zomato4)
  table: (cities)
  group: uae_derived_tables
  privileges:
  - SELECT
  filter: country_id in (214)
- type: table
  entity_category: data_entity
  schema: (zomato4)
  table: (restaurants)
  group: uae_derived_tables
  privileges:
  - SELECT
  filter: city_id in (select city_id from zomato4.cities where country_id = 214)
- type: table
  entity_category: data_entity
  schema: (jumbo_derived)
  table: (zomato_order_history|uae_city_dsz_o2_funnel|uae_restaurant_dsz_funnel|restaurants|cities|content_search|dining_depth_score|search_base|dining_collection_impression)
  group: uae_derived_tables
  privileges:
  - SELECT
  filter: cast(country_id as integer) in (214)
- type: table
  entity_category: data_entity
  schema: (jumbo_derived)
  table: (dining_priority|zopay_funnel_events|collections_data_post_see_all|go_out_tab_features|dining_tab_features|go_out_tab_events|dining_gold_supply|res_menu|dining_district_restaurant_funnel)
  group: uae_derived_tables
  privileges:
  - SELECT
  filter: city_id in (select city_id from zomato4.cities where country_id = 214)
- type: table
  entity_category: data_entity
  schema: (jumbo_derived)
  table: (collection_res_taps|content_collections_traffic)
  group: uae_derived_tables
  privileges:
  - SELECT
  filter: cityid in (select city_id from zomato4.cities where country_id = 214)
- type: table
  entity_category: data_entity
  schema: (jumbo_derived|mx_etls|zomatoreviewsdb)
  table: (dining_res_menus|mx_onboarding_backend_events|dining_content_support_inflow|user_rating_inline)
  group: uae_derived_tables
  privileges:
  - SELECT
  filter: cast(res_id as VARCHAR) in (select res_id from zomato4.restaurants where
    city_id in (select city_id from zomato4.cities where country_id = 214))
- type: table
  entity_category: data_entity
  schema: (jumbo2)
  table: (restaurant_ratings)
  group: uae_derived_tables
  privileges:
  - SELECT
  filter: city_id in (select city_id from zomato4.cities where country_id = 214)
- type: table
  entity_category: data_entity
  schema: (jumbo_derived)
  table: (subzones)
  group: uae_derived_tables
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (zomatopaydb|secretx_metrics)
  table: (zpay_restaurants|zpay_restaurant_entity_toggles|sx_ticket_metrics)
  group: uae_derived_tables
  privileges:
  - SELECT
  filter: cast(res_id as VARCHAR) in (select res_id from zomato4.restaurants where
    city_id in (select city_id from zomato4.cities where country_id = 214))
- type: table
  entity_category: data_entity
  schema: (zomato4)
  table: (attributes_restaurants_mapping|dineout_metafilter_res_mapping|dining_restaurants|restaurants_comments_history)
  group: uae_derived_tables
  privileges:
  - SELECT
  filter: cast(res_id as VARCHAR) in (select res_id from zomato4.restaurants where
    city_id in (select city_id from zomato4.cities where country_id = 214))
- type: table
  entity_category: data_entity
  schema: (zomato4)
  table: (zomato_events|zomato_events_types_mapping|zomato_events_type|users|dineout_metafilters|user_collection_entities)
  group: uae_derived_tables
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (zomato4)
  table: (zomato_events_restaurants_mapping|phones)
  group: uae_derived_tables
  privileges:
  - SELECT
  filter: cast(entity_id as VARCHAR) in (select res_id from zomato4.restaurants where
    city_id in (select city_id from zomato4.cities where country_id = 214))
- type: table
  entity_category: data_entity
  schema: (billing_db|zbilling|ztransactions|zomatodotedb|hp_finance_etls|aurora_billing_db|blinkit_aurora_billing_db)
  group: (hyperpure_finance|hyperpure_business_finance)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (chat|jumbo_derived|zomato4|chatdbv2)
  group: (table_reservations.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo_derived|jumbo_external|jumbo2|location|menuservicedb|secretx_metrics|zomato4||zomato_content|zomatogolddb|zomatophotos|zomatoreviewsdb|storiesservicedb|search_service|zomato_ml|videodb)
  group: listings.*
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (dynamodb|jumbo_dashboard|jumbo_derived|jumbo_external|jumbo2|jumbo3|menuservicedb|notification_service_prod|share_service|zomato_voice|zomato4|zomatogolddb|zomatopaydb|zomatophotos|zomatoreviewsdb|zomato_ml|reward_service|storiesservicedb|zexperiments|zomato_sales|mongo_rate_card_service|zomatopromodb|videodb)
  group: (dining_out.*|district_corporate.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (chat|chatdbv2|dynamodb|edition|gold_etls|jumbo_dashboard|jumbo_derived|jumbo_external|jumbo2|jumbo3|location|zexperiments|zomato4|zomatoexperimentsdb|zomatogolddb|zomatopaydb|zomatoreviewsdb|reward_service|storiesservicedb|videodb)
  group: pro.*
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (accounting_production|ads_etls|carthero_prod|carthero_stats_prod|carthero_onboarding_prod|chat|chatdbv2|delivery_composite_order_service|delivery_consumer_order_service|diy_db|driver_service|dynamodb|gold_etls|jumbo_dashboard|jumbo_derived|jumbo_external|jumbo_insights|jumbo2|jumbo3|location|logistics|logs_dashboard_etls|menuservicedb|support_service_prod|zexperiments|zintegrations|zomato_ads|zomato_ml|zomato_sales|zomato4|zomatogolddb|zomatopaydb|zomatoreviewsdb|mongo_ads|mongo_merchant_service|mongo_rate_card_service|mongo_support_service_prod|search_service|marketing_etls|driver_shift|delivery_logistics_order_service|zomatophotos|zomato4|supply_etls|reward_service|dynamodb|storiesservicedb|zomatopromodb|online_ordering|notification_service_prod|zomatoexperimentsdb|videodb)
  group: (sales.*|supply.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (accounting_production|accounting_production_archive|ads_etls|carthero_driveracc_prod_archive_archive|carthero_merchant_prod|carthero_mongo|carthero_onboarding_prod|carthero_prod|carthero_stats_prod|chat|chatdbv2|diy_db|driver_service|driver_shift|dynamodb|jumbo_dashboard|jumbo_derived|jumbo_external|jumbo_insights|jumbo2|jumbo3|leads_master.*|location|location_etls|logistics|logs_dashboard_etls|logs_etls|marketing_etls|menuservicedb|mongo_carthero_mongodb_prod|mongo_ads|mongo_tickets|mongo_carthero_order_details|mongo_driver_onboarding_service_production|mongo_merchant_service|mongo_rule_engine_prod|mongo_support_service_prod|notification_service_prod|referral_partner_prod|search_service|secretx_metrics|support_service_prod|tonguestun|zexperiments|zintegrations|zomato_ads|zomato_ml|zomato_sales|zomato4|zomatogolddb|zomatopaydb|zomatophotos|zomatoreviewsdb|rider_training_prod|reward_service|mongo_prod_feature_toggle|mongo_driver_accounting_engine|mongo_ads_target_service|zomalanddb|logistics_bonus|mongo_chat|delivery_consumer_order_service|product_analytics_etls|online_ordering|realtime_enriched_store|sms_service|zerp_etls|storiesservicedb|zomatopromodb|zomatostoriesdb|supply_etls|order_fulfillment_service_walker|instant_etls|zomatoexperimentsdb|insights_etls|mongo_drivers_db|videodb|groundintelligencedb|zomatorulesdb)
  group: (corporate.*|online_ordering.*|zomato.*|eternal_corporate.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: arroyo
  table: (firehose_online_ordering_events|firehose_search_salience|firehose_search_salience_v2)
  group: (online_ordering.*|eternal_corporate.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (revenue_etls)
  table: (fpa_do_fsa_rev)
  group: (dining_finance)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (revenue_etls)
  table: (fpa_nov)
  group: (ds_revenue_etls)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (revenue_etls)
  table: (fpa_do_ads|fpa_do_daily|fpa_do_fsa_rev|do_res_live_base)
  group: (dining_out_business_finance|dining_out_finance)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (billing_db|cbilling_etls|delivery_composite_order_service|delivery_consumer_order_service|diy_billing_db|revenue_etls|zbilling|zomatodotedb|zomatopaydb|ztransactions|delivery_logistics_order_service|blinkit_billing|aurora_billing_db|blinkit_aurora_billing_db|hyperpure_etls)
  group: (eternal_corporate_finance|zomato_corporate_finance|corporate_finance|online_ordering_finance|online_ordering_business_finance|zomato_corporate_business_finance|corporate_business_finance|zomato_payments.*|eternal_corporate_business_finance)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (cbilling_etls)
  group: (cbilling_etls)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (ads|ads_etls|carthero_prod_archive|delivery_composite_order_service|delivery_consumer_order_service|edition|experimentationdb|flywheeladsdb|infinitydb|mongo_rate_card_service|online_ordering|jumbo2|jumbo3|zomato_ml|reward_service|rider_training_prod|simulation_carthero_prod|sms_service|storeservicedb|userservicedb|zomato_content|zomato_etl|zomatoauthdb|zomatoexperimentsdb|zpush|delivery_logistics_order_service|groundintelligencedb|store_service)
  group: (corporate_tech.*|zomato_corporate_engineering.*|eternal_corporate_engineering.*|online_ordering_tech.*|online_ordering_engineering.*|dining_out_engineering.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (.*dynamo.*|.*mongo.*)
  table: (.*audit.*)
  group: (corporate_tech.*|zomato_corporate_engineering.*|eternal_corporate_engineering.*|online_ordering_tech.*|online_ordering_engineering.*|dining_out_engineering.*|eternal_corporate_governance_risk_compliance|hyperpure_engineering|hyperpure_governance_risk_compliance)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (.*playground.*)
  group: (corporate_tech.*|zomato_corporate_engineering.*|.*corporate_engineering.*|online_ordering_tech.*|online_ordering_engineering.*|dining_out_engineering.*|eternal_corporate_governance_risk_compliance|hyperpure_engineering|hyperpure_governance_risk_compliance)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (billing_db|cbilling_etls|diy_billing_db|zbilling|ztransactions|wallet|payments_etls|revenue_etls|blinkit_billing|aurora_billing_db|blinkit_aurora_billing_db)
  group: zomato_finance
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (aurora_billing_db|billing_db|cbilling_etls)
  table: (invoice_details|billing_ledger|invoice_meta|entity_invoice_mapping|invoice_details)
  group: dining_finance
  privileges:
  - SELECT
  filter: business_id in ('ZM_CL', 'ZM_CLPRO', 'ZM_TR', 'ZM_ADS')
- type: table
  entity_category: data_entity
  schema: (aurora_billing_db|billing_db|cbilling_etls)
  table: (invoice_details|billing_ledger|payout_details_v2)
  group: events_finance
  privileges:
  - SELECT
  filter: business_id in ('ZM_EVENTS', 'ZM_CL', 'ZM_CLPRO',  'ZM_TR', 'ZM_SHOP')
- type: table
  entity_category: data_entity
  schema: (aurora_billing_db|billing_db|cbilling_etls|revenue_etls)
  table: (payout_details_v2|billing_ledger|onboarding_fee_o2)
  group: o2_finance
  privileges:
  - SELECT
  filter: business_id in ('ZM_O2')
- type: table
  entity_category: data_entity
  schema: (aurora_billing_db)
  table: (invoice_details)
  group: flywheel_finance
  privileges:
  - SELECT
  filter: business_id in ('ZM_SADS')
- type: table
  entity_category: data_entity
  schema: (payments_etls)
  group: payments_etls
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (ztransactions)
  group: ztransactions
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (edition|ztransactions|wallet|payments_etls)
  group: zomato_payments.*
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo_derived|zomato4|zomatogolddb|zomatopaydb)
  group: zomato_portugal
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo2|jumbo3)
  table: (pageview|userlifecycle)
  group: zomato_portugal
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: revenue_etls
  table: (bizfin_cm|buzfin_legends_revenue_etl|fpa_gm_pnl|gmpl_overheads_manuals|india_res_billing_utr_details|daily_sam_pnl|cm_playbook_v1)
  group: bizfin
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (billing_db|cbilling_etls)
  table: (billing_ledger|payout_details|payout_details_v2)
  group: uae_billing
  privileges:
  - SELECT
  filter: country_id in (214)
- type: table
  entity_category: data_entity
  schema: billing_db
  table: (billing_breakup|payout_order_mapping)
  group: uae_billing
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: revenue_etls
  table: (fpa_zoh_base|fpa_mto_base|fpa_promo_final|fpa_revenue_mxr|fpa_cx_comp|fpa_daily_ads|cx_penalty_pg|daily_dsz_pnl|lcpo_updated|fpa_lcpo|pnl_logs_q1|fpa_gm_pnl|bizfin_logs_cost_per_order|fpa_promo_requested|fpa_cb_gold_rev|cm_playbook_v1|fpa_dc_breakup|fpa_mx_refund_detailed|fpa_city_ads|pnl_manual|fpa_mx_refund_detailed_v2)
  group: revenue_cm
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: revenue_etls
  table: (fpa_cx_comp_detailed|fpa_revenue_mxr)
  group: pnl_cd_refunds
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: aurora_billing_db
  table: (s3path_file_mapping)
  group: grc
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (reward_service)
  group: (corporate_partnerships|online_ordering_partnerships)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (jumbo_derived|jumbo_dashboard|kitchenservicedb|hp_wms|jumbo2|jumbo3|dynamodb|zomatoreviewsdb|delivery_consumer_order_service|menuservicedb|jumbo_external|instant_etls|zomato4|zomatoreviewsdb|chat|chatdbv2)
  group: (instant_food.*)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: (carthero_prod)
  table: (delivery_driver_service_mappings|delivery_drivers)
  group: (instant_food.*)
  privileges:
  - SELECT
# - type: table
#   entity_category: data_entity
#   privileges:
#   - SELECT
- type: query
  entity_category: query_engine_entity
  allow:
  - EXECUTE
  - KILL
  - VIEW
- type: system_information
  entity_category: query_engine_entity
  allow:
  - READ
  - WRITE
  user: (jumbo-admin|dwh_admin)

- type: function # This is a procedure but treated as function
  entity_category: query_engine_entity
  user: "jumbo-admin"
  catalog: ".*"
  schema: ".*"
  procedure: ".*"
  privileges: ["EXECUTE"]

- type: function # This is a procedure but treated as function
  entity_category: query_engine_entity
  user: ".*"
  catalog: ".*"
  schema: ".*"
  procedure: "flush_metadata_cache"
  privileges: ["EXECUTE"]

- type: function
  entity_category: query_engine_entity
  user: "jumbo-admin"
  catalog: ".*"
  schema: ".*"
  function: ".*"
  privileges: ["EXECUTE", "GRANT_EXECUTE", "OWNERSHIP"]

- type: session_property_operation
  entity_category: data_entity
  user: "<EMAIL>"
  catalog: ".*"
  schema: ".*"
  property: "(query_max_execution_time|query_max_run_time)"
  allow: true

- type: session_property_operation
  entity_category: data_entity
  user: ".*"
  catalog: ".*"
  schema: ".*"
  property: "(dynamic_filtering_wait_timeout)"
  allow: true

- type: impersonation
  entity_category: query_engine_entity
  original_user: (adhoc_jhub|bi_redash|blinkit_superset|bi_common|blinkit_backend_application)
  new_user: (^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+)
  allow: true

# Global Select Rules
# These rules are applied to all users by default, unless overridden by more specific rules.
- type: table
  entity_category: data_entity
  schema: (default|jumbo_derived)
  table: (primary_access_control|res_access_control_mapping)
  privileges:
  - SELECT

# Supply & MX Related Rules
- type: table
  entity_category: data_entity
  schema: mx_etls
  group: (online_ordering_central_.*|online_ordering_city_.*|online_ordering_bharat_supply|online_ordering_bharat_operations|online_ordering_product_analytics)
  privileges:
  - SELECT

# Flywheel lob
- type: table
  entity_category: data_entity
  schema: (flywheel_etls|flywheeladsdb)
  group: (online_ordering_flywheel|online_ordering_key_accounts|online_ordering_engineering|flywheel_finance)
  privileges:
  - SELECT

# Insights Service Rules
- type: table
  entity_category: data_entity
  catalog: hive
  schema: ads_etls
  user: <EMAIL>
  priority: 10
  table: (campaign_performance|campaign_targeting_segments)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: insights_etls
  user: <EMAIL>
  priority: 10
  table: (ba_hub_ads_analytics|merchant_dashboard_user_ads_analytics)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: mongo_ads
  user: <EMAIL>
  priority: 10
  table: (campaigns)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: z_cdp_etls
  user: <EMAIL>
  priority: 10
  table: (user_order_stats_lifetime_fullsync|user_order_stats_lifetime_incremental)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: insights_etls
  user: <EMAIL>
  priority: 10
  table: (cbilling_ledgers_pe|cbilling_payout_trend_alert_table)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: z_cdp_etls
  user: cdp_application
  priority: 10
  table: (user_app_open_stats_fullsync|user_app_open_stats_incremental|user_gold_end_date_fullsync|user_gold_end_date_incremental|user_order_stats_daily_fullsync|user_order_stats_daily_incremental|user_order_stats_lifetime_fullsync|user_order_stats_lifetime_incremental|user_order_stats_static_fullsync|user_order_stats_static_incremental|user_region_daily_fullsync|user_region_daily_incremental|user_zomato_details_fullsync|user_zomato_details_incremental)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: blinkit
  schema: dwh
  user: <EMAIL>
  priority: 10
  table: (dim_product|fact_sales_order_item_details)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: blinkit
  schema: lake_events
  user: <EMAIL>
  priority: 10
  table: (mobile_event_data)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: blinkit_iceberg
  schema: dwh
  user: <EMAIL>
  priority: 10
  table: (dim_product|fact_sales_order_item_details)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: flywheel_etls
  user: <EMAIL>
  priority: 10
  table: (blinkit_ad_device|blinkit_meta_ad_info|meta_insights_blinkit|o2_user_res_data_mapping|z_flywheel_audience)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: jumbo_derived
  user: <EMAIL>
  priority: 10
  table: (res_cuisine_dsz_mapping|user_base_mapping|user_dsz_mapping|user_platform_properties|user_properties_cuisine_level)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: jumbo_derived
  user: <EMAIL>
  priority: 10
  table: (driver_vehicle_mapping|vendor_payouts)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: rbac_logistics_etls
  user: <EMAIL>
  priority: 10
  table: (city_delivery_drivers_master)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: hp_wms
  user: <EMAIL>
  priority: 10
  table: (bin_inventory_details|product|product_company_mapping)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: hp_wms_cdc
  user: <EMAIL>
  priority: 10
  table: (bin_inventory_details|putaway_list|putaway_list_item)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: insights_etls
  user: <EMAIL>
  priority: 10
  table: (enterprise_daily_stats|enterprise_stats|res_o2_payout_details|res_related_entities|res_status_logs_daily_sync|mx_enterprise_stats|ba_hub_analytics)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: mx_etls
  user: <EMAIL>
  priority: 10
  table: (mx_order_history)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: insights_etls
  user: <EMAIL>
  priority: 10
  table: (rbr_post_ob|rbr_pre_ob)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: (jumbo2|blinkit_jumbo2)
  user: <EMAIL>
  priority: 10
  table: (email_tracking|sms_tracking|telecom_call_tracking|telecom_ivr_tracking|whatsapp_tracking)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: z_cdp_etls
  user: <EMAIL>
  priority: 10
  table: (user_app_open_stats_fullsync)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: z_cdp_etls
  user: <EMAIL>
  priority: 10
  table: (user_app_open_stats_fullsync|user_app_open_stats_incremental|user_gold_end_date_fullsync|user_gold_end_date_incremental|user_order_stats_daily_fullsync|user_order_stats_daily_incremental|user_order_stats_lifetime_fullsync|user_order_stats_lifetime_incremental|user_order_stats_static_fullsync|user_order_stats_static_incremental|user_region_daily_fullsync|user_region_daily_incremental|user_zomato_details_fullsync|user_zomato_details_incremental)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: rbac_logistics_etls
  user: <EMAIL>
  priority: 10
  table: (city_delivery_drivers_master)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: insights_etls
  user: <EMAIL>
  priority: 10
  table: (weather_device_enriched_data)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: z_cdp_etls
  user: zdp-common-python-etls
  priority: 10
  table: (user_app_open_stats_fullsync)
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  catalog: hive
  schema: genai_etls
  group: gen_ai
- type: session_property_operation
  entity_category: data_entity
  group: insights_service_users
  catalog: ".*"
  schema: ".*"
  property: "(query_max_execution_time|query_max_run_time)"
  allow: true
- type: table
  entity_category: data_entity
  catalog: (district.*|blinkit*|zomato.*)
  schema: ".*"
  user: bi_tableau
  table: ".*"
  privileges:
    - SELECT
- type: table
  entity_category: data_entity
  schema: (cdp|zomato_cdp)
  user: cdp_application
  privileges:
  - DELETE
  - GRANT_SELECT
  - INSERT
  - OWNERSHIP
  - SELECT
  - UPDATE
- type: table
  entity_category: data_entity
  catalog: (district.*|zomato.*)
  schema: (movies_etls|district_etls|jumbo_derived|jumbo2)
  table: (movies_orders_history|zomato_do_trxn_history|going_out_user_properties|reward_credit_events|z_b_uid_mapping)
  user: cdp_application
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  schema: dwh_etls
  group: dwh
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: zomato_iceberg
  schema: dynamodb
  table: (.*blinkit.*|pharma_service|inventory_reports_service|prod_lead_management_service|prod_weather_service|prod_driver_service|instructions)
  group: (blinkit_default|blinkit_finance)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (lake_events|viz)
  table: (order_lifecycle_events|realtime_current_day_sales_data)
  group: (blinkit_default|blinkit_finance)
  privileges: []
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (dwh|dwh_hpl|bistro_etls)
  table: (fact_sales_order_details_bistro|fact_sales_order_item_details_bistro|fact_sales_order_details_hpl|fact_sales_order_item_details_hpl|business_performance_daily_consumer_metrics|metrics_supply_chain_view_board|fact_sales_order_item_details|fact_sales_order_details|dim_customer|dim_merchant_polygon)
  group: (dynamic_rule:blinkit.*\|(dwh|dwh_hpl|bistro_etls)\|(fact_sales_order_details_bistro|fact_sales_order_item_details_bistro|fact_sales_order_details_hpl|fact_sales_order_item_details_hpl|business_performance_daily_consumer_metrics|metrics_supply_chain_view_board|fact_sales_order_item_details|fact_sales_order_details|dim_customer|dim_merchant_polygon):table)
  privileges:
  - GRANT_SELECT
  - SELECT
  columns:
  - name: rm
    allow: true
    mask: '0'
  - name: percentage_retained_margin
    allow: true
    mask: '0'
  - name: total_weighted_landing_price
    allow: true
    mask: '0'
  - name: total_retained_margin
    allow: true
    mask: '0'
  - name: unit_retained_margin
    allow: true
    mask: '0'
  - name: unit_weighted_landing_price
    allow: true
    mask: '0'
