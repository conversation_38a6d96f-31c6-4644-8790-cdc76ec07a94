- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  user: (blinkit_superset)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit)
  schema: (viz)
  table: (dwh_daily_ptype_inventory_sales_data)
  group: (blinkit_store_core_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (storeops_etls_.*|logistics_data_etls_.*|dwh_absolute_hourly_inventory_availability_perishables|dwh_absolute_hourly_inventory_availability_fnv|ss_hygiene_data_summary|ss_hygiene_data_detail|ss_hygiene_data_cash_difference)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (cd_etls_.*)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (supply_etls_frozen_city_daily_wtd_avail|supply_etls_frozen_outlet_daily_wtd_avail|supply_etls_frozen_city_item_daily_wtd_avail|supply_etls_frozen_city_hourly_wtd_avail|supply_etls_frozen_outlet_hourly_wtd_avail|supply_etls_store_hourly_weighted_availability|consumer_etls_society_tracker|consumer_etls_rca_weekly_view|supply_etls_frozen_backend_tea_tagging|supply_etls_frozen_backend_po|supply_etls_frozen_backend_view_summary|consumer_etls_sccd_asp|consumer_etls_p0_metrics_dau|consumer_etls_sccd_l0_conv|consumer_etls_user_bucket_dau|consumer_etls_superset_city_keyword_conversion_weekly|consumer_etls_growth_dashboard_1|consumer_etls_city_level_business_health_metrics|consumer_etls_city_level_store_metrics|consumer_etls_city_level_business_health_metrics_bounds|consumer_etls_sccd_null_searches|consumer_etls_sccd_l0_upscaled_conv|consumer_etls_sccd_l0_cr_conv|consumer_etls_sccd_ptype_cr_conv|consumer_etls_sccd_item_sales|consumer_etls_sccd_ptype_avail|consumer_etls_sccd_ptype_hourly_avail|consumer_etls_sccd_ptype_conv|consumer_etls_sccd_l0_wtd_avail)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (infra_expansion_etls_.*)
  group: (blinkit_expansion_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (cd_etls_product_complaints_wow_l2|cd_etls_customer_comment_store_date|cd_etls_product_complaints_category_trend|cd_etls_cd_product_store_dashboard_wow_pid_store|cd_etls_product_store_dashboard_wow_pid_store_complaint_type|cd_etls_product_complaints_last_30_d_complaint_type|cd_etls_product_complaints_wow_l2_complaint_type|cd_etls_product_complaints_last_30_d)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (category_etls_.*|supply_etls_.*)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (supply_etls_store_hourly_weighted_availability|supply_etls_frozen_city_daily_wtd_avail|supply_etls_frozen_city_hourly_wtd_avail|supply_etls_frozen_outlet_daily_wtd_avail|supply_etls_frozen_outlet_daily_wtd_avail|supply_etls_frozen_city_item_daily_wtd_avail)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (viz)
  table: (consumer_etls_jan_festive_be_fe_inv|consumer_etls_jan_festive_be_inv|category_etls_festive_sales_daily|category_etls_festive_sales_daily|category_etls_festive_daily_conversion|category_etls_festive_daily_conversion)
  group: (blinkit_category_default)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit.*)
  schema: (viz)
  table: (category_etls_fact_sales_order_item_details|category_etls_fact_sales_order_details|category_etls_daily_product_sales)
  group: (blinkit_category_sales)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (dwh_agg_daily_search_conversion_keyword|consumer_etls_society_tracker)
  group: (blinkit_growth_metrics)
  privileges:
  - SELECT

- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (consumer_etls_ds_wh_city_level_projections_dod)
  group: (blinkit_warehouse_metrics)
  privileges:
  - SELECT
  filter: level = 'Warehouse'
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (warehouse_etls_.*|warehouse_etls_rsto_.*)
  group: (blinkit_warehouse_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (supply_etls_festive_metrics_temp_viz_.*|supply_etls_be_inventory_metrics_.*|warehouse_hourly_facility_ds_zone_level_view|warehouse_hourly_facility_zone_level_view|warehouse_hourly_facility_ds_level_view|supply_etls_cpu_view|supply_etls_fleet_master_data_view)
  group: (blinkit_warehouse_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (.*seller.*)
  group: (blinkit_seller_hub_metrics)
  privileges:
  - SELECT

- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit)
  schema: (viz)
  table: (supply_etls_store_hourly_weighted_availability)
  group: (blinkit_inventory_fresh_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (consumer_etls_bistro_rating|consumer_etls_bistro_reviews)
  group: (blinkit_zomato_cd_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (supply_etls_.*)
  group: (blinkit_replenishment_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (.*bistro.*)
  group: (blinkit_bistro_metrics)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (realtime_current_day_sales_data)
  group: (blinkit_realtime)
  privileges:
  - SELECT
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (realtime_current_day_sales_data)
  group: (blinkit_finance)
  privileges: []






######################################################################## RLS rules ##############################################################






- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (category_etls_.*_rls_l0l1cat)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
  filter: concat(CAST(l0_id AS VARCHAR), '_', CAST(l1_id AS VARCHAR)) IN ( SELECT
    concat(CAST(l0_category_id AS VARCHAR), '_', CAST(l1_category_id AS VARCHAR))
    FROM dwh.category_superset_redash_access_map WHERE email_id = current_user ) OR
    -1 = (select min(l0_category_id) from dwh.category_superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (ba_etls_fin_daily_pnl|ba_etls_fin_daily_margin)
  group: (blinkit_default|blinkit_category_metrics)
  privileges:
  - SELECT
  filter: ('-1' = (select min(category) from dwh.finance_category_superset_redash_access_map
    WHERE email_id=current_user) AND concat(cast(l0_category_id as varchar),cast(l1_category_id
    as varchar)) IN (select concat(cast(l0_category_id as varchar),cast(l1_category_id
    as varchar)) FROM dwh.finance_category_superset_redash_access_map WHERE email_id=current_user))
    OR ('-1-1' = (select concat(cast(min(l0_category_id) as varchar),cast(min(l1_category_id)
    as varchar)) FROM dwh.finance_category_superset_redash_access_map WHERE email_id=current_user)
    AND category in (select category FROM dwh.finance_category_superset_redash_access_map
    WHERE email_id=current_user)) OR ('-1-1-1' = (select concat(cast(min(l0_category_id)
    as varchar),cast(min(l1_category_id) as varchar),cast(min(category) as varchar))
    FROM dwh.finance_category_superset_redash_access_map WHERE email_id=current_user))
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (category_etls_sales_dashboard_monthly_tab_data|category_etls_fbg_daily_sales|category_etls_daily_sales|category_etls_fact_sales_order_item_details|category_etls_pricing_domain_productchangelog)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
  filter: concat(CAST(l0_id AS VARCHAR), '_', CAST(l1_id AS VARCHAR)) IN ( SELECT
    concat(CAST(l0_category_id AS VARCHAR), '_', CAST(l1_category_id AS VARCHAR))
    FROM dwh.category_superset_redash_access_map WHERE email_id = current_user ) OR
    -1 = (select min(l0_category_id) from dwh.category_superset_redash_access_map
    where email_id = current_user) OR manufacturer IS NULL OR manufacturer ='-' OR
    manufacturer IN (SELECT manufacturer FROM blinkit_iceberg.dwh.category_superset_redash_access_map
    WHERE email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (warehouse_etls_.*_rls_facility_id|supply_etls_.*_rls_facility_id)
  group: (blinkit_default|blinkit_warehouse_metrics|blinkit_store_metrics)
  privileges:
  - SELECT
  filter: facility_id IN ( SELECT facility_id FROM dwh.warehouse_superset_redash_access_map
    WHERE email_id = current_user ) OR -1 = (select min(facility_id) from dwh.warehouse_superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (supply_etls_vuelogix_tickets|supply_etls_ideabyte_fleet_temp_graph|supply_etls_ideabyte_fleet_temperature|supply_etls_frozen_wh_temperature|supply_etls_frozen_backend_tea_tagging|supply_etls_frozen_backend_po|supply_etls_frozen_backend_view_summary)
  group: (blinkit_default|blinkit_warehouse_metrics)
  privileges:
  - SELECT
  filter: facility_id IN ( SELECT facility_id FROM dwh.warehouse_superset_redash_access_map
    WHERE email_id = current_user ) OR -1 = (select min(facility_id) from dwh.warehouse_superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit)
  schema: (viz)
  table: (category_etls_fnv_hourly_metrics_view|category_etls_fnv_inventory_view|category_etls_fnv_dump_view|category_etls_fnv_store_level_data|category_etls_fnv_store_ptype_level_data)
  group: (blinkit_inventory_fresh_metrics)
  privileges:
  - SELECT
  filter: city IN (SELECT city FROM dwh.fnv_city_superset_redash_access_map WHERE
    email_id = current_user) OR '-1' = (select min(city) from dwh.fnv_city_superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (cd_etls_daily_cancel|consumer_etls_last_15_days_non_converting_searches)
  group: (blinkit_store_metrics)
  privileges:
  - SELECT
  filter: (city_name IN (select city_name from blinkit_iceberg.dwh.superset_redash_access_map
    mt where current_user = mt.email_id and active_flag)) OR ('Overall' = (select
    city_name from blinkit_iceberg.dwh.superset_redash_access_map mt where current_user
    = mt.email_id and city_name = 'Overall' and active_flag))
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (storeops_etls_.*_rls_outlet_id|logistics_data_etls_.*_rls_outlet_id)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
  filter: outlet_id in (select outlet_id from dwh.superset_redash_access_map where
    email_id = current_user) or -1 = (select min(outlet_id) from dwh.superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (storeops_etls_superset_od_slots|serviceability_etls_block_reasons|storeops_etls_realtime_picker_utilisation|storeops_etls_receiver_daily_esto_transfer_loss_invoice_item_details|storeops_etls_sender_daily_esto_transfer_loss_invoice_item_details|storeops_etls_daily_esto_transfer_loss|storeops_etls_agg_hourly_outlet_instore_metrics|storeops_etls_agg_hourly_city_instore_metrics|logistics_data_etls_lm_cpo_v1_based_daily|logistics_data_etls_weekly_fod_outlet_cohort|storeops_etls_order_level_complaint_rca|logistics_data_etls_rider_ob_funnel_view|serviceability_etls_surge_reasons|serviceability_etls_surge_seen_hourly|logistics_data_etls_dh_c2a_surge_bifercation|logistics_data_etls_lm_cpo_metric|logistics_data_etls_operation_metrics|logistics_data_etls_dp_live_logins|logistics_data_etls_logins_delta|storeops_etls_employee_metrics_daily|storeops_etls_employee_metrics_hourly|storeops_etls_instore_metrics_daily_level|storeops_etls_instore_metrics_hourly_level|dwh_bv_scm_deviation_view_tableau|dwh_bv_scm_metrics_tableau|logistics_data_etls_rider_weekly_churn|logistics_data_etls_store_distance_rca|logistics_data_etls_dp_work_details|logistics_data_etls_rider_onboarding|storeops_etls_new_employee_metrics|storeops_etls_store_wastage|storeops_etls_instore_return_loss_metrics|storeops_etls_instore_refund_metrics|storeops_etls_transfer_loss_metrics|storeops_etls_instore_loss_metrics|logistics_data_etls_hourly_time_legs|logistics_data_etls_lm_ops|storeops_etls_location_impact_on_store_ppi|storeops_etls_location_with_high_ppi|storeops_etls_sku_with_bad_ppi|storeops_etls_employee_level_ppi|storeops_etls_complaint_summary_order_level|storeops_etls_complaint_summary_product_level|storeops_etls_test_location_cohorts|logistics_data_etls_hourly_supply_metrics|logistics_data_etls_store_hourly_orders_dp_logins|storeops_etls_goals_sm_cm_weekly|storeops_etls_goals_sm_cm_daily|storeops_etls_packaged_grn|storeops_etls_hp_fresh_grn|logistics_data_etls_blinkit_store_offers_cohorts_login|logistics_data_etls_rider_daily_performance_summary|logistics_data_etls_rider_ob_funnel_bifurcation|logistics_data_etls_b_store_rider_cohorts_weekly_performance|logistics_data_etls_fc_fleet_view|logistics_data_etls_fact_rider_weekly_quality_metrics|logistics_data_etls_fact_rider_weekly_eph|storeops_etls_cluster_level_performance|consumer_etls_store_level_opa_wow|consumer_etls_store_level_opb_wow|storeops_etls_category_hourly_putaway|storeops_etls_fnv_ptype_summary|storeops_etls_daily_agg_outlet_fnv_data|storeops_etls_daily_outlet_sla_tracker|logistics_data_etls_daily_rider_nps_tag_level_summary|logistics_data_etls_daily_rider_nps_summary|consumer_etls_store_level_business_health_metrics|consumer_etls_store_level_bhm_wow|storeops_etls_hourly_store_crates_details|storeops_etls_hourly_employee_summary|storeops_etls_open_ds_variance|logistics_data_etls_weekly_last_mile_fod_funnel|storeops_etls_instore_headcount_hiring_summary|storeops_etls_goals_sm_cm_monthly|storeops_etls_agg_weekly_outlet_instore_metrics|storeops_etls_agg_hourly_outlet_instore_metrics|storeops_etls_agg_hourly_storeops_realtime_metrics|storeops_etls_agg_hourly_storeops_realtime_metrics_till_hour)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
  filter: outlet_id in (select outlet_id from dwh.superset_redash_access_map where
    email_id = current_user) or -1 = (select min(outlet_id) from dwh.superset_redash_access_map
    where email_id = current_user)
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (consumer_etls_ds_wh_city_level_projections_dod)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
  filter: ( outlet_id IN (select outlet_id from blinkit_iceberg.dwh.superset_redash_access_map
    mt where current_user = mt.email_id and active_flag) AND city_name IN (select
    city_name from blinkit_iceberg.dwh.superset_redash_access_map mt where current_user
    = mt.email_id and active_flag) ) OR ( -1 = (select MIN(outlet_id) from blinkit_iceberg.dwh.superset_redash_access_map
    mt where current_user = mt.email_id and active_flag) AND 'Overall' = (select city_name
    from blinkit_iceberg.dwh.superset_redash_access_map mt where current_user = mt.email_id
    and city_name = 'Overall' and active_flag) ) OR ( 0 = (select MIN(outlet_id) from
    blinkit_iceberg.dwh.superset_redash_access_map mt where current_user = mt.email_id
    and active_flag and outlet_id <> -1) AND 'Overall' = (select city_name from blinkit_iceberg.dwh.superset_redash_access_map
    mt where current_user = mt.email_id and city_name = 'Overall' and active_flag)
    AND level = 'Pan_india' )
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (consumer_etls_city_level_hourly_projections|consumer_etls_hourly_projections_actuals_view1)
  group: (blinkit_default|blinkit_store_metrics)
  privileges:
  - SELECT
  filter: (city IN (select city_name from blinkit_iceberg.dwh.superset_redash_access_map
    mt where current_user = mt.email_id and active_flag)) OR ('Overall' = (select
    city_name from blinkit_iceberg.dwh.superset_redash_access_map mt where current_user
    = mt.email_id and city_name = 'Overall' and active_flag)) OR ('Overall' = (select
    city_name from blinkit_iceberg.dwh.superset_redash_access_map mt where current_user
    = mt.email_id and city_name = 'Overall' and active_flag))
- type: table
  entity_category: data_entity
  catalog: (blinkit_iceberg|blinkit|blinkit_hudi)
  schema: (viz)
  table: (fbg_mtd_lmtd_ytd_sales|fbg_daily_sales|category_etls_fbg_daily_sales_weekly_rls_l0l1cat|category_etls_staples_daily_sales_rls_l0l1cat|category_etls_blinkit_pricing_health_insights_rls_l0l1cat|warehouse_etls_pro_funnel)
  group: (blinkit_category_metrics)
  privileges:
  - SELECT
  filter: concat(CAST(l0_id AS VARCHAR), '_', CAST(l1_id AS VARCHAR)) IN ( SELECT
    concat(CAST(l0_category_id AS VARCHAR), '_', CAST(l1_category_id AS VARCHAR))
    FROM dwh.category_superset_redash_access_map WHERE email_id = current_user ) OR
    -1 = (select min(l0_category_id) from dwh.category_superset_redash_access_map
    where email_id = current_user) OR manufacturer IS NULL OR manufacturer ='-' OR
    manufacturer IN (SELECT manufacturer FROM blinkit_iceberg.dwh.category_superset_redash_access_map
    WHERE email_id = current_user)