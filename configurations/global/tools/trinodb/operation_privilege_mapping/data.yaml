# Source - https://github.com/trinodb/trino/blob/master/plugin/trino-opa/src/main/java/io/trino/plugin/opa/OpaAccessControl.java
# Table operations
SelectFromColumns:
  - privilege_type: SELECT
    op_type: table
    entity_category: data_entity
CreateViewWithSelectFromColumns:
  - privilege_type: GRANT_SELECT
    op_type: table
    entity_category: data_entity
UpdateTableColumns:
  - privilege_type: UPDATE
    op_type: table
    entity_category: data_entity
InsertIntoTable:
  - privilege_type: INSERT
    op_type: table
    entity_category: data_entity
DeleteFromTable:
  - privilege_type: DELETE
    op_type: table
    entity_category: data_entity
DropTable:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
DropView:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
DropMaterializedView:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
TruncateTable:
  - privilege_type: DELETE
    op_type: table
    entity_category: data_entity
AddColumn:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
AlterColumn:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
CreateView:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
DropColumn:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
RefreshMaterializedView:
  - privilege_type: UPDATE
    op_type: table
    entity_category: data_entity
RenameColumn:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
SetColumnComment:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
SetTableComment:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
SetViewComment:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
ShowColumns:
  - privilege_type: SELECT
    op_type: table
    entity_category: data_entity
ShowCreateTable:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
ShowTables:
  - privilege_type: SELECT
    op_type: table
    entity_category: data_entity
CreateMaterializedView:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
CreateTable:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
SetMaterializedViewProperties:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
SetTableProperties:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
RenameMaterializedView:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
RenameTable:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
RenameView:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity

# Authorization
SetTableAuthorization:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity
SetViewAuthorization:
  - privilege_type: OWNERSHIP
    op_type: table
    entity_category: data_entity

# Schema operations
ShowSchemas:
  - privilege_type: SELECT
    op_type: schema
    entity_category: data_entity
ShowCreateSchema:
  - privilege_type: OWNERSHIP
    op_type: schema
    entity_category: data_entity
CreateSchema:
  - privilege_type: OWNERSHIP
    op_type: schema
    entity_category: data_entity
DropSchema:
  - privilege_type: OWNERSHIP
    op_type: schema
    entity_category: data_entity
RenameSchema:
  - privilege_type: OWNERSHIP
    op_type: schema
    entity_category: data_entity
SetSchemaAuthorization:
  - privilege_type: OWNERSHIP
    op_type: schema
    entity_category: data_entity
FilterSchemas:
  - privilege_type: SELECT
    op_type: schema
    entity_category: data_entity

# Catalog operations
AccessCatalog:
  - privilege_type: SELECT
    op_type: catalog
    entity_category: data_entity
CreateCatalog:
  - privilege_type: OWNERSHIP
    op_type: catalog
    entity_category: data_entity
DropCatalog:
  - privilege_type: OWNERSHIP
    op_type: catalog
    entity_category: data_entity
FilterCatalogs:
  - privilege_type: SELECT
    op_type: catalog
    entity_category: data_entity

# Session property operations
SetCatalogSessionProperty:
  - privilege_type: UPDATE
    op_type: session_property_operation
    entity_category: data_entity
SetSystemSessionProperty:
  - privilege_type: UPDATE
    op_type: session_property_operation
    entity_category: data_entity
SetSessionProperty:
  - privilege_type: UPDATE
    op_type: session_property_operation
    entity_category: data_entity

# Filtering
FilterColumns:
  - privilege_type: SELECT
    op_type: filtering
    entity_category: data_entity
FilterTables:
  - privilege_type: SELECT
    op_type: filtering
    entity_category: data_entity
FilterFunctions:
  - privilege_type: EXECUTE
    op_type: filtering
    entity_category: data_entity
GetColumnMask:
  - privilege_type: SELECT
    op_type: filtering
    entity_category: data_entity
GetRowFilters:
  - privilege_type: SELECT
    op_type: filtering
    entity_category: data_entity

# Function/procedure operations
ShowFunctions:
  - privilege_type: SELECT
    op_type: function
    entity_category: query_engine_entity
ExecuteTableProcedure:
  - privilege_type: EXECUTE
    op_type: function
    entity_category: query_engine_entity
ExecuteFunction:
  - privilege_type: EXECUTE
    op_type: function
    entity_category: query_engine_entity
ExecuteProcedure:
  - privilege_type: EXECUTE
    op_type: function
    entity_category: query_engine_entity
CreateViewWithExecuteFunction:
  - privilege_type: GRANT_EXECUTE
    op_type: function
    entity_category: query_engine_entity
ShowCreateFunction:
  - privilege_type: OWNERSHIP
    op_type: function
    entity_category: query_engine_entity
CreateFunction:
  - privilege_type: OWNERSHIP
    op_type: function
    entity_category: query_engine_entity
DropFunction:
  - privilege_type: OWNERSHIP
    op_type: function
    entity_category: query_engine_entity

# System operations
ReadSystemInformation:
  - privilege_type: READ
    op_type: system_information
    entity_category: query_engine_entity
WriteSystemInformation:
  - privilege_type: WRITE
    op_type: system_information
    entity_category: query_engine_entity

# Query operations
ExecuteQuery:
  - privilege_type: EXECUTE
    op_type: query
    entity_category: query_engine_entity
KillQueryOwnedBy:
  - privilege_type: KILL
    op_type: query
    entity_category: query_engine_entity
ViewQueryOwnedBy:
  - privilege_type: VIEW
    op_type: query
    entity_category: query_engine_entity
FilterViewQueryOwnedBy:
  - privilege_type: VIEW
    op_type: query
    entity_category: query_engine_entity

# Impersonation
ImpersonateUser:
  - privilege_type: IMPERSONATE
    op_type: impersonation
    entity_category: query_engine_entity
