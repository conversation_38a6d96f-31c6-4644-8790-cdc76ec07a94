# This file list down the operations grouped by their access scopes.
column_masking:
  - "GetColumnMask"
  - "BatchColumnMasking"
row_filtering:
  - "GetRowFilters"
schema_authorization:
  - "SetSchemaAuthorization" 
schema_operations_ddl:
  - "CreateSchema"
  - "DropSchema"
  - "ShowCreateSchema"
schema_operations_ddl_rename:
  - "RenameSchema"
schema_visibility:
  - "FilterSchemas"
  - "ShowFunctions"
  - "ShowTables"
table_authorization:
  - "SetTableAuthorization"
  - "SetViewAuthorization"
system_operations:
  - "ReadSystemInformation"
  - "WriteSystemInformation"
catalog_access:
  - "AccessCatalog"
catalog_session_properties:
  - "SetCatalogSessionProperty"
session_properties:
  - "SetSystemSessionProperty"
  - "SetSessionProperty"
catalog_visibility:
  - "FilterCatalogs"
  - "ShowSchemas"
catalog_operations_ddl:
  - "CreateCatalog"
  - "DropCatalog"
column_level_select:
  - "SelectFromColumns"
column_filtering:
  - "FilterColumns"
column_visibility:
  - "ShowColumns"
functions_operations_ddl:
  - "CreateFunction"
  - "DropFunction"
  - "ShowCreateFunction"
functions_operations_dql_extn:
  - "ExecuteFunction"
  - "FilterFunctions"
  - "CreateViewWithExecuteFunction"
impersonation:
  - "ImpersonateUser"
procedure_operations:
  - "ExecuteProcedure"
  - "ExecuteTableProcedure"
query_execution:
  - "ExecuteQuery"
meta_query_operations:
  - "FilterViewQueryOwnedBy"
  - "ViewQueryOwnedBy"
  - "KillQueryOwnedBy"
table_operations_ddl:
  - "AddColumn"
  - "AlterColumn"
  - "CreateMaterializedView"
  - "CreateTable"
  - "CreateView"
  - "CreateViewWithSelectFromColumns"
  - "DropColumn"
  - "DropMaterializedView"
  - "DropTable"
  - "DropView"
  - "RenameColumn"
  - "SetColumnComment"
  - "SetMaterializedViewProperties"
  - "SetTableComment"
  - "SetTableProperties"
  - "SetViewComment"
  - "ShowCreateTable"
  # UPDATE PRIVILEGE
  - "RefreshMaterializedView"
  - "UpdateTableColumns"
  # DELETE PRIVILEGE
  - "DeleteFromTable"
  - "TruncateTable"
  # INSERT PRIVILEGE
  - "InsertIntoTable"
table_visibility:
  - "FilterTables"
table_operations_ddl_rename:
  - "RenameMaterializedView"
  - "RenameTable"
  - "RenameView"