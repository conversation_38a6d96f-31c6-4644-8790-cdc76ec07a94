row_level_filters:
  supply_kams:
    template: "${res_id_col} in (select res_id from jumbo_derived.res_access_control_mapping where email = current_user and res_id in (select res_id from jumbo_derived.o2_restaurant_attributes as a where (a.regional_sam_flag = 1 OR lower(city_cluster) like 'bharat%' OR a.am_id is not null) and a.sam_flag = 0 and o2_active_flag = 1 and res_id is not null))"
    default_value: "res_id"
    description: "Filter for supply KAMs"
    
  bharat_leadership:
    template: res_id in (select res_id from jumbo_derived.res_access_control_mapping where
      email = current_user and res_id in (select res_id from jumbo_derived.o2_restaurant_attributes
      as a where (a.regional_sam_flag = 1 OR lower(city_cluster) like 'bharat%' OR a.am_id
      is not null) and a.sam_flag = 0 and o2_active_flag = 1 and res_id is not null))
    description: "Filter for bharat leadership o2_daily_rate_cards access"