#!/bin/bash
set -euo pipefail

cleanup() {
    if [ -d "$TEMP_DIR" ]; then
        echo "INFO: Cleaning up temporary directory..."
        rm -rf "$TEMP_DIR"
    fi
}

trap cleanup EXIT

for tool in opa git yq tar; do
    if ! command -v "$tool" > /dev/null 2>&1; then
        echo "ERROR: '$tool' command not found. Please install it."
        exit 0
    fi
done

FINAL_BUNDLE="bundle.tar.gz"
TEMP_DIR="temp_$(date +%s)"

echo "INFO: Creating initial OPA bundle..."

GIT_REVISION=$(git rev-parse HEAD)
export GIT_REVISION

if ! opa build -b . -O=1 -r "$GIT_REVISION" -o "$FINAL_BUNDLE"; then
    echo "ERROR: Failed to create initial OPA bundle"
    exit 1
fi

echo "INFO: Unpacking bundle into temporary directory '$TEMP_DIR'..."

rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

if ! tar -xzf "$FINAL_BUNDLE" -C "$TEMP_DIR"; then
    echo "ERROR: Failed to unpack bundle"
    exit 1
fi

echo "INFO: Removing root data.json from temp bundle..."
rm -f "$TEMP_DIR/data.json"

echo "INFO: Finding and converting YAML files..."

SUCCESS_COUNT=0
FAIL_COUNT=0

yaml_files=()
for file in $(find . -name "*.yaml" 2>/dev/null); do
    yaml_files+=("$file")
done

if [ ${#yaml_files[@]} -eq 0 ]; then
    echo "INFO: No YAML files found to convert."
else
    echo "INFO: Found ${#yaml_files[@]} YAML file(s) to process."
    
    for yaml_file in "${yaml_files[@]}"; do
        if [ ! -f "$yaml_file" ]; then
            continue
        fi

        relative_path="${yaml_file#./}"
        json_path="$TEMP_DIR/${relative_path%.*}.json"

        echo " -> Converting '$yaml_file' to '$json_path'"

        target_dir=$(dirname "$json_path")
        mkdir -p "$target_dir"

        if [ ! -s "$yaml_file" ]; then
            echo "    WARNING: '$yaml_file' is empty, creating empty JSON object"
            echo '{}' > "$json_path"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        else
            if yq eval '.' "$yaml_file" -o=json > "$json_path" 2>/dev/null; then
                if [ -s "$json_path" ]; then
                    SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
                else
                    echo "    ERROR: Conversion produced empty output for '$yaml_file'"
                    rm -f "$json_path"
                    FAIL_COUNT=$((FAIL_COUNT + 1))
                fi
            else
                echo "    ERROR: Failed to convert '$yaml_file'"
                rm -f "$json_path"
                FAIL_COUNT=$((FAIL_COUNT + 1))
            fi
        fi
    done
fi

echo "-----------------------------------------------------------------------------"
echo "INFO: YAML Conversion summary: $SUCCESS_COUNT successful, $FAIL_COUNT failed."

if [ "$FAIL_COUNT" -gt 0 ]; then
    echo "ERROR: Build failed due to YAML conversion errors."
    exit 1
fi

echo "INFO: Creating .opal_manifest file..."

create_opal_manifest() {
    local temp_dir="$1"
    local manifest_file="$temp_dir/.opal_manifest"

    echo "INFO: Creating manifest at $manifest_file"

    echo "# OPAL Manifest - defines loading order for rego files and data" > "$manifest_file"
    echo "# Files are loaded in the order they appear in this manifest" >> "$manifest_file"
    echo "# Generated with simple ordering: utils first, then rules (core.rego second-last, main.rego last), then others" >> "$manifest_file"
    echo "" >> "$manifest_file"

    # Find all rego files in the bundle (excluding test files)
    local all_rego_files="$temp_dir/.all_rego_files"
    find "$temp_dir" -name "*.rego" -not -name "*_test.rego" -type f > "$all_rego_files.tmp"

    # Convert absolute paths to relative paths
    while read -r rego_file; do
        relative_path="${rego_file#$temp_dir/}"
        echo "$relative_path" >> "$all_rego_files"
    done < "$all_rego_files.tmp"

    rm -f "$all_rego_files.tmp"

    local rego_count=$(wc -l < "$all_rego_files" 2>/dev/null || echo "0")
    echo "INFO: Found $rego_count rego files"

    # Phase 1: All files in utils/ folder first
    echo "# Phase 1: Utility files (utils/ folder)" >> "$manifest_file"
    grep "^utils/" "$all_rego_files" | sort >> "$manifest_file"
    echo "" >> "$manifest_file"

    # Phase 2: Rules files in specific order
    echo "# Phase 2: Rules files (specific order)" >> "$manifest_file"

    # Add rules files except core.rego and main.rego
    grep "^rules/" "$all_rego_files" | grep -v "rules/core.rego" | grep -v "rules/main.rego" | sort >> "$manifest_file"

    # Add core.rego second to last
    if grep -q "^rules/core.rego$" "$all_rego_files"; then
        echo "rules/core.rego" >> "$manifest_file"
    fi

    # Add main.rego last
    if grep -q "^rules/main.rego$" "$all_rego_files"; then
        echo "rules/main.rego" >> "$manifest_file"
    fi

    echo "" >> "$manifest_file"

    # Phase 3: All other rego files
    echo "# Phase 3: Other rego files" >> "$manifest_file"
    grep -v "^utils/" "$all_rego_files" | grep -v "^rules/" | sort >> "$manifest_file"
    echo "" >> "$manifest_file"

    # Phase 4: Data files (converted from YAML)
    echo "# Phase 4: Data files (converted from YAML)" >> "$manifest_file"
    find "$temp_dir" -name "data.json" -type f | while read -r json_file; do
        relative_path="${json_file#$temp_dir/}"
        echo "$relative_path" >> "$manifest_file"
    done

    echo "" >> "$manifest_file"

    # Phase 5: Test files
    echo "# Phase 5: Test files" >> "$manifest_file"
    find "$temp_dir" -name "*_test.rego" -type f | while read -r test_file; do
        relative_path="${test_file#$temp_dir/}"
        echo "$relative_path" >> "$manifest_file"
    done

    # Cleanup temporary files
    rm -f "$all_rego_files" "$all_rego_files.tmp" 2>/dev/null || true

    local manifest_lines=$(wc -l < "$manifest_file" 2>/dev/null || echo "0")
    echo "INFO: Created .opal_manifest with $manifest_lines lines"

    return 0
}

# Create the manifest
create_opal_manifest "$TEMP_DIR"

echo "INFO: Re-creating final bundle..."

if [ ! -d "$TEMP_DIR" ] || [ -z "$(ls -A "$TEMP_DIR" 2>/dev/null)" ]; then
    echo "ERROR: Temporary directory is empty"
    exit 1
fi

cd "$TEMP_DIR"
tar -czf "../$FINAL_BUNDLE" .
cd ..

echo "INFO: Final bundle contents:"
tar -tf "$FINAL_BUNDLE"

echo "---------------------------------------------------------------------------------"
echo "SUCCESS: Bundle '$FINAL_BUNDLE' created with $SUCCESS_COUNT converted YAML files and .opal_manifest."