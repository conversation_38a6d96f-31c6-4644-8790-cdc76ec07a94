#!/usr/bin/env python3
"""
OPA Migration Script: old_rules.json → canonical OPA YAML format

- Reads configurations/old_rules.json
- Converts to canonical OPA YAML (see Subtask 2 and test-driven requirements)
- Migrates only "allow" rules (skips denies/privileges: [])
- Handles field/value normalization and mapping (e.g. columns, masks, etc.)
- Logs/reporting: logs skipped/untranslatable rules by type and reason
- Idempotent: overwrites configurations/global/rules/data.yaml in full
- Robust structure/comments for incremental/partial migration/extensibility

You can re-run this script safely (output will always be canonicalized,
no duplicate, residual, or deny rules, and old artifacts are eliminated).
"""

import json
import yaml
from collections import defaultdict, OrderedDict
from copy import deepcopy
import re

def to_plain_obj(obj):
    """Recursively convert OrderedDicts (and tuples) to standard Python dicts/lists for safe YAML output."""
    if isinstance(obj, OrderedDict):
        return {k: to_plain_obj(v) for k, v in obj.items()}
    elif isinstance(obj, dict):
        return {k: to_plain_obj(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [to_plain_obj(x) for x in obj]
    elif isinstance(obj, tuple):
        return [to_plain_obj(x) for x in obj]
    return obj

SRC = "configurations/old_rules.json"
DEST = "configurations/global/rules/data.yaml"

# Helpers for robust normalization
def upper_key(d):
    if isinstance(d, dict):
        return {k.lower(): upper_key(v) for k, v in d.items()}
    elif isinstance(d, list):
        return [upper_key(x) for x in d]
    return d

def canonicalize_privileges(privs):
    if not isinstance(privs, list):
        return []
    return sorted(set(p.upper() for p in privs), key=str)

def has_allow_privileges(rule, field="privileges"):
    # Only treat as allow if non-empty privileges AND not a known deny artifact
    return bool(rule.get(field)) and isinstance(rule.get(field), list) and len(rule[field]) > 0

def singularize_type(s):
    # "tables" → "table", etc.
    return s[:-1] if s.endswith('s') else s

def is_pattern(s):
    return isinstance(s, str) and (s.startswith("(") and s.endswith(")"))

# -- Canonical Mapping Functions: Each returns a ready-for-YAML dict following canonical OPA conventions

def migrate_catalog_rule(rule, skiplog):
    # OPA: catalog access/ownership, minimal fields
    res = OrderedDict([
        ("type", "catalog"),
        ("entity_category", "data_entity")
    ])
    allow = rule.get("allow")
    owner = rule.get("owner", False)
    user = rule.get("user")
    group = rule.get("group")
    catalog = rule.get("catalog")

    # Canonicalize field casing (OPA YAML is all-lower)
    if user: res["user"] = user
    if group: res["group"] = group
    if catalog: res["catalog"] = catalog
    if owner: res["owner"] = bool(owner)

    if allow and allow != "none":
        # Only migrate proper allow values
        res["allow"] = allow if allow in ("all", "read-only") else "none"
        return res

    if owner:
        # Ownership grant, migrate
        res["owner"] = True
        return res

    skiplog.append(("catalog", rule, "skipped: no allow/owner or ambiguous"))
    return None

def migrate_schema_rule(rule, skiplog):
    # OPA: schema access/ownership, minimal fields
    res = OrderedDict([
        ("type", "schema"),
        ("entity_category", "data_entity")
    ])
    user = rule.get("user")
    group = rule.get("group")
    catalog = rule.get("catalog")
    schema = rule.get("schema")
    owner = rule.get("owner", False)

    if user: res["user"] = user
    if group: res["group"] = group
    if catalog: res["catalog"] = catalog
    if schema: res["schema"] = schema
    if owner: res["owner"] = bool(owner)
    else:
        # If neither owner = True nor privileges (see below), skip
        privs = rule.get("privileges", [])
        if not has_allow_privileges(rule) and not owner:
            skiplog.append(("schema", rule, "skipped: neither owner nor allow privileges"))
            return None

    privs = canonicalize_privileges(rule.get("privileges", []))
    if privs:
        res["privileges"] = privs
    if not privs and not owner:
        skiplog.append(("schema", rule, "skipped: no privileges and not owner"))
        return None

    return res

def migrate_table_rule(rule, skiplog, migrated_no_priv_only):
    """
    Refactored OPA migration:
    - Migrates table rules if ANY of:
        - Non-empty privileges,
        - Any column mask field,
        - Any row filter field
    - Canonicalizes all mask/filter fields
    - Logs rules migrated ONLY due to mask/filter field (w/o privileges)
    """
    # --- Canonical field extraction helpers ---
    COLUMN_FIELDS = ("columns", "masking_expression", "mask")
    FILTER_FIELDS = ("filter", "filter_expression", "where", "row_filter")

    # Detect privileges
    privs = canonicalize_privileges(rule.get("privileges", []))
    has_privs = len(privs) > 0

    # Detect column masking
    canon_cols = []
    for field in COLUMN_FIELDS:
        if field in rule and rule[field]:
            if field == "columns":
                candidates = rule[field]
                if isinstance(candidates, list):
                    for col in candidates:
                        name = col.get("name") if isinstance(col, dict) else None
                        mask = col.get("mask")
                        canon = OrderedDict()
                        if name:
                            canon["name"] = name
                        # If legacy: value is field->colname, mask in other field
                        if not name and isinstance(col, dict):
                            # Fallback: possible input {"column": "xxx", ...}
                            n2 = col.get("column")
                            if n2:
                                canon["name"] = n2
                        if mask:
                            canon["mask"] = mask
                        elif "masking_expression" in col:
                            canon["mask"] = col.get("masking_expression")
                        elif "mask" in col:
                            canon["mask"] = col.get("mask")
                        if canon.get("name"):
                            canon_cols.append(canon)
                # else, skip (legacy can have dict/str, but out of scope here)
            else:
                # Single field: treat as mask for all columns or as special
                name = rule.get("column_name") or None
                val = rule[field]
                if name and val:
                    canon_cols.append(OrderedDict([("name", name), ("mask", val)]))
                # else, can't canonicalize safely
    has_colmask = bool(canon_cols)

    # Detect row filter (first of any mapping wins for canonical 'filter')
    filter_expr = None
    filter_fields_used = []
    for field in FILTER_FIELDS:
        if rule.get(field):
            filter_expr = rule[field]
            filter_fields_used.append(field)
            break
    has_filter = filter_expr is not None

    # Additional filter-related fields
    filter_meta = {}
    for meta in ("filter_environment", "filter_substitution", "filter_template"):
        if rule.get(meta) is not None:
            filter_meta[meta] = rule[meta]

    # Decide migration: if no privs, no col mask, no filter, skip
    if not (has_privs or has_colmask or has_filter):
        skiplog.append(("table", rule, "skipped: no privileges, mask, or filter field"))
        return None

    res = OrderedDict([
        ("type", "table"),
        ("entity_category", "data_entity"),
    ])
    for f in ("catalog", "schema", "table", "user", "group"):
        v = rule.get(f)
        if v:
            res[f] = v

    if has_privs:
        res["privileges"] = privs

    if canon_cols:
        res["columns"] = canon_cols

    if has_filter:
        res["filter"] = filter_expr
        for k, v in filter_meta.items():
            res[k] = v

    # Log rules migrated ONLY for colmask/filter (i.e., with NO privileges)
    if not has_privs and (has_colmask or has_filter):
        migrated_no_priv_only.append({
            "rule": deepcopy(rule),
            "reason": "migrated due to " +
                      ("mask + filter" if has_colmask and has_filter else
                       "mask" if has_colmask else "filter")
        })

    return res

def migrate_query_rule(rule, skiplog):
    # OPA: query access (allow must be non-empty and a list)
    allow = rule.get("allow", [])
    if not allow or not isinstance(allow, list):
        skiplog.append(("query", rule, "skipped: no query allows"))
        return None
    res = OrderedDict([
        ("type", "query"),
        ("entity_category", "query_engine_entity"),
        ("allow", [a.lower() for a in allow])
    ])
    # OPA canonical may have 'user', 'group' patterns for query rules
    for f in ("user", "group"):
        v = rule.get(f)
        if v:
            res[f] = v
    return res

def migrate_system_information_rule(rule, skiplog):
    # OPA: system_information (must have allow)
    allow = rule.get("allow", [])
    if not allow:
        skiplog.append(("system_information", rule, "skipped: no allow actions"))
        return None
    res = OrderedDict([
        ("type", "system_information"),
        ("entity_category", "query_engine_entity"),
        ("allow", [a.lower() for a in allow])
    ])
    for f in ("user", "group"):
        v = rule.get(f)
        if v:
            res[f] = v
    return res

def migrate_impersonation_rule(rule, skiplog):
    # OPA: impersonation (must specify at least one original_user pattern and a new_user pattern)
    orig = rule.get("original_user")
    new = rule.get("new_user")
    if not orig or not new:
        skiplog.append(("impersonation", rule, "skipped: missing fields"))
        return None
    res = OrderedDict([
        ("type", "impersonation"),
        ("entity_category", "query_engine_entity"),
        ("original_user", orig),
        ("new_user", new),
        ("allow", True)  # OPA canonical: allow: true
    ])
    return res

# --- TYPE ROUTING ---

def migrate_rules_by_type(src, skiplog):
    migrated = []
    # Track all rules migrated solely for column mask or filter field
    migrated_no_priv_only = []

    # Top-level mappings by type for OPA canonicalization
    # Patch: wrap table migrator to accept tracking for no-priv-only
    def table_migrator(rule, skiplog):
        return migrate_table_rule(rule, skiplog, migrated_no_priv_only)

    canonical_migrators = {
        "catalogs": migrate_catalog_rule,
        "schemas": migrate_schema_rule,
        "tables": table_migrator,
        "queries": migrate_query_rule,
        "system_information": migrate_system_information_rule,
        "impersonation": migrate_impersonation_rule,
    }

    type_counts = defaultdict(int)
    skip_counts = defaultdict(int)

    # Process each section
    for category, rules in src.items():
        migrator = canonical_migrators.get(category)
        if not migrator or not isinstance(rules, list):
            continue
        for rule in rules:
            # Upper/lower/canonical key normalization
            rule = upper_key(rule)
            new_rule = migrator(rule, skiplog)
            if new_rule is not None:
                migrated.append(new_rule)
                type_counts[category] += 1
            else:
                skip_counts[category] += 1

    return migrated, type_counts, skip_counts, migrated_no_priv_only

# --- MAIN MIGRATION DRIVER ---

def main():
    with open(SRC, "r") as f:
        src = json.load(f)

    skiplog = []
    migrated, type_counts, skip_counts, migrated_no_priv_only = migrate_rules_by_type(src, skiplog)

    # --- YAML OUTPUT ---
    # Write a compliant, deeply canonicalized YAML array file
    output = migrated
    with open(DEST, "w") as f:
        yaml.safe_dump(
            to_plain_obj(output),
            f,
            default_flow_style=False,
            sort_keys=False,
            allow_unicode=True
        )

    # --- LOG/REPORT ---
    print("OPA Migration completed.")
    print("Rules migrated by type:")
    for t, cnt in type_counts.items():
        print(f"  {t}: {cnt}")
    print("Rules skipped by type:")
    for t, cnt in skip_counts.items():
        print(f"  {t}: {cnt}")
    # Mask/filter-only rules migrated (table type) report
    if migrated_no_priv_only:
        print("\n[INFO] Table rules migrated due to column mask/row filter (empty privileges, still migrated):")
        for ex in migrated_no_priv_only[:10]:
            r, why = ex["rule"], ex["reason"]
            print(f"- Reason: {why}\n  Rule: {json.dumps(r, indent=2)}")
        if len(migrated_no_priv_only) > 10:
            print(f"... and {len(migrated_no_priv_only)-10} more such rules")
    if skiplog:
        print("\nSample skipped rules (first 10):")
        for typ, rule, reason in skiplog[:10]:
            print(f"- [{typ}] Reason: {reason}\n  Rule: {json.dumps(rule, indent=2)}")
        if len(skiplog) > 10:
            print(f"... and {len(skiplog)-10} more skipped (if any)")
    else:
        print("No rules skipped.")
    print(f"Canonical OPA YAML written to {DEST}.\nIdempotent: re-running will always rewrite YAML in canonical form.")
    print("Column mask and row filter fields are now always preserved when present (even if privileges are missing). All canonicalization guarantees maintained.")

    # For future: you can easily extend migrate_* functions for new types/fields.
    # The script structure allows for partial/incremental migration with robust logging.

if __name__ == "__main__":
    main()