#!/usr/bin/env python3
"""
Script to modify superset access rules:
- Remove 'group' field from all rules
- Add 'source: blinkit-superset' to all rules

Usage: python modify_superset_rules.py [input_file] [output_file]
"""

import yaml
import sys
import argparse
from pathlib import Path


def load_yaml_file(file_path):
    """Load and parse YAML file."""
    try:
        with open(file_path, 'r') as f:
            return yaml.safe_load(f)
    except yaml.YAMLError as e:
        print(f"Error parsing YAML file {file_path}: {e}")
        return None
    except FileNotFoundError:
        print(f"Error: YAML file {file_path} not found")
        return None


def save_yaml_file(data, file_path):
    """Save data to YAML file with proper formatting."""
    try:
        with open(file_path, 'w') as f:
            yaml.safe_dump(
                data,
                f,
                default_flow_style=False,
                sort_keys=False,
                allow_unicode=True,
                indent=2,
                width=120
            )
        return True
    except Exception as e:
        print(f"Error saving YAML file {file_path}: {e}")
        return False


def modify_rule(rule):
    """
    Modify a single rule:
    - Remove 'group' field if present
    - Add 'source: blinkit-superset'
    """
    if not isinstance(rule, dict):
        return rule
    
    # Create a copy to avoid modifying the original
    modified_rule = rule.copy()
    
    # Remove 'group' field if present
    if 'group' in modified_rule:
        del modified_rule['group']
        print(f"  Removed 'group' field: {rule.get('group', 'N/A')}")
    
    # Add source field
    modified_rule['source'] = 'blinkit-superset'
    print(f"  Added 'source: blinkit-superset'")
    
    return modified_rule


def modify_superset_rules(input_file, output_file):
    """
    Main function to modify superset rules.
    """
    print(f"Loading rules from: {input_file}")
    
    # Load the YAML file
    rules_data = load_yaml_file(input_file)
    if rules_data is None:
        return False
    
    if not isinstance(rules_data, list):
        print("Error: Expected YAML file to contain a list of rules")
        return False
    
    print(f"Found {len(rules_data)} rules to process")
    
    # Process each rule
    modified_rules = []
    for i, rule in enumerate(rules_data):
        print(f"\nProcessing rule {i + 1}/{len(rules_data)}:")
        
        # Show rule identifier for context
        rule_id = f"type={rule.get('type', 'unknown')}"
        if 'catalog' in rule:
            rule_id += f", catalog={rule['catalog']}"
        if 'schema' in rule:
            rule_id += f", schema={rule['schema']}"
        if 'table' in rule:
            rule_id += f", table={rule['table']}"
        print(f"  Rule: {rule_id}")
        
        modified_rule = modify_rule(rule)
        modified_rules.append(modified_rule)
    
    print(f"\nSaving modified rules to: {output_file}")
    
    # Save the modified rules
    if save_yaml_file(modified_rules, output_file):
        print(f"✅ Successfully saved {len(modified_rules)} modified rules to {output_file}")
        return True
    else:
        print(f"❌ Failed to save rules to {output_file}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="Modify superset access rules by removing 'group' field and adding 'source: blinkit-superset'"
    )
    parser.add_argument(
        'input_file',
        nargs='?',
        default='configurations/global/rules/superset_access.yml',
        help='Input YAML file path (default: configurations/global/rules/superset_access.yml)'
    )
    parser.add_argument(
        'output_file',
        nargs='?',
        help='Output YAML file path (default: same as input file)'
    )
    parser.add_argument(
        '--backup',
        action='store_true',
        help='Create a backup of the original file before modifying'
    )
    
    args = parser.parse_args()
    
    # Set default output file if not provided
    if not args.output_file:
        args.output_file = args.input_file
    
    # Check if input file exists
    if not Path(args.input_file).exists():
        print(f"Error: Input file '{args.input_file}' does not exist")
        sys.exit(1)
    
    # Create backup if requested
    if args.backup and args.input_file == args.output_file:
        backup_file = f"{args.input_file}.backup"
        print(f"Creating backup: {backup_file}")
        try:
            import shutil
            shutil.copy2(args.input_file, backup_file)
            print(f"✅ Backup created: {backup_file}")
        except Exception as e:
            print(f"❌ Failed to create backup: {e}")
            sys.exit(1)
    
    # Perform the modification
    success = modify_superset_rules(args.input_file, args.output_file)
    
    if success:
        print("\n🎉 Script completed successfully!")
        print(f"Modified rules saved to: {args.output_file}")
        if args.backup and args.input_file == args.output_file:
            print(f"Original file backed up to: {args.input_file}.backup")
    else:
        print("\n❌ Script failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
