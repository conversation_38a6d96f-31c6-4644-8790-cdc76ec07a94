#!/bin/bash
# Script to modify superset access rules:
# - Remove 'group' field from all rules  
# - Add 'source: blinkit-superset' to all rules

set -euo pipefail

# Default file path
DEFAULT_FILE="configurations/global/rules/superset_access.yml"
INPUT_FILE="${1:-$DEFAULT_FILE}"
OUTPUT_FILE="${2:-$INPUT_FILE}"
BACKUP_FILE="${INPUT_FILE}.backup"

# Check if required tools are available
for tool in yq; do
    if ! command -v "$tool" > /dev/null 2>&1; then
        echo "ERROR: '$tool' command not found. Please install it."
        echo "Install yq with: wget -qO- https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 | sudo tee /usr/local/bin/yq > /dev/null && sudo chmod +x /usr/local/bin/yq"
        exit 1
    fi
done

# Check if input file exists
if [ ! -f "$INPUT_FILE" ]; then
    echo "ERROR: Input file '$INPUT_FILE' does not exist"
    exit 1
fi

echo "Processing superset access rules..."
echo "Input file: $INPUT_FILE"
echo "Output file: $OUTPUT_FILE"

# Create backup if modifying the same file
if [ "$INPUT_FILE" = "$OUTPUT_FILE" ]; then
    echo "Creating backup: $BACKUP_FILE"
    cp "$INPUT_FILE" "$BACKUP_FILE"
    echo "✅ Backup created: $BACKUP_FILE"
fi

# Count total rules
TOTAL_RULES=$(yq eval 'length' "$INPUT_FILE")
echo "Found $TOTAL_RULES rules to process"

# Create temporary file for processing
TEMP_FILE=$(mktemp)
trap "rm -f $TEMP_FILE" EXIT

# Process the YAML file:
# 1. Remove 'group' field from all rules
# 2. Add 'source: blinkit-superset' to all rules
echo "Modifying rules..."

yq eval '
  map(
    del(.group) |
    .source = "blinkit-superset"
  )
' "$INPUT_FILE" > "$TEMP_FILE"

# Verify the output is valid YAML
if ! yq eval '.' "$TEMP_FILE" > /dev/null 2>&1; then
    echo "ERROR: Generated invalid YAML"
    exit 1
fi

# Move the processed file to the output location
mv "$TEMP_FILE" "$OUTPUT_FILE"

# Count modified rules
MODIFIED_RULES=$(yq eval 'length' "$OUTPUT_FILE")

echo "✅ Successfully processed $MODIFIED_RULES rules"
echo "✅ Modified rules saved to: $OUTPUT_FILE"

if [ "$INPUT_FILE" = "$OUTPUT_FILE" ]; then
    echo "✅ Original file backed up to: $BACKUP_FILE"
fi

echo ""
echo "Summary of changes made:"
echo "- Removed 'group' field from all rules"
echo "- Added 'source: blinkit-superset' to all rules"
echo ""
echo "🎉 Script completed successfully!"
