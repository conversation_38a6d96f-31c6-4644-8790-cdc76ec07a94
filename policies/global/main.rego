package policies.global.main

import data.configurations.global.catalog_tenant_mapping
import data.configurations.global.lob_tenant_mapping
import data.policies.utils.main as utils_main
import data.tools.trinodb.utils.extraction
import data.utils.custom_regex
import data.utils.generic

import data.rules.main as central_rules

import rego.v1

# Default deny
default allow := false

default valid_user := false

# Get the user attributes
default user_attributes := {"line_of_business": null}

user_attributes := object.get(data.dynamic_data.user_attributes, input.user, {})

valid_user if {
	count(user_attributes) > 0
}

system_users := {user | some user in data.configurations.global.system_users}

valid_user if {
	input.user in system_users
}

# Get the external groups
default external_groups := []

external_groups := object.get(data.group_mappings, input.user, []).groups

groups := array.concat(
	array.concat(input.groups, external_groups),
	[""],
)

op_priv_mapping := object.get(data.configurations.global.tools, [input.tool, "operation_privilege_mapping"], {})

privilege := extraction._get_privilege(input.operation, op_priv_mapping)

source_tenant := generic.normalize_to_array(object.get(
	lob_tenant_mapping,
	object.get(user_attributes, "line_of_business", null),
	input.tenant_map.runtime_tenant,
))

destination_tenant := generic.normalize_to_array(object.get(catalog_tenant_mapping, input.catalog, []))

# Check if the source and destination tenant are same
default same_tenant := false

same_tenant if {
	count(source_tenant) == 1
	count(destination_tenant) == 1
	source_tenant[0] == destination_tenant[0]
}

request_context := {
	"tenant_map": {
		"source_tenant": source_tenant,
		"destination_tenant": destination_tenant,
		"same_tenant": same_tenant,
	},
	"user": input.user,
	"groups": groups,
	"tool": input.tool,
	"user_attributes": user_attributes,
	"catalog": input.catalog,
	"schema": input.schema,
	"table": input.table,
	"columns": input.columns,
	"action": input.action,
	"operation": input.operation,
	"privilege": privilege.privilege_type,
	"op_type": privilege.op_type,
	"entity_category": privilege.entity_category,
	"source": input.source,
}

precomputed_generic_compiled_rules := compiled_rules if {
	rules := central_rules.compiled_rules with input as request_context
	compiled_rules := [rule | rule := rules[_]]
}

# Evaluate the global policies
allow if {
	valid_user
	central_rules.allow with input as request_context
		with central_rules.compiled_rules as precomputed_generic_compiled_rules
}

precomputed_row_filter_compiled_rules := compiled_rules if {
	rules := central_rules.compiled_rules with input as request_context
	compiled_rules := [rule | rule := rules[_]; utils_main.is_valid_or_has_priority(rule, "row_filtering"); rule.entity_category == privilege.entity_category]
}

# Evaluate custom obj return polcies
row_filter := rf if {
	valid_user

	valid_operations := generic.fetch_operations(input.tool, "row_filtering")
	input.operation in valid_operations

	rf := central_rules.row_filter with input as request_context
		with central_rules.compiled_rules as precomputed_row_filter_compiled_rules
}

precomputed_column_masking_compiled_rules := compiled_rules if {
	rules := central_rules.compiled_rules with input as request_context
	compiled_rules := [rule | rule := rules[_]; utils_main.is_valid_or_has_priority(rule, "column_masking"); rule.entity_category == privilege.entity_category]
}

column_mask := cm if {
	valid_user

	valid_operations := generic.fetch_operations(input.tool, "column_masking")
	input.operation in valid_operations

	cm := central_rules.column_mask with input as request_context
		with central_rules.compiled_rules as precomputed_column_masking_compiled_rules
}
