# Core Policy Evaluation Engine

This document details the core Rego-based policy evaluation engine. It outlines how policies are structured, loaded, and evaluated to make authorization decisions.

## Overview

The engine is composed of several key Rego packages working in concert:

-   **`rules.constants`**: Defines shared constants.
-   **`rules.user_attributes`**: Handles policies based on user attributes.
-   **`rules.core`**: Implements fundamental rule-matching and permission-checking logic.
-   **`rules.main`**: Orchestrates the policy evaluation flow, compiles applicable rules, and makes final decisions (`allow`, `row_filter`, `column_mask`).
-   **`utils.generic`**: Provides helper functions for string manipulation, data fetching, and normalization (from the `utils` directory).
-   **`utils.custom_regex`**: Offers custom regex matching utilities (from the `utils` directory).

## Evaluation Principles

1.  **Standardized Privileges**: Incoming actions from query engines (e.g., Trino operations) are mapped to a common set of privileges (e.g., `SELECT`, `OWNERSHIP`, `EXECUTE`) defined in `rules.constants`.
2.  **Categorized Rules**: Rules are categorized (e.g., `data_entity` for tables/schemas/catalogs, `query_engine_entity` for functions/impersonation) using `entity_category` from `rules.constants` to apply relevant logic.
3.  **Configuration-Driven**: Policies are defined in YAML files under the `configurations/` directory and loaded dynamically by OPA. The engine accesses these via the `data.configurations` document.
4.  **Tenant-Awareness**: The engine supports multi-tenancy. It distinguishes between global rules (`configurations/global/rules/data.yaml`), tenant-specific rules (e.g., `configurations/zomato/rules/data.yaml`), and cross-tenant access logic, primarily managed in `rules.main`.
5.  **User Attribute Integration**: Policies can be dynamically applied based on user attributes (e.g., department, LOB, designation). This is primarily handled by `rules.user_attributes` by looking up rules in specially structured paths within `configurations/<tenant>/user_attributes/...` or via regex matching defined in tenant configurations.

## Package Breakdown

### 1. `rules.constants` (`constants.rego`)

-   **Purpose**: Centralizes definitions for common values used across policies to ensure consistency and maintainability.
-   **Key Definitions**:
    -   `privilege_type`: A map of standardized privilege strings (e.g., `"SELECT"`, `"INSERT"`, `"UPDATE"`, `"DELETE"`, `"OWNERSHIP"`, `"EXECUTE"`). This allows policies to refer to privileges in a uniform way.
    -   `access_level`: Defines general access levels for resources (e.g., `"read_only"`, `"all"`, `"none"`). Often used for catalog-level permissions.
    -   `entity_category`: Categorizes the type of entity a rule applies to (e.g., `"data_entity"` for tables/schemas/catalogs, `"query_engine_entity"` for functions or impersonation rules). This helps in scoping rule application.

### 2. `rules.user_attributes` (`user_attributes.rego`)

-   **Purpose**: Manages the dynamic loading and application of policies based on the attributes of the requesting user.
-   **Key Logic & Functions**:
    -   `get_custom_config_paths(tenant_name)`: Retrieves custom configuration paths for user attribute rules specific to a tenant. These paths are typically defined in `data.configurations.[tenant_name].user_attribute_configuration.user_attribute_paths`.
    -   `build_user_attribute_paths(tenant_name, user_attrs)`:
        -   Constructs a list of potential file paths where user attribute-specific rules might be defined (e.g., `configurations/[tenant]/user_attributes/line_of_business/[lob_value]/rules/data.yaml`).
        -   It combines default hierarchical paths (based on attributes like `line_of_business`, `department`, `sub_department`, `designation`) with any custom paths defined for the tenant.
        -   User attribute values are sanitized (e.g., lowercased, spaces replaced with underscores by `utils.generic.sanitize_string`) to form valid path segments.
    -   `build_custom_paths`, `build_dynamic_path`, `substitute_path_variables`, `substitute_single_segment`: These are helper functions. They work together to take a path template (which might include placeholders like `user_attribute_template:department`) and substitute these placeholders with actual, sanitized values from the `user_attrs` input, thereby generating concrete paths to rule files.
    -   `user_attribute_rule_arrays(tenant, user_attributes)`: Loads all rules found at the dynamically generated hierarchical paths for the given user attributes.
    -   `regex_user_attribute_rules(tenant, user_attributes)`:
        -   Loads rules based on regular expression matching against user attributes.
        -   It looks up regex patterns and corresponding rule file paths in `data.configurations.[tenant].user_attribute_configuration.regex_user_attributes`.
        -   If a user's attribute value (e.g., `user_attributes.designation`) matches a defined regex pattern (using `utils.custom_regex.match_entire` for a full string match), the rules from the associated path are loaded.

### 3. `rules.core` (`core.rego`)

-   **Purpose**: Provides the foundational logic for matching input requests (like a query operation) against the granular conditions defined in policy rules. It contains numerous functions, each designed to check a specific type of permission or condition.
-   **Key Helper Functions**:
    -   `match_any_group(group_pattern, groups)`: Checks if any of the user's `groups` match the provided `group_pattern` (which can be a regex).
    -   `match_user_group(rule, user, groups)`: Determines if a given `rule` applies to the current `user` (based on `rule.user`, a regex) or any of their `groups` (based on `rule.group`, a regex).
-   **Core Permission Functions (Illustrative List - see `core.rego` for all definitions)**:
    -   `authorization_permission(grantee_name, user, groups, rules)`: Checks if the `user` (or their `groups`) has the authority to grant/revoke permissions to the specified `grantee_name`.
    -   `catalog_access(catalog_name, user, groups, rules)`: Aggregates and returns the set of access levels (e.g., `all`, `read_only`) a user has on a given `catalog_name`.
    -   `catalog_owner(catalog_name, user, groups, rules)`: Determines if the user is an owner of the `catalog_name`.
    -   `catalog_session_properties_access(...)`: Checks permissions for setting catalog-specific session properties.
    -   `catalog_visibility(catalog_name, user, groups, rules)`: Determines if a catalog should be visible to the user.
    -   `function_privileges(catalog_name, schema_name, function_name, user, groups, rules)`: Returns the set of allowed privileges (e.g., `EXECUTE`) on a specific database function.
    -   `impersonation_access(user, impersonated_user, groups, rules)`: Checks if the current `user` is allowed to impersonate the `impersonated_user`.
    -   `procedure_privileges(...)`: Similar to `function_privileges`, but for database procedures.
    -   `query_access(user, groups, rules)`: Returns allowed general query actions (e.g., `EXECUTE`) for the user.
    -   `query_owned_by_access(principal_user, resource_user, groups, rules)`: Determines if `principal_user` can perform actions (e.g., `VIEW`, `KILL`) on queries owned by `resource_user`.
    -   `schema_owner(catalog_name, schema_name, user, groups, rules)`: Determines if the user owns the specified `schema_name`.
    -   `schema_visibility(catalog_name, schema_name, user, groups, rules)`: Determines schema visibility (special handling for `information_schema`).
    -   `table_privileges(catalog, schema, table, user, groups, rules)`: Returns the set of allowed privileges (e.g., `SELECT`, `INSERT`) on a specific table.
    -   `column_allow(catalog, schema, table, column, user, groups, rules)`: Checks if access to a specific `column` within a table is permitted.
    -   `row_filters(catalog, schema, table, user, groups, rules)`: Retrieves all applicable row filter rules for a given table and user context.
    -   `column_mask(catalog, schema, table, column, user, groups, rules)`: Retrieves the applicable column masking rule for a specific column.
    -   `system_information_access(...)`: Checks access permissions to system-level information.
    -   `system_session_properties_access(...)`: Checks permissions for setting global system-level session properties.

### 4. `rules.main` (`main.rego`)

-   **Purpose**: This is the central orchestrator for policy evaluation. It compiles the relevant set of rules based on the input context (user, resource, action, tenant information) and then uses these rules to derive the final `allow`, `row_filter`, and `column_mask` decisions.
-   **Default Decisions**:
    -   `default allow := false`: Access is denied unless a rule explicitly permits it.
    -   `default row_filter := {"expression": null}`: No row filter is applied by default.
    -   `default column_mask := {"expression": null}`: No column mask is applied by default.
-   **Rule Compilation (`compiled_rules`)**:
    -   This is a critical multi-part rule that dynamically assembles the set of all policy rules applicable to the current request.
    -   It first checks for `mock_rules` (defined in `data.configurations.mock_rules`), which, if present, are used directly for testing purposes, bypassing the standard rule loading logic.
    -   **Same-Tenant Scenario** (when `input.tenant_map.same_tenant == true`):
        -   Loads global rules from `data.configurations.global.rules`.
        -   Generates user attribute-specific rules for the source tenant using `generate_user_attribute_rules` (which internally calls functions from `rules.user_attributes`).
        -   Loads rules specific to the `input.tenant_map.source_tenant` from `data.configurations.[source_tenant].rules`.
        -   All these rule sets (global, tenant-specific, user-attribute-based) are concatenated.
        -   A final filter ensures that for tenant-specific rules, the `tenant` field within the rule definition (converted to an array by `utils.generic.normalize_to_array`) correctly matches the current `tenant_name` (or defaults to it if the field is absent).
    -   **Cross-Tenant Scenario** (when `input.tenant_map.same_tenant != true`):
        -   Loads global rules.
        -   Generates user attribute-specific rules for the `input.tenant_map.source_tenant`.
        -   It iterates through the list of `input.tenant_map.destination_tenant`(s). For each destination tenant:
            -   It loads rules from `data.configurations.[destination_tenant_name].rules`.
            -   These rules are then filtered: only rules where the `rule.tenant` field (normalized to an array) explicitly includes the `input.tenant_map.source_tenant` are considered. This is the mechanism that enables controlled cross-tenant data access.
        -   The global rules, the filtered cross-tenant rules, and user attribute rules of the source tenant are concatenated.
    -   `generate_user_attribute_rules(tenant, user_attributes)`: A helper within `main.rego` that calls `ua_utils.user_attribute_rule_arrays` and `ua_utils.regex_user_attribute_rules` to get all rules derived from user attributes and combines them.
-   **Row Filter Logic (`row_filter`)**:
    -   This is also a multi-part rule; OPA tries each definition until one succeeds.
    -   It starts by calling `core_rules.row_filters` to get all matching row filter rules from the `compiled_rules`.
    -   `fetch_templated_row_filter(tenant, filter_name)`: Loads filter templates from `data.configurations.[tenant].templates.row_level_filters.[filter_name]`. Templates provide reusable filter expressions.
    -   Handles **templated filters**: If a rule specifies `filter_template == true`, the system fetches the named template and substitutes placeholders (defined in `rule.filter_substitution`) into the template expression using `utils.generic.substitute_values`.
    -   Handles **direct filter expressions**: If `filter_template == false`, the `rule.filter` string itself is treated as the filter expression (potentially after placeholder substitution).
    -   Handles `filter_environment`: If a rule includes `filter_environment.user`, the filter is effectively applied as if that specified user is making the request. This is useful for scenarios where a service account needs to apply user-specific row-level security.
    -   If multiple filter rules apply (and `filter_environment` is null for them), their expressions are combined using `AND` logic.
    -   A special case exists: if a filter rule has `is_default: true` (as determined by `core_rules.row_filters`), it can result in a "deny all" filter (e.g., `1=0`).
-   **Column Masking Logic (`column_mask`)**:
    -   Structurally similar to `row_filter`.
    -   It calls `core_rules.column_mask` to get the relevant masking rule for the specified column from `compiled_rules`.
    -   Returns a masking expression (typically an SQL expression like `regexp_replace(...)` or `sha256(...)`) for the requested column.
    -   Also supports `mask_environment` to apply masking logic as if a different user is making the request.
-   **Access Decision Logic (`allow`)**:
    -   This is the primary decision rule, composed of many individual `allow if {...}` blocks. The overall `allow` decision is `true` if *any one* of these blocks evaluates to `true`.
    -   Each block is typically responsible for a specific category of operations or resources:
        1.  It first checks if the `input.operation` (e.g., a Trino operation name) falls into a predefined category (e.g., "schema_operations_ddl", "table_visibility"). This mapping is done by `utils.generic.fetch_operations(input.tool, operation_category_key)`, which looks up operation lists in `data.configurations.global.tools.[input.tool].operations.[operation_category_key]`.
        2.  It then calls relevant permission-checking functions from `rules.core` (e.g., `core_rules.catalog_access`, `core_rules.table_privileges`, `core_rules.column_allow`, `core_rules.impersonation_access`).
        3.  It may apply additional conditions specific to the operation (e.g., for "column_level_select", it iterates through all requested columns and checks `core_rules.column_allow` for each one).
    -   The `allow` rules collectively cover a comprehensive range of data access scenarios, including: schema authorization (grant/revoke), table/view authorization, system information access, catalog access and visibility, session property management, column-level selection and filtering, function execution and DDL, user impersonation, procedure execution, query execution and management, schema DDL and visibility, and table DDL and visibility.

### 5. `utils.generic` (`generic.rego` from `utils/` directory)

-   **Purpose**: Collection of general-purpose utility functions used throughout the policy engine.
-   **Key Functions Used by Core Engine**:
    -   `sanitize_string(str)`: Converts a string to lowercase and replaces spaces with underscores. Used by `rules.user_attributes` for creating consistent path segments from user attribute values.
    -   `fetch_operations(tool, op_type)`: Retrieves lists of operation names (e.g., Trino operation names) that map to a logical operation type (e.g., "schema_operations_ddl"). This data is loaded from `data.configurations.global.tools.[tool].operations.[op_type]`. This function is crucial for `rules.main` to categorize incoming requests from different tools.
    -   `substitute_values(values, template)`: Replaces placeholders in a template string with actual values. Used in `rules.main` for row filtering with templates.
    -   `normalize_to_array(x)`: Ensures a value is an array. If `x` is a string, it's converted to a list containing that string; if `x` is already an array, it's returned as is. Used in `rules.main` for handling the `rule.tenant` field, which can be a single tenant string or a list of tenants.

### 6. `utils.custom_regex` (`custom_regex.rego` from `utils/` directory)

-   **Purpose**: Provides enhanced regular expression capabilities.
-   **Key Functions Used by Core Engine**:
    -   `match_entire(pattern, value)`: Ensures that the `pattern` matches the *entire* `value` string. It achieves this by implicitly adding `^` (start of string) and `$` (end of string) anchors to the provided `pattern` before performing the regex match. This is used in `rules.user_attributes` for precise matching of user attribute values against regex patterns when loading rules.

# Core Policy Evaluation Engine Packages

This document details the Rego packages that form the core of the policy evaluation engine.

## `rules` Package

The `rules` package is central to determining access permissions. It uses OPA (Open Policy Agent) and Rego to evaluate user requests against a configured set of rules.

### `rules.constants.rego`

*   **Purpose:** Defines shared constants for consistent policy evaluation.
*   **Key Constants:**
    *   `privilege_type`: Standardized privileges (e.g., `SELECT`, `INSERT`, `EXECUTE`).
    *   `access_level`: Defines access tiers (e.g., `read-only`, `all`).
    *   `entity_category`: Classifies entities (e.g., `data_entity`, `query_engine_entity`).
*   **Significance:** Ensures uniformity and avoids hardcoded strings in policies.

### `rules.user_attributes.rego`

*   **Purpose:** Manages the loading and processing of rules based on user attributes.
*   **Key Logic & Functions:**
    *   `get_custom_config_paths(tenant_name)`: Fetches custom configuration paths for tenant-specific user attribute rules.
    *   `build_user_attribute_paths(tenant_name, user_attrs)`: Constructs hierarchical paths (default and custom) to locate rules relevant to user attributes (e.g., line_of_business, department).
    *   `build_custom_paths(tenant_name, user_attrs, custom_config)`: Generates rule paths from custom configurations.
    *   `build_dynamic_path(tenant_name, user_attrs, path_template)`: Creates rule paths by substituting variables in templates with user attribute values.
    *   `substitute_path_variables` & `substitute_single_segment`: Helpers for replacing placeholders in path templates.
    *   `user_attribute_rule_arrays(tenant, user_attributes)`: Loads all rules from determined paths based on user attributes.
    *   `regex_user_attribute_rules(tenant, user_attributes)`: Loads rules by matching user attributes against regular expressions, enabling flexible rule targeting.
*   **Significance:** Facilitates fine-grained, attribute-based access control and dynamic rule discovery.

### `rules.core.rego`

*   **Purpose:** Contains the fundamental evaluation logic for various access checks.
*   **Key Functions & Logic (Illustrative Examples):**
    *   `match_any_group`, `match_user_group`, `match_original_user_group`: Check user group memberships.
    *   `authorization_permission(grantee_name, user, groups, rules)`: Verifies if a user can grant permissions.
    *   `catalog_access(catalog_name, user, groups, rules)`: Determines access level to a catalog.
    *   `catalog_owner(catalog_name, user, groups, rules)`: Checks catalog ownership.
    *   `catalog_session_properties_access(catalog_name, property_name, user, groups, rules)`: Validates access to set catalog session properties.
    *   `catalog_visibility(catalog_name, user, groups, rules)`: Determines if a catalog is visible.
    *   `function_privileges(catalog_name, schema_name, function_name, user, groups, rules)`: Aggregates user privileges for a function.
    *   `impersonation_access(user, impersonated_user, groups, rules)`: Checks if impersonation is allowed.
    *   `procedure_privileges`: Similar to `function_privileges`, for procedures.
    *   `query_access(user, groups, rules)`: Determines allowed query actions.
    *   `query_owned_by_access(principal_user, resource_user, groups, rules)`: Defines actions on queries owned by others.
    *   `schema_owner(catalog_name, schema_name, user, groups, rules)`: Checks schema ownership.
    *   `schema_visibility(catalog_name, schema_name, user, groups, rules)`: Determines schema visibility.
*   **Evaluation Principle:** Most functions default to denying access if no explicit rule grants permission.
*   **Significance:** Implements the core access control logic for diverse resources and actions.

### `rules.main.rego`

*   **Purpose:** Serves as the primary entry point for policy decisions, consolidating rules and producing final outcomes (`allow`, `row_filter`, `column_mask`).
*   **Key Logic & Components:**
    *   **Defaults:** `default allow := false`, `default row_filter := {"expression": null}`, `default column_mask := {"expression": null}`.
    *   **Rule Compilation (`compiled_rules`):**
        *   Aggregates global, tenant-specific, and user attribute-based rules.
        *   `generate_user_attribute_rules`: Consolidates rules from direct and regex-based user attribute paths.
        *   Differentiates between same-tenant and cross-tenant requests.
        *   Supports `mock_rules` for testing scenarios.
    *   **Row Filtering (`row_filter`):**
        *   Retrieves applicable row filter rules (e.g., via `core_rules.row_filters`).
        *   `fetch_templated_row_filter`: Fetches templated filter expressions.
        *   `generic.substitute_values`: Populates filter templates with dynamic values.
        *   Combines multiple filter expressions, often with AND logic.
        *   Defaults to a restrictive filter (e.g., `1=0`) if no permissive filter applies.
    *   **Column Masking (`column_mask`):**
        *   Applies similar logic as row filtering to determine column masking expressions.
    *   **Main `allow` Decision:**
        *   A series of `allow if {...}` statements, each corresponding to a specific operation type (e.g., catalog, schema, table, query, impersonation).
        *   Leverages `input.privilege` and `input.entity_category` to invoke appropriate checks in `rules.core`.
        *   Access is granted if *any* relevant `allow` condition is met.
*   **Significance:** Orchestrates the policy evaluation pipeline, from rule aggregation to final decision making and data transformation directives.

## `utils` Package (Supporting Utilities)

These packages provide helper functions used by the `rules` package and other policy files.

### `utils.generic.rego`

*   **Purpose:** Offers general-purpose utility functions.
*   **Key Functions:**
    *   `sanitize_string(str)`: Normalizes strings (lowercase, spaces to underscores).
    *   `fetch_operations(tool, op_type)`: Retrieves operation mappings for tools.
    *   `invalid_priority_rules(rules)`: Validates priority rule configurations.
    *   `substitute_values(values, template)`: Replaces placeholders in templates.
    *   `handle_null_value(value, default_value)`: Provides a default for null values.
    *   `normalize_to_array(x)`: Ensures a value is an array.
*   **Significance:** Enhances code reusability and provides common data manipulation tools.

### `utils.custom_regex.rego`

*   **Purpose:** Extends Rego's built-in regular expression capabilities.
*   **Key Functions:**
    *   `match_entire(pattern, value)`: Matches an entire string against a regex pattern by adding `^` and `$` anchors.
*   **Significance:** Simplifies full-string regex matching in policy definitions.
