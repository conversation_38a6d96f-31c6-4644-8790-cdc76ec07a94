package rules.constants

import rego.v1

privilege_type := {
	"SELECT": "SELECT",
	"INSERT": "INSERT",
	"UPDATE": "UPDATE",
	"DELETE": "DELETE",
	"OWNERSHIP": "OWNERSHIP",
	"EXECUTE": "EXECUTE",
	"GRANT_EXECUTE": "GRANT_EXECUTE",
}

access_level := {
	"read_only": "read-only",
	"all": "all",
	"none": "none",
}

entity_category := {
	"data_entity": "data_entity",
	"query_engine_entity": "query_engine_entity",
}

# rules_types : data.tools[input.tool].operation_privilege_mapping
