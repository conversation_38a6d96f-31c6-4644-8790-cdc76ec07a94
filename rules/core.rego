package rules.core

import data.configurations.global.rules
import rego.v1

import data.rules.dynamic_rules

import data.rules.constants
import data.utils.custom_regex as util
import data.utils.generic as generic_utils
import data.utils.constants as util_constants

default match_any_group(_, _) := false

default match_user_group(_, _, _) := false

default match_original_user_group(_, _, _) := false

match_any_group(group_pattern, groups) if {
	some group in groups
	util.match_entire(group_pattern, group)
}

match_user_group(rule, user, groups) if {
	util.match_any_user(rule, "user", user)
	util.match_any_group(rule, "group", groups)
}

match_original_user_group(rule, user, groups) if {
	user_pattern := object.get(rule, "original_user", ".*")
	util.match_entire(user_pattern, user)

	group_pattern := object.get(rule, "original_group", ".*")
	match_any_group(group_pattern, groups)
}

# Evaluate the global policies under configuration/rules

default authorization_permission(_, _, _, _) := false

# This covers the data entities
# --- Authorization (Grant/Revoke) ---
# Checks if ANY rule allows the original user/group to grant to the new user.
authorization_permission(grantee_name, user, groups, rules) if {
	some rule in rules
	rule.entity_category == constants.entity_category.data_entity
	match_original_user_group(rule, user, groups)
	new_user_pattern := object.get(rule, "new_user", ".*")
	util.match_entire(new_user_pattern, grantee_name)
	object.get(rule, "allow", false) == true # Check if this specific rule allows
}

# --- Catalog Access ---
catalog_access_map := {
	"all": {"all", "read-only"},
	"read-only": {"read-only"},
	"none": {"none"},
}

default catalog_access(_, _, _, _) := {"none"} # Default if no rule grants any access

# Aggregate all access levels granted by matching rules.
# The final result is the union of levels ('all' implies 'read-only').
catalog_access(catalog_name, user, groups, rules) := {level |
	some rule in rules
	rule.type == "catalog"
	rule.entity_category == constants.entity_category.data_entity
	match_user_group(rule, user, groups)
	catalog_pattern := object.get(rule, "catalog", ".*")
	util.match_entire(catalog_pattern, catalog_name)

	allow_value := object.get(rule, "allow", "none") # Default to 'none' if 'allow' is missing
	some level in catalog_access_map[allow_value]
}

# --- Catalog Ownership ---
# Checks if ANY rule grants ownership for the catalog.
default catalog_owner(_, _, _, _) := false

catalog_owner(catalog_name, user, groups, rules) if {
	some rule in rules
	rule.type == "catalog"
	rule.entity_category == constants.entity_category.data_entity
	match_user_group(rule, user, groups)
	catalog_pattern := object.get(rule, "catalog", ".*")
	util.match_entire(catalog_pattern, catalog_name)
	object.get(rule, "owner", false) == true # Check if this rule grants ownership
}

# --- Catalog Session Properties ---
# Checks if ANY rule allows setting the property.
default catalog_session_properties_access(_, _, _, _, _) := false

catalog_session_properties_access(catalog_name, property_name, user, groups, rules) if {
	some rule in rules
	rule.type == "session_property_operation"
	rule.entity_category == constants.entity_category.data_entity
	match_user_group(rule, user, groups)
	catalog_pattern := object.get(rule, "catalog", ".*")
	util.match_entire(catalog_pattern, catalog_name)
	property_pattern := object.get(rule, "property", ".*")
	util.match_entire(property_pattern, property_name)
	object.get(rule, "allow", false) == true # Check if this rule allows
}

# --- Catalog Visibility ---
# Catalog is visible if ANY matching rule grants 'all' access,
# OR if 'read-only' is granted AND ANY schema/table/function/procedure/session property rule exists for that catalog.
default catalog_visibility(_, _, _, _) := false

catalog_visibility(catalog_name, user, groups, rules) if {
	constants.access_level.all in catalog_access(catalog_name, user, groups, rules)
}

catalog_visibility(catalog_name, user, groups, rules) if {
	catalog_access(catalog_name, user, groups, rules) == {constants.access_level.read_only} # Only read-only granted (not 'all')

	# Check if any schema rule grants ownership within this catalog
	some rule in rules
	rule.type == "schema"
	rule.entity_category == constants.entity_category.data_entity
	match_user_group(rule, user, groups)
	catalog_pattern_s := object.get(rule, "catalog", ".*")
	util.match_entire(catalog_pattern_s, catalog_name)
	object.get(rule, "owner", false) == true
}

catalog_visibility(catalog_name, user, groups, rules) if {
	catalog_access(catalog_name, user, groups, rules) == {constants.access_level.read_only}

	# Check if any table/function/procedure rule grants privileges within this catalog
	some rule in rules
	rule.type in ["table", "function"]
	match_user_group(rule, user, groups)
	catalog_pattern_tfp := object.get(rule, "catalog", ".*")
	util.match_entire(catalog_pattern_tfp, catalog_name)
	count(object.get(rule, "privileges", [])) > 0
}

catalog_visibility(catalog_name, user, groups, rules) if {
	catalog_access(catalog_name, user, groups, rules) == {constants.access_level.read_only}

	# Check if any session property rule allows setting properties within this catalog
	some rule in rules
	rule.type == "session_property_operation"
	rule.entity_category == constants.entity_category.data_entity
	match_user_group(rule, user, groups)
	catalog_pattern_p := object.get(rule, "catalog", ".*")
	util.match_entire(catalog_pattern_p, catalog_name)
	object.get(rule, "allow", false) == true
}

# --- Function Privileges ---
# Aggregate all function privileges granted by matching rules.
default function_privileges(_, _, _, _, _, _) := set()

function_privileges(catalog_name, schema_name, function_name, user, groups, rules) := {priv |
	some rule in rules
	rule.type == "function"
	rule.entity_category == constants.entity_category.query_engine_entity
	match_user_group(rule, user, groups)
	catalog_pattern := object.get(rule, "catalog", ".*")
	schema_pattern := object.get(rule, "schema", ".*")
	function_pattern := object.get(rule, "function", ".*")
	util.match_entire(catalog_pattern, catalog_name)
	util.match_entire(schema_pattern, schema_name)
	util.match_entire(function_pattern, function_name)

	some priv in object.get(rule, "privileges", []) # Union of privileges
}

# --- Impersonation ---
# Checks if ANY rule allows the original user/group to impersonate the target user.
default impersonation_access(_, _, _, _) := false

impersonation_access(user, impersonated_user, _, _) if {
	impersonated_user == user # Always allow self-impersonation
}

impersonation_access(user, impersonated_user, groups, rules) if {
	impersonated_user != user
	some rule in rules
	rule.type == "impersonation"
	rule.entity_category == constants.entity_category.query_engine_entity
	match_original_user_group(rule, user, groups)

	original_user_pattern := object.get(rule, "original_user", ".*")
	unsubstituted_new_user_pattern := object.get(rule, "new_user", ".*")

	# Substitution logic remains the same as it applies per-rule
	matches := regex.find_all_string_submatch_n(original_user_pattern, user, -1)
	substitutes := {var: match | some i, match in matches[0]; var := concat("", ["$", format_int(i, 10)])}
	new_user_pattern := strings.replace_n(substitutes, unsubstituted_new_user_pattern)

	util.match_entire(new_user_pattern, impersonated_user)
	object.get(rule, "allow", false) == true # Check if this rule allows
}

# --- Procedure Privileges ---
# Aggregate all procedure privileges granted by matching rules.
# Note: Matching the "function name" with the "procedure pattern" is intended.
default procedure_privileges(_, _, _, _, _, _) := set()

procedure_privileges(catalog_name, schema_name, function_name, user, groups, rules) := {priv |
	some rule in rules
	rule.type == "function"
	rule.entity_category == constants.entity_category.query_engine_entity
	match_user_group(rule, user, groups)
	catalog_pattern := object.get(rule, "catalog", ".*")
	schema_pattern := object.get(rule, "schema", ".*")
	procedure_pattern := object.get(rule, "procedure", ".*") # Uses procedure pattern
	util.match_entire(catalog_pattern, catalog_name)
	util.match_entire(schema_pattern, schema_name)
	util.match_entire(procedure_pattern, function_name) # Matches against function_name

	some priv in object.get(rule, "privileges", []) # Union of privileges
}

# --- Query Access (Execute) ---
# Aggregate all allowed query actions ('execute') from matching rules.
default query_access(_, _, _) := set()

query_access(user, groups, rules) := {upper(access) |
	some rule in rules
	rule.type == "query"
	rule.entity_category == constants.entity_category.query_engine_entity
	match_user_group(rule, user, groups)

	# Check if rule applies generally (no queryOwner constraint specific to this rule)
	not object.get(rule, "queryOwner", false)

	some access in object.get(rule, "allow", [])
}

# --- Query Owned By Access (View/Kill) ---
default query_owned_by_access(_, _, _, _) := set() # Default for other users if no rule matches

query_owned_by_access(principal_user, user, _, _) := {"KILL", "VIEW"} if {
	user == principal_user # Always allow view/kill own queries
}

query_owned_by_access(principal_user, resource_user, groups, rules) := access_set if {
	resource_user != principal_user
	access_set := {access |
		some rule in rules
		rule.type == "query"
		rule.entity_category == constants.entity_category.query_engine_entity
		match_user_group(rule, principal_user, groups)

		# Check if rule applies to the specific query owner
		query_owner_pattern := object.get(rule, "queryOwner", ".*") # Default means applies if queryOwner key exists
		util.match_entire(query_owner_pattern, resource_user)

		some access in object.get(rule, "allow", [])
	}
}

# --- Schema Ownership ---
# Checks if ANY rule grants ownership for the schema.
default schema_owner(_, _, _, _, _) := false

schema_owner(catalog_name, schema_name, user, groups, rules) if {
	some rule in rules
	rule.type == "schema"
	rule.entity_category == constants.entity_category.data_entity
	match_user_group(rule, user, groups)
	catalog_pattern := object.get(rule, "catalog", ".*")
	util.match_entire(catalog_pattern, catalog_name)
	schema_pattern := object.get(rule, "schema", ".*")
	util.match_entire(schema_pattern, schema_name)
	object.get(rule, "owner", false) == true
}

# --- Schema Visibility ---
# Schema is visible if user owns it OR it's information_schema OR
# ANY table/function/procedure rule grants privileges within it.
default schema_visibility(_, _, _, _, _) := false

schema_visibility(catalog_name, schema_name, user, groups, rules) if {
	schema_owner(catalog_name, schema_name, user, groups, rules) # Owner based on aggregated check
}

schema_visibility(_, "information_schema", _, _, _) := true

schema_visibility(catalog_name, schema_name, user, groups, rules) if {
	schema_name != "information_schema"
	not schema_owner(catalog_name, schema_name, user, groups, rules) # Only check if not owner

	some rule in rules
	rule.type in ["table", "function"]
	match_user_group(rule, user, groups)
	catalog_pattern := object.get(rule, "catalog", ".*")
	util.match_entire(catalog_pattern, catalog_name)
	schema_pattern := object.get(rule, "schema", ".*")
	util.match_entire(schema_pattern, schema_name)
	count(object.get(rule, "privileges", [])) > 0 # Check if this rule grants *any* privilege
}

# --- Table Privileges & Column Access ---
default table_privileges(_, _, _, _, _, _) := set()

# Aggregate all table privileges granted by matching rules.
table_privileges(catalog_name, schema_name, table_name, user, groups, rules) := privileges if {
	# Handle information_schema separately
	schema_name == "information_schema"
	privileges := {"DELETE", "GRANT_SELECT", "INSERT", "OWNERSHIP", "SELECT", "UPDATE"}
} else := privileges if {
	schema_name != "information_schema"
	matching_rules := {rule |
		some rule in rules
		rule.type == "table"
		rule.entity_category == constants.entity_category.data_entity
		match_user_group(rule, user, groups)
		catalog_pattern := object.get(rule, "catalog", ".*")
		schema_pattern := object.get(rule, "schema", ".*")
		table_pattern := object.get(rule, "table", ".*")
		util.match_entire(catalog_pattern, catalog_name)
		util.match_entire(schema_pattern, schema_name)
		util.match_entire(table_pattern, table_name)
	}

	# trace(sprintf("Matching rules for %s.%s.%s: %v", [catalog_name, schema_name, table_name, matching_rules]))

	# Verifies no matching rules has privilege empty (fails early)
	not generic_utils.any_empty_privileges(matching_rules)
	dynamic_privileges := dynamic_rules.dynamic_table_rule(user, groups, catalog_name, schema_name, table_name)
	all_matching_rules := matching_rules | dynamic_privileges

	privileges := {priv |
		some rule in all_matching_rules
		some priv in object.get(rule, "privileges", [])
	}
}

# Helper function to get matching table rules
fetch_matching_table_rules(catalog_name, schema_name, table_name, user, groups, rules) := {rule |
	some rule in rules
	rule.type == "table"
	rule.entity_category == constants.entity_category.data_entity
	match_user_group(rule, user, groups)
	catalog_pattern := object.get(rule, "catalog", ".*")
	schema_pattern := object.get(rule, "schema", ".*")
	table_pattern := object.get(rule, "table", ".*")
	util.match_entire(catalog_pattern, catalog_name)
	util.match_entire(schema_pattern, schema_name)
	util.match_entire(table_pattern, table_name)
}

# --- Column ---
column_spec_from_policy(catalog_name, schema_name, table_name, column_name, user, groups, rules) := col_spec if {
	schema_name == "information_schema"
	col_spec := {"allow": true, "mask": null}
} else := col_spec if {
	schema_name != "information_schema"

	# Get all matching table rules
	matching_table_rules := fetch_matching_table_rules(catalog_name, schema_name, table_name, user, groups, rules)
	filtered_matching_table_rules := generic_utils.fetch_priority_rule(matching_table_rules)

	# Get all matching column specs from those rules
	matching_col_specs := [c |
		some table_rule in filtered_matching_table_rules
		some c in object.get(table_rule, "columns", [])
		c.name == column_name
	]

	dynamic_privileges := dynamic_rules.dynamic_table_rule(user, groups, catalog_name, schema_name, table_name)
	all_matching_rules := filtered_matching_table_rules | dynamic_privileges

	# Handle different scenarios based on rule and column spec counts
	col_spec := handle_column_spec_scenarios(all_matching_rules, matching_col_specs)
}

# Helper function to handle different column spec scenarios
handle_column_spec_scenarios(matching_table_rules, matching_col_specs) := col_spec if {
	# Case 1: Exactly one column spec found
	count(matching_col_specs) == 1
	col_spec := matching_col_specs[0]
} else := col_spec if {
	# Case 2: Multiple column specs found (conflicting rules)
	count(matching_col_specs) > 1
	col_spec := {"allow": true, "mask": "'Rules are not mutually exclusive'"}
} else := col_spec if {
	# Case 3: No column specs found but table rules exist (implicit allow)
	count(matching_col_specs) == 0
	count(matching_table_rules) >= 1
	col_spec := {"allow": true, "mask": null}
}

column_allow(catalog_name, schema_name, table_name, column_name, user, groups, rules) := allow if {
	col_spec := column_spec_from_policy(catalog_name, schema_name, table_name, column_name, user, groups, rules)
	allow := object.get(col_spec, "allow", false)
}

column_mask(catalog_name, schema_name, table_name, column_name, user, groups, rules) := mask if {
	col_spec := column_spec_from_policy(catalog_name, schema_name, table_name, column_name, user, groups, rules)
	mask := {"mask": object.get(col_spec, "mask", null), "mask_environment": object.get(col_spec, "mask_environment", null)}
}

# --- System Information Access ---
# Aggregate all allowed system information actions ('read', 'write') from matching rules.
default system_information_access(_, _, _) := set()

system_information_access(user, groups, rules) := {access |
	some rule in rules
	rule.type == "system_information"
	rule.entity_category == constants.entity_category.query_engine_entity
	match_user_group(rule, user, groups)
	some access in object.get(rule, "allow", [])
}

# --- System Session Properties ---
# Checks if ANY rule allows setting the property.
default system_session_properties_access(_, _, _, _) := false

system_session_properties_access(property_name, user, groups, rules) if {
	some rule in rules
	rule.type == "session_property_operation"
	rule.entity_category == constants.entity_category.data_entity
	match_user_group(rule, user, groups)
	property_name_pattern := object.get(rule, "property", ".*")
	util.match_entire(property_name_pattern, property_name)
	object.get(rule, "allow", false) == true # Check if this rule allows
}

default row_level_filters(_, _, _, _, _, _) := [{"filter": null, "filter_environment": null, "filter_substitution": null, "is_default": true}] # Default if no rule matches

# Helper function to get source filtered rules
get_source_filtered_rules(rules, source) := filtered_rules if {
	source == util_constants.blinkit_superset_source
	filtered_rules := [rule |
		some rule in rules
		object.get(rule, "source", "") == util_constants.blinkit_superset_source
	]
}

get_source_filtered_rules(rules, source) := filtered_rules if {
	source != util_constants.blinkit_superset_source
	filtered_rules := [rule | 
		some rule in rules
		object.get(rule, "source", "") != util_constants.blinkit_superset_source
	]
}



row_filters(catalog_name, schema_name, table_name, user, groups, rules, source) := filters if {
	filtered_rules := get_source_filtered_rules(rules, source)
	matching_rules := [rule |
		some rule in filtered_rules
		rule.type == "table"
		rule.entity_category == constants.entity_category.data_entity
		match_user_group(rule, user, groups)
		catalog_pattern := object.get(rule, "catalog", ".*")
		schema_pattern := object.get(rule, "schema", ".*")
		table_pattern := object.get(rule, "table", ".*")
		util.match_entire(catalog_pattern, catalog_name)
		util.match_entire(schema_pattern, schema_name)
		util.match_entire(table_pattern, table_name)
		filter := object.get(rule, "filter", null) # Default null if no filter found
		filter_environment := object.get(rule, "filter_environment", null) # Default null if no environment found
		filter_template := object.get(rule, "filter_template", false) # Default null if no template found
	]

	# trace(sprintf("Matching rules for %s.%s.%s: %v", [catalog_name, schema_name, table_name, matching_rules]))

	# TODO: return 1=0 if filter has invalid priority rule
	filters := [{"filter": filter, "filter_environment": filter_environment, "filter_substitution": filter_substitution, "filter_template": filter_template} |
		some rule in generic_utils.fetch_priority_rule(matching_rules)
		filter := object.get(rule, "filter", null) # Default null if no filter found
		filter_environment := object.get(rule, "filter_environment", null) # Default null if no environment found
		filter_substitution := object.get(rule, "filter_substitution", {}) # Default null if no substitution found
		filter_template := object.get(rule, "filter_template", false) # Default false
	]
}


## ---------- Source based handling -----------
source_specific_access(source, operation) := true if {
	print("source in core", source)
	print("source in utils constants", util_constants.blinkit_superset_source)
	print("operation in core", operation == "SelectFromColumns")
	source == util_constants.blinkit_superset_source
	operation in ["ReadSystemInformation", "CreateViewWithSelectFromColumns", "AccessCatalog", "GetColumnMask", "GetRowFilters", "SelectFromColumns"]
}