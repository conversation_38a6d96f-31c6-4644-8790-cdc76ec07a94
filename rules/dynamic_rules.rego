package rules.dynamic_rules

import rego.v1

import data.utils.custom_regex

default allow := false

# Table Level Dynamic Rule
dynamic_table_rule(user, groups, catalog, schema, table) := privilege if {
	some group in groups
	startswith(group, "dynamic_rule:")

	group_split := split(group, ":")
	group_split[2] == "table"

	rule := split(group_split[1], "|")

	match_rule_components(rule[0], rule[1], rule[2], catalog, schema, table)

	privilege := {{"privileges": ["SELECT"]}}
} else := set()

match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table) if {
	rule_catalog == "blinkit"
	custom_regex.match_entire("blinkit.*", actual_catalog)
	custom_regex.match_entire(concat("", ["(lake_)?", rule_schema]), actual_schema)
	rule_table == actual_table
} else if {
	rule_catalog != "blinkit"
	rule_catalog == actual_catalog
	rule_schema == actual_schema
	rule_table == actual_table
}
