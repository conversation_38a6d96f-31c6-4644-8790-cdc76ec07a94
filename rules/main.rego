package rules.main

import rego.v1

import data.utils.generic

import data.rules.constants
import data.rules.core as core_rules
import data.rules.user_attributes as ua_utils

import data.configurations

import data.utils.custom_regex

import data.utils.constants as util_constants

# Default deny
default allow := false

default row_filter := []

default column_mask := []

# We get the rules from the configurations basis the tenant as well as the global rules
# Broadly we've to server the following rules:
# 1. Global rules
# 2. Tenant specific rules (This are attributed with a tenant param in the rule itself on each level)
# 3. User Attribute based rules

# Paths for these could be:
# These are the default paths which will be picked and scoped.
# configurations/<tenant_name>/<line_of_business>/rules/data.yaml
# configurations/<tenant_name>/<line_of_business>/<department>/rules/data.yaml
# configurations/<tenant_name>/<line_of_business>/<department>/<sub_department>/rules/data.yaml
# configurations/<tenant_name>/<line_of_business>/<department>/<designation>/rules/data.yaml

# You can override these paths in the configurations file.
# configurations_file -> configurations/<tenant_name>/config/data.yaml
# This path will can be used to handle regex basis user attribute values, i.e designation could be .*key_account_manager.*

# Combine all user attribute rules
generate_user_attribute_rules(tenant, user_attributes) := combined_rules if {
	flat_rules := [rule |
		some arr in ua_utils.user_attribute_rule_arrays(tenant, user_attributes)
		some rule in arr
	]
	combined_rules := array.concat(flat_rules, ua_utils.regex_user_attribute_rules(tenant, user_attributes))
}

# Used for testing purposes
mock_rules := object.get(configurations, ["mock_rules"], false)

compiled_rules := rules if {
	not mock_rules
	input.tenant_map.same_tenant == true
	source_tenants := object.get(input, ["tenant_map", "source_tenant"], [])

	global_rules := object.get(configurations, ["global", "rules"], [])

	user_attribute_rules := [rule |
		some source_tenant in source_tenants
		user_rules_for_tenant := generate_user_attribute_rules(source_tenant, input.user_attributes)
		some rule in user_rules_for_tenant
	]

	tenants_list := input.tenant_map.source_tenant
	all_tenant_rules := [rule |
		some tenant_name in tenants_list
		tenant_specific_rules := object.get(configurations, [tenant_name, "rules"], [])
		all_custom_roles := array.concat(tenant_specific_rules, user_attribute_rules)

		# trace(sprintf("Tenant: %s, Rules: %v", [tenant_name, all_custom_roles]))
		some rule in all_custom_roles
		tenant_name in generic.normalize_to_array(object.get(rule, "tenant", [tenant_name]))
	]
	rules := array.concat(
		global_rules,
		all_tenant_rules,
	)
} else := rules if {
	not mock_rules
	input.tenant_map.same_tenant != true

	global_rules := object.get(configurations, ["global", "rules"], [])
	source_tenant_set := {t | some t in input.tenant_map.source_tenant}
	destination_tenants := object.get(input, ["tenant_map", "destination_tenant"], [])
	source_tenants := object.get(input, ["tenant_map", "source_tenant"], [])

	user_attribute_rules := [rule |
		some dest_tenant in destination_tenants

		# TODO: Add test coverage for this
		user_rules_for_tenant := generate_user_attribute_rules(dest_tenant, input.user_attributes)
		some rule in user_rules_for_tenant
	]

	all_config_rules := [rule |
		some dest_tenant in destination_tenants
		config_rules := object.get(configurations, [dest_tenant, "rules"], [])
		some rule in config_rules
	]

	matching_rules := [rule |
		some rule in array.concat(all_config_rules, user_attribute_rules)
		rule_tenant_set := {t | some t in generic.normalize_to_array(object.get(rule, "tenant", ["nulled"]))}
		count(rule_tenant_set & source_tenant_set) > 0
	]
	rules := array.concat(global_rules, matching_rules)
}

compiled_rules := mock_rules if {
	mock_rules
}

# Row Filter Implementation where just filter is present

fetch_templated_row_filter(tenants, filter_name) := [templated_filter |
	some tenant in tenants
	filter := object.get(
		configurations,
		[tenant, "templates", "row_level_filters", filter_name],
		{"template": "", "default": ""},
	)

	templated_filter := object.get(filter, "template", object.get(filter, "default", null))
]

row_filter := rf if {
	filter_rules := core_rules.row_filters(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules, input.source)

	# Apply variable substitution if needed and combine filters
	# Get the templated filter expression
	tenants := input.tenant_map.source_tenant
	filters := [
	{"expression": generic.substitute_values(
		rule.filter_substitution,
		fetch_templated_row_filter(tenants, rule.filter)[0],
	)} |
		rule := filter_rules[_]
		is_string(rule.filter)
		is_null(rule.filter_environment)

		# rule.filter_substitution != {}
		rule.filter_template == true
	]

	count(filters) == 1
	rf := filters[0]
}

row_filter := rf if {
	filter_rules := core_rules.row_filters(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules, input.source)

	# Apply variable substitution if needed and combine filters
	# Get the templated filter expression
	filters := [{"expression": generic.substitute_values(rule.filter_substitution, rule.filter)} | rule := filter_rules[_]; is_string(rule.filter); is_null(rule.filter_environment); rule.filter_template == false]
	count(filters) == 1
	rf := filters[0]
}

# Row Filter Implementation where filter and filter_environment is present
row_filter := rf if {
	filter_rules := core_rules.row_filters(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules, input.source)
	filters := [{"expression": generic.substitute_values(rule.filter_substitution, rule.filter), "identity": rule.filter_environment.user} | rule := filter_rules[_]; is_string(rule.filter); is_string(rule.filter_environment.user)]
	count(filters) == 1
	rf := filters[0]
}

# Row Filter Implementation where we combine rules when filter_environment is null
row_filter := {"expression": combined_expression} if {
	filter_rules := core_rules.row_filters(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules, input.source)

	filters := [{"expression": generic.substitute_values(rule.filter_substitution, rule.filter)} | rule := filter_rules[_]; is_string(rule.filter); is_null(rule.filter_environment)]

	# Case where we have multiple filters
	count(filters) > 1

	# Combine expressions with AND
	expressions := [f.expression | f := filters[_]]
	combined_expression := concat(" AND ", expressions)
}

row_filter := {"expression": "1=0"} if {
	filter_rules := core_rules.row_filters(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules, input.source)
	count(filter_rules) > 0
	object.get(filter_rules[0], "is_default", false) == true
}

# Column Masking
column_mask := cm if {
	columnName := input.action.resource.column.columnName
	mask_rule := core_rules.column_mask(input.catalog, input.schema, input.table, columnName, input.user, input.groups, compiled_rules)

	is_string(mask_rule.mask)
	is_null(mask_rule.mask_environment)

	cm := {"expression": mask_rule.mask}
}

column_mask := cm if {
	columnName := input.action.resource.column.columnName
	mask_rule := core_rules.column_mask(input.catalog, input.schema, input.table, columnName, input.user, input.groups, compiled_rules)

	is_string(mask_rule.mask)
	is_string(mask_rule.mask_environment.user)

	cm := {"expression": mask_rule.mask, "identity": mask_rule.mask_environment.user}
}

# We will be having multiple allow rules, so we need to check if any of them is true
# Source specific paths 
allow if {
	core_rules.source_specific_access(input.source, input.operation)
}
allow if {
	# For Schema authoriszation
	input.operation in generic.fetch_operations(input.tool, "schema_authorization")

	constants.access_level.all in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)
	core_rules.schema_owner(input.catalog, input.schema, input.user, input.groups, compiled_rules)

	grantee_name := input.action.grantee.name

	# grantee_type := input.action.grantee.type
	core_rules.authorization_permission(grantee_name, input.user, input.groups, compiled_rules)
}

allow if {
	# For Table/View authoriszation
	input.operation in generic.fetch_operations(input.tool, "table_authorization")

	constants.access_level.all in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)
	input.privilege in core_rules.table_privileges(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules)

	grantee_name := input.action.grantee.name

	# grantee_type := input.action.grantee.type
	core_rules.authorization_permission(grantee_name, input.user, input.groups, compiled_rules)
}

allow if {
	# For System Information
	input.operation in generic.fetch_operations(input.tool, "system_operations")
	input.privilege in core_rules.system_information_access(input.user, input.groups, compiled_rules)
}

allow if {
	# For Access Catalog Permissions
	input.operation in generic.fetch_operations(input.tool, "catalog_access")

	constants.access_level.read_only in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)
}

allow if {
	# For Catalog Session Properties
	input.operation in generic.fetch_operations(input.tool, "catalog_session_properties")

	constants.access_level.read_only in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	catalog_session_property_catalog_name := input.action.resource.catalogSessionProperty.catalogName
	catalog_session_property_name := input.action.resource.catalogSessionProperty.propertyName

	core_rules.catalog_session_properties_access(
		catalog_session_property_catalog_name,
		catalog_session_property_name,
		input.user,
		input.groups,
		compiled_rules,
	)
}

allow if {
	# This controls the catalog visibility
	input.operation in generic.fetch_operations(input.tool, "catalog_visibility")

	constants.access_level.read_only in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)
	core_rules.catalog_visibility(input.catalog, input.user, input.groups, compiled_rules)
}

allow if {
	# This handles column level access
	input.operation in generic.fetch_operations(input.tool, "column_level_select")

	# Check if the user has access to the catalog
	constants.access_level.read_only in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	# Check if user has access to the table & any valid privileges
	valid_privileges := core_rules.table_privileges(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules)
	count(object.filter(constants.privilege_type, valid_privileges)) > 0

	# trace(sprintf("compiled rules: %v", [compiled_rules]))

	# Check if the user has access to the columns
	columns := object.get(input.action, ["resource", "table", "columns"], [])
	every column in columns {
		core_rules.column_allow(input.catalog, input.schema, input.table, column, input.user, input.groups, compiled_rules)
	}
}

allow if {
	input.operation in generic.fetch_operations(input.tool, "column_filtering")

	# Check if user has access to the table & any valid privileges
	valid_privileges := core_rules.table_privileges(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules)
	count(object.filter(constants.privilege_type, valid_privileges)) > 0

	# Check if the user has access to the columns
	column := input.action.resource.table.columnName
	core_rules.column_allow(
		input.catalog,
		input.schema,
		input.table,
		column,
		input.user,
		input.groups,
		compiled_rules,
	)
}

allow if {
	# This handles function level access for OWNERSHIP
	input.operation in generic.fetch_operations(input.tool, "functions_operations_ddl")

	# Check if the user has access to the catalog
	constants.access_level.all in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	# Check the privilege on the function
	function_name := input.action.resource.function.functionName
	input.privilege in core_rules.function_privileges(input.catalog, input.schema, function_name, input.user, input.groups, compiled_rules)
}

allow if {
	# This handles function level access for EXECUTE
	input.operation in generic.fetch_operations(input.tool, "functions_operations_dql_extn")

	# Check if the user has access to the catalog
	constants.access_level.read_only in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	# Check the privilege on the function
	function_name := input.action.resource.function.functionName
	input.privilege in core_rules.function_privileges(input.catalog, input.schema, function_name, input.user, input.groups, compiled_rules)
}

allow if {
	# This handles for impersonation
	input.operation in generic.fetch_operations(input.tool, "impersonation")

	user := input.user
	impersonated_user := input.action.resource.user.user

	core_rules.impersonation_access(user, impersonated_user, input.groups, compiled_rules)
}

allow if {
	# This handles for procedure
	input.operation in generic.fetch_operations(input.tool, "procedure_operations")

	# Check if the user has access to the catalog
	constants.access_level.read_only in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	# Check the privilege on the procedure
	function_name := input.action.resource.function.functionName
	input.privilege in core_rules.procedure_privileges(input.catalog, input.schema, function_name, input.user, input.groups, compiled_rules)
}

allow if {
	# This handles for query
	input.operation in generic.fetch_operations(input.tool, "query_execution")

	input.privilege in core_rules.query_access(input.user, input.groups, compiled_rules)
}

allow if {
	# This handles for query_owned by perms
	input.operation in generic.fetch_operations(input.tool, "meta_query_operations")

	principal_user := input.user
	resource_user := input.action.resource.user.user

	input.privilege in core_rules.query_owned_by_access(principal_user, resource_user, input.groups, compiled_rules)
}

allow if {
	# These handles the schema operations
	input.operation in generic.fetch_operations(input.tool, "schema_operations_ddl")

	# Check if the user has access to the catalog
	constants.access_level.all in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	# Check if owner of the schema
	core_rules.schema_owner(input.catalog, input.schema, input.user, input.groups, compiled_rules)
}

allow if {
	# These handles the schema operations
	input.operation in generic.fetch_operations(input.tool, "catalog_operations_ddl")

	# Check if the user has access to the catalog
	constants.access_level.all in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	# Check if owner of the schema
	core_rules.catalog_owner(input.catalog, input.user, input.groups, compiled_rules)
}

allow if {
	# Rename of schema
	input.operation in generic.fetch_operations(input.tool, "schema_operations_ddl_rename")

	# Check if the user has access to the both the catalogs
	source_catalog := input.catalog
	constants.access_level.all in core_rules.catalog_access(source_catalog, input.user, input.groups, compiled_rules)

	destination_catalog := input.action.targetResource.schema.catalogName
	constants.access_level.all in core_rules.catalog_access(destination_catalog, input.user, input.groups, compiled_rules)

	# Check if owner of the both the schema
	source_schema := input.schema
	core_rules.schema_owner(source_catalog, source_schema, input.user, input.groups, compiled_rules)

	destination_schema := input.action.targetResource.schema.schemaName
	core_rules.schema_owner(destination_catalog, destination_schema, input.user, input.groups, compiled_rules)
}

allow if {
	# controls the schema visibility
	input.operation in generic.fetch_operations(input.tool, "schema_visibility")

	# Check if the user has access to the catalog
	constants.access_level.read_only in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	# Check if the user has visibility access to the schema
	core_rules.schema_visibility(input.catalog, input.schema, input.user, input.groups, compiled_rules)
}

allow if {
	# Controls System Session Properties
	input.operation in generic.fetch_operations(input.tool, "session_properties")

	system_session_property_name := input.action.resource.systemSessionProperty.name
	core_rules.system_session_properties_access(
		system_session_property_name,
		input.user,
		input.groups,
		compiled_rules,
	)
}

allow if {
	# Controls Session Properties
	input.operation in generic.fetch_operations(input.tool, "session_properties")

	session_property_name := input.action.resource.property
	core_rules.system_session_properties_access(
		session_property_name,
		input.user,
		input.groups,
		compiled_rules,
	)
}

allow if {
	# Controls the DDL table operations
	input.operation in generic.fetch_operations(input.tool, "table_operations_ddl")

	# Check if the user has access to the catalog
	constants.access_level.all in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	# Check if the user has owner access to the table
	input.privilege in core_rules.table_privileges(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules)
}

allow if {
	# Filter tables
	input.operation in generic.fetch_operations(input.tool, "table_visibility")

	# Check if the user has access to the catalog
	constants.access_level.read_only in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	# Check if the user has access to the table
	# TODO: Any of the privileges should be enough (SELECT|INSERT|UPDATE|DELETE)
	input.privilege in core_rules.table_privileges(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules)
}

allow if {
	# Handles the rename ops
	input.operation in generic.fetch_operations(input.tool, "table_operations_ddl_rename")
	source_entities := {
		"catalog": input.catalog,
		"schema": input.schema,
		"table": input.table,
	}
	constants.access_level.all in core_rules.catalog_access(source_entities.catalog, input.user, input.groups, compiled_rules)

	destination_entities := {
		"catalog": input.action.targetResource.table.catalogName,
		"schema": input.action.targetResource.table.schemaName,
		"table": input.action.targetResource.table.tableName,
	}
	constants.access_level.all in core_rules.catalog_access(destination_entities.catalog, input.user, input.groups, compiled_rules)

	input.privilege in core_rules.table_privileges(
		source_entities.catalog,
		source_entities.schema,
		source_entities.table,
		input.user,
		input.groups,
		compiled_rules,
	)
	input.privilege in core_rules.table_privileges(
		destination_entities.catalog,
		destination_entities.schema,
		destination_entities.table,
		input.user,
		input.groups,
		compiled_rules,
	)
}

allow if {
	# Column read access
	input.operation in generic.fetch_operations(input.tool, "column_visibility")

	# Check if the user has access to the catalog
	constants.access_level.read_only in core_rules.catalog_access(input.catalog, input.user, input.groups, compiled_rules)

	# Check if the user has access to the table
	# TODO: Any of the privileges should be enough (SELECT|INSERT|UPDATE|DELETE)
	input.privilege in core_rules.table_privileges(input.catalog, input.schema, input.table, input.user, input.groups, compiled_rules)
}
