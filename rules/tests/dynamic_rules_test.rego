package rules.tests.dynamic_rules_test

import data.rules.dynamic_rules
import rego.v1

# Test basic dynamic rule parsing and matching
test_dynamic_table_rule_basic_success if {
	user := "test_user"
	groups := ["dynamic_rule:test_catalog|test_schema|test_table:table"]
	catalog := "test_catalog"
	schema := "test_schema"
	table := "test_table"

	result := dynamic_rules.dynamic_table_rule(user, groups, catalog, schema, table)
	count(result) == 1
	some privilege in result
	privilege.privileges == ["SELECT"]
}

# Test dynamic rule with no matching groups
test_dynamic_table_rule_no_matching_groups if {
	user := "test_user"
	groups := ["regular_group", "another_group"]
	catalog := "test_catalog"
	schema := "test_schema"
	table := "test_table"

	result := dynamic_rules.dynamic_table_rule(user, groups, catalog, schema, table)
	count(result) == 0
}



# Test blinkit special handling - exact catalog match
test_blinkit_special_handling_exact_catalog if {
	rule_catalog := "blinkit"
	rule_schema := "test_schema"
	rule_table := "test_table"
	actual_catalog := "blinkit"
	actual_schema := "test_schema"
	actual_table := "test_table"

	dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table)
}

# Test blinkit special handling - catalog pattern match
test_blinkit_special_handling_catalog_pattern if {
	rule_catalog := "blinkit"
	rule_schema := "test_schema"
	rule_table := "test_table"
	actual_catalog := "blinkit_iceberg"
	actual_schema := "test_schema"
	actual_table := "test_table"

	dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table)
}

# Test blinkit special handling - schema with lake_ prefix
test_blinkit_special_handling_schema_with_lake_prefix if {
	rule_catalog := "blinkit"
	rule_schema := "crm"
	rule_table := "users"
	actual_catalog := "blinkit_hudi"
	actual_schema := "lake_crm"
	actual_table := "users"

	dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table)
}


# Test blinkit special handling - table must match exactly
test_blinkit_special_handling_table_exact_match if {
	rule_catalog := "blinkit"
	rule_schema := "test_schema"
	rule_table := "test_table"
	actual_catalog := "blinkit_sales"
	actual_schema := "test_schema"
	actual_table := "different_table"

	not dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table)
}

# Test blinkit special handling - catalog must match pattern
test_blinkit_special_handling_catalog_no_match if {
	rule_catalog := "blinkit"
	rule_schema := "test_schema"
	rule_table := "test_table"
	actual_catalog := "zomato"
	actual_schema := "test_schema"
	actual_table := "test_table"

	not dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table)
}

# Test blinkit special handling - schema must match pattern
test_blinkit_special_handling_schema_no_match if {
	rule_catalog := "blinkit"
	rule_schema := "crm"
	rule_table := "users"
	actual_catalog := "blinkit_sales"
	actual_schema := "different_schema"
	actual_table := "users"

	not dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table)
}

# Test non-blinkit exact matching
test_non_blinkit_exact_matching_success if {
	rule_catalog := "zomato"
	rule_schema := "public"
	rule_table := "orders"
	actual_catalog := "zomato"
	actual_schema := "public"
	actual_table := "orders"

	dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table)
}

# Test non-blinkit exact matching - catalog mismatch
test_non_blinkit_exact_matching_catalog_mismatch if {
	rule_catalog := "zomato"
	rule_schema := "public"
	rule_table := "orders"
	actual_catalog := "different_catalog"
	actual_schema := "public"
	actual_table := "orders"

	not dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table)
}

# Test non-blinkit exact matching - schema mismatch
test_non_blinkit_exact_matching_schema_mismatch if {
	rule_catalog := "zomato"
	rule_schema := "public"
	rule_table := "orders"
	actual_catalog := "zomato"
	actual_schema := "different_schema"
	actual_table := "orders"

	not dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table)
}

# Test non-blinkit exact matching - table mismatch
test_non_blinkit_exact_matching_table_mismatch if {
	rule_catalog := "zomato"
	rule_schema := "public"
	rule_table := "orders"
	actual_catalog := "zomato"
	actual_schema := "public"
	actual_table := "different_table"

	not dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, actual_catalog, actual_schema, actual_table)
}

# Test integration with multiple dynamic rule groups
test_dynamic_table_rule_multiple_groups if {
	user := "test_user"
	groups := [
		"regular_group",
		"dynamic_rule:test_catalog|test_schema|test_table:table",
		"dynamic_rule:other_catalog|other_schema|other_table:table",
		"another_regular_group"
	]
	catalog := "test_catalog"
	schema := "test_schema"
	table := "test_table"

	result := dynamic_rules.dynamic_table_rule(user, groups, catalog, schema, table)
	count(result) == 1
	some privilege in result
	privilege.privileges == ["SELECT"]
}

# Test dynamic rule with blinkit catalog variations
test_dynamic_table_rule_blinkit_variations if {
	user := "test_user"
	groups := ["dynamic_rule:blinkit|crm|users:table"]

	# Test with blinkit catalog
	result1 := dynamic_rules.dynamic_table_rule(user, groups, "blinkit", "crm", "users")
	count(result1) == 1

	# Test with blinkit_sales catalog
	result2 := dynamic_rules.dynamic_table_rule(user, groups, "blinkit_sales", "crm", "users")
	count(result2) == 1

	# Test with blinkit_analytics catalog
	result3 := dynamic_rules.dynamic_table_rule(user, groups, "blinkit_analytics", "crm", "users")
	count(result3) == 1

	# Test with lake_crm schema
	result4 := dynamic_rules.dynamic_table_rule(user, groups, "blinkit", "lake_crm", "users")
	count(result4) == 1
}

# Test dynamic rule with blinkit - no match scenarios
test_dynamic_table_rule_blinkit_no_match if {
	user := "test_user"
	groups := ["dynamic_rule:blinkit|crm|users:table"]

	# Test with non-blinkit catalog
	result1 := dynamic_rules.dynamic_table_rule(user, groups, "zomato", "crm", "users")
	count(result1) == 0

	# Test with wrong table
	result2 := dynamic_rules.dynamic_table_rule(user, groups, "blinkit", "crm", "orders")
	count(result2) == 0

	# Test with schema that doesn't match pattern
	result3 := dynamic_rules.dynamic_table_rule(user, groups, "blinkit", "different_schema", "users")
	count(result3) == 0
}




# Test blinkit special case with complex schema patterns
test_blinkit_complex_schema_patterns if {
	# Test various schema patterns that should match
	rule_catalog := "blinkit"
	rule_table := "test_table"
	actual_catalog := "blinkit_sales"
	actual_table := "test_table"

	# Schema without lake_ prefix
	dynamic_rules.match_rule_components(rule_catalog, "analytics", rule_table, actual_catalog, "analytics", actual_table)

	# Schema with lake_ prefix
	dynamic_rules.match_rule_components(rule_catalog, "analytics", rule_table, actual_catalog, "lake_analytics", actual_table)

	# Schema with underscores
	dynamic_rules.match_rule_components(rule_catalog, "user_data", rule_table, actual_catalog, "user_data", actual_table)
	dynamic_rules.match_rule_components(rule_catalog, "user_data", rule_table, actual_catalog, "lake_user_data", actual_table)
}

# Test blinkit catalog pattern edge cases
test_blinkit_catalog_pattern_edge_cases if {
	rule_catalog := "blinkit"
	rule_schema := "test_schema"
	rule_table := "test_table"
	actual_schema := "test_schema"
	actual_table := "test_table"

	# Various blinkit catalog variations should match
	dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, "blinkit", actual_schema, actual_table)
	dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, "blinkit_", actual_schema, actual_table)
	dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, "blinkit_sales", actual_schema, actual_table)
	dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, "blinkit_analytics_v2", actual_schema, actual_table)

	# Non-blinkit catalogs should not match
	not dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, "zomato", actual_schema, actual_table)
	not dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, "hyperpure", actual_schema, actual_table)
	not dynamic_rules.match_rule_components(rule_catalog, rule_schema, rule_table, "blink", actual_schema, actual_table)
}

# Test schema pattern edge cases for blinkit
test_blinkit_schema_pattern_edge_cases if {
	rule_catalog := "blinkit"
	rule_table := "test_table"
	actual_catalog := "blinkit_sales"
	actual_table := "test_table"

	# Test various schema patterns
	dynamic_rules.match_rule_components(rule_catalog, "crm", rule_table, actual_catalog, "crm", actual_table)
	dynamic_rules.match_rule_components(rule_catalog, "crm", rule_table, actual_catalog, "lake_crm", actual_table)

	# Schema with numbers
	dynamic_rules.match_rule_components(rule_catalog, "data_v2", rule_table, actual_catalog, "data_v2", actual_table)
	dynamic_rules.match_rule_components(rule_catalog, "data_v2", rule_table, actual_catalog, "lake_data_v2", actual_table)

	# Schema that should not match
	not dynamic_rules.match_rule_components(rule_catalog, "crm", rule_table, actual_catalog, "different_crm", actual_table)
	not dynamic_rules.match_rule_components(rule_catalog, "crm", rule_table, actual_catalog, "crm_lake", actual_table)
	not dynamic_rules.match_rule_components(rule_catalog, "crm", rule_table, actual_catalog, "lake_different", actual_table)
}



# Test that privileges are always SELECT for dynamic rules
test_dynamic_table_rule_privileges_always_select if {
	user := "test_user"
	groups := ["dynamic_rule:test_catalog|test_schema|test_table:table"]
	catalog := "test_catalog"
	schema := "test_schema"
	table := "test_table"

	result := dynamic_rules.dynamic_table_rule(user, groups, catalog, schema, table)
	count(result) == 1
	some privilege in result
	privilege.privileges == ["SELECT"]
	count(privilege.privileges) == 1
}
