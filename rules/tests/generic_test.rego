package rules.tests.generic_test

import data.rules.main
import rego.v1

# Test: Allow SELECT for jumbo-admin on any table
allow_select_jumbo_admin if {
	# Rename the local variable holding test data
	test_input := {
		"user": "jumbo-admin",
		"catalog": "any_catalog",
		"schema": "any_schema",
		"table": "any_table",
		"operation": "SELECT",
	}

	# Use the new variable name in the 'with' clause
	data.rules.main.allow with input as test_input
}

# Test: Deny INSERT for bi_tableau if not in privileges
deny_insert_bi_tableau if {
	# Rename the local variable
	test_input := {
		"user": "bi_tableau",
		"catalog": "any_catalog",
		"schema": "any_schema",
		"table": "any_table",
		"operation": "INSERT",
	}

	# Use the new variable name
	not data.rules.main.allow with input as test_input
}

# Test: Row filter is returned if defined
row_filter_expression if {
	# Rename the local variable
	test_input := {
		"user": "bi_tableau",
		"catalog": "sales",
		"schema": "public",
		"table": "orders",
		"operation": "SELECT",
	}

	# Use the new variable name
	rf := data.rules.main.row_filter with input as test_input
	rf.expression != ""
}

# Test: Column mask is returned if defined
column_mask_expression if {
	# Rename the local variable
	test_input := {
		"user": "common_airflow",
		"catalog": "blinkit_sales",
		"schema": "lake_crm",
		"table": "crm_user",
		"column": "phone",
		"operation": "SELECT",
	}

	# Use the new variable name
	mask := data.rules.main.mask with input as test_input
	mask.mask != ""
}
