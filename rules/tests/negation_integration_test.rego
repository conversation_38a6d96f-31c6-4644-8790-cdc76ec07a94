package rules.tests.negation_integration

import data.rules.core as core_rules
import data.utils.custom_regex as util
import rego.v1

# Test basic match_user_group with negation
test_match_user_group_with_user_negation if {
	rule := {
		"user": "admin",
		"user_negated": true,
		"group": ".*",
	}
	user := "regular_user"
	groups := ["users"]

	# Should match because user is NOT admin (negated)
	core_rules.match_user_group(rule, user, groups)
}

test_match_user_group_with_user_negation_excluded if {
	rule := {
		"user": "admin",
		"user_negated": true,
		"group": ".*",
	}
	user := "admin"
	groups := ["users"]

	# Should NOT match because user IS admin (and admin is negated/excluded)
	not core_rules.match_user_group(rule, user, groups)
}

test_match_user_group_with_group_negation if {
	rule := {
		"user": ".*",
		"group": "banned",
		"group_negated": true,
	}
	user := "any_user"
	groups := ["regular_users", "analysts"]

	# Should match because groups do NOT contain "banned" (negated)
	core_rules.match_user_group(rule, user, groups)
}

test_match_user_group_with_group_negation_excluded if {
	rule := {
		"user": ".*",
		"group": "banned",
		"group_negated": true,
	}
	user := "any_user"
	groups := ["banned", "other_groups"]

	# Should NOT match because groups contain "banned" (and banned is negated/excluded)
	not core_rules.match_user_group(rule, user, groups)
}

# Test both user and group negation together
test_match_user_group_both_negated if {
	rule := {
		"user": "(admin|superuser)",
		"user_negated": true,
		"group": "(banned|restricted)",
		"group_negated": true,
	}
	user := "regular_user"
	groups := ["normal_users", "analysts"]

	# Should match because user is NOT in (admin|superuser) AND groups do NOT contain (banned|restricted)
	core_rules.match_user_group(rule, user, groups)
}

test_match_user_group_both_negated_user_excluded if {
	rule := {
		"user": "(admin|superuser)",
		"user_negated": true,
		"group": "(banned|restricted)",
		"group_negated": true,
	}
	user := "admin" # This user should be excluded
	groups := ["normal_users", "analysts"]

	# Should NOT match because user IS in excluded list (admin|superuser)
	not core_rules.match_user_group(rule, user, groups)
}

test_match_user_group_both_negated_group_excluded if {
	rule := {
		"user": "(admin|superuser)",
		"user_negated": true,
		"group": "(banned|restricted)",
		"group_negated": true,
	}
	user := "regular_user"
	groups := ["banned", "normal_users"] # Contains excluded group

	# Should NOT match because groups contain excluded group (banned)
	not core_rules.match_user_group(rule, user, groups)
}

# Test realistic scenario from your migration: rls_zcd_associate exclusion
test_rls_zcd_associate_exclusion_scenario if {
	# This represents a deny rule that should NOT apply to rls_zcd_associate group
	deny_rule := {
		"type": "table",
		"entity_category": "data_entity",
		"privileges": [],
		"user": "(.*.php.*|<EMAIL>|ml_jobs|rating-jobs|tableau-etl)",
		"user_negated": true,
		"group": "((rls_zcd_associate|rls_zcd_associate_tl)|rls_zcd_associate)",
		"group_negated": true,
	}

	# Test case 1: rls_zcd_associate should be excluded from deny rule
	user1 := "data_analyst"
	groups1 := ["rls_zcd_associate"]
	not core_rules.match_user_group(deny_rule, user1, groups1)

	# Test case 2: regular users not in excluded groups should match deny rule
	user2 := "business_analyst"
	groups2 := ["business_users", "external_users"]
	core_rules.match_user_group(deny_rule, user2, groups2)

	# Test case 3: users matching excluded user pattern should not match
	user3 := "test.php.script"
	groups3 := ["business_users"]
	not core_rules.match_user_group(deny_rule, user3, groups3)
}

# Test with complex regex patterns from your data_migrated.yaml
test_complex_regex_negation_patterns if {
	rule := {
		"user": "((.*.php.*|<EMAIL>|ml_jobs|rating-jobs|tableau-etl|zml-segments|common-python-etls|zexperiments|ml|dataplatform-python-client|jumbo_rscripts|zml-jobs|zdp-common-python-etls|zexperiments-etl)|(blinkit_adhoc_testing)|(blinkit_backend_application))",
		"user_negated": true,
		"group": "((admin|data_platform)|(blinkit.*)|(blinkit_default)|(district.*|dining_out.*)|pseudo_admin)",
		"group_negated": true,
	}

	# Users matching the complex pattern should be excluded
	excluded_user := "<EMAIL>"
	regular_groups := ["regular_users"]
	not core_rules.match_user_group(rule, excluded_user, regular_groups)

	# Groups matching the complex pattern should be excluded
	regular_user := "analyst"
	excluded_groups := ["admin", "other_groups"]
	not core_rules.match_user_group(rule, regular_user, excluded_groups)

	# Users and groups not matching patterns should be included
	regular_user2 := "business_analyst"
	regular_groups2 := ["viewers", "external_users"]
	core_rules.match_user_group(rule, regular_user2, regular_groups2)
}

# Test catalog access with negation (integration with core.rego functions)
test_catalog_access_with_negation if {
	# Mock rules with negated patterns
	rules := [{
		"type": "catalog",
		"entity_category": "data_entity",
		"catalog": "sensitive_catalog",
		"user": "(restricted_user|banned_user)",
		"user_negated": true,
		"group": "restricted_group",
		"group_negated": true,
		"allow": "read-only",
	}]

	catalog_name := "sensitive_catalog"

	# Regular user with regular groups should get access
	user1 := "data_analyst"
	groups1 := ["analysts", "viewers"]
	access1 := core_rules.catalog_access(catalog_name, user1, groups1, rules)
	"read-only" in access1

	# Restricted user should NOT get access (excluded by negation)
	user2 := "restricted_user"
	groups2 := ["analysts"]
	core_rules.catalog_access(catalog_name, user2, groups2, rules) != true

	# User with restricted group should NOT get access (excluded by negation)
	user3 := "regular_user"
	groups3 := ["restricted_group", "analysts"]
	core_rules.catalog_access(catalog_name, user3, groups3, rules) != true
}

# Test table privileges with negation
test_table_privileges_with_negation if {
	rules := [{
		"type": "table",
		"entity_category": "data_entity",
		"catalog": "test_catalog",
		"schema": "test_schema",
		"table": "sensitive_table",
		"user": "(unauthorized_user|blocked_user)",
		"user_negated": true,
		"group": "blocked_group",
		"group_negated": true,
		"privileges": ["SELECT"],
	}]

	# Regular user should get privileges
	user1 := "analyst"
	groups1 := ["analysts"]
	privileges1 := core_rules.table_privileges("test_catalog", "test_schema", "sensitive_table", user1, groups1, rules)
	"SELECT" in privileges1

	# Unauthorized user should NOT get privileges (excluded by negation)
	user2 := "unauthorized_user"
	groups2 := ["analysts"]
	privileges2 := core_rules.table_privileges("test_catalog", "test_schema", "sensitive_table", user2, groups2, rules)
	count(privileges2) == 0
}

# Test edge case: empty patterns with negation
test_empty_patterns_with_negation if {
	rule := {
		"user": "",
		"user_negated": true,
		"group": "",
		"group_negated": true,
	}

	# With empty patterns being negated, most users/groups should match
	user := "any_user"
	groups := ["any_group"]
	core_rules.match_user_group(rule, user, groups)
}

# Test default values when negation flags are missing
test_missing_negation_flags if {
	rule := {
		"user": "admin",
		"group": "admin_group",
		# No user_negated or group_negated flags
	}

	# Should behave as normal (non-negated) matching
	user := "admin"
	groups := ["admin_group"]
	core_rules.match_user_group(rule, user, groups)

	# Should not match when user/group don't match
	user2 := "regular_user"
	groups2 := ["regular_group"]
	not core_rules.match_user_group(rule, user2, groups2)
}

# Test performance with complex negated patterns
test_performance_complex_negated_patterns if {
	rule := {
		"user": "(user1|user2|user3|user4|user5|user6|user7|user8|user9|user10|user11|user12|user13|user14|user15)",
		"user_negated": true,
		"group": "(group1|group2|group3|group4|group5|group6|group7|group8|group9|group10)",
		"group_negated": true,
	}

	# Should handle complex patterns efficiently
	user := "regular_user"
	groups := ["regular_group1", "regular_group2"]
	core_rules.match_user_group(rule, user, groups)
}
