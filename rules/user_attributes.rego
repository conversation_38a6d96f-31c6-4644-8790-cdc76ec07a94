package rules.user_attributes

import rego.v1

import data.utils.custom_regex
import data.utils.generic

# Get custom configuration paths for a tenant
get_custom_config_paths(tenant_name) := custom_paths if {
	config := object.get(data.configurations, [tenant_name, "user_attribute_configuration"], {})
	custom_paths := object.get(config, "user_attribute_paths", {})
}

add_tenant_to_rules(fallback_tenant, rules_array) := [modified_rule |
	some rule in rules_array
	has_tenant := "tenant" in object.keys(rule)
	modified_rule := process_rule(has_tenant, fallback_tenant, rule)
]

# Helper function to process a single rule using if-else
process_rule(true, fallback_tenant, rule) := rule

process_rule(false, fallback_tenant, rule) := object.union(rule, {"tenant": fallback_tenant})

# Build hierarchical paths for user attributes
build_user_attribute_paths(tenant_name, user_attrs) := paths if {
	custom_paths := get_custom_config_paths(tenant_name)

	# Default hierarchical paths
	default_paths := [
		# Level 1: Line of Business
		[
			tenant_name, "user_attributes", "line_of_business",
			generic.sanitize_string(object.get(user_attrs, "line_of_business", "default")),
			"rules",
		],
		# Level 2: Line of Business + Department
		[
			tenant_name, "user_attributes", "line_of_business",
			generic.sanitize_string(object.get(user_attrs, "line_of_business", "default")),
			"department",
			generic.sanitize_string(object.get(user_attrs, "department", "default")), "rules",
		],
		# Level 3: Line of Business + Department + Sub Department
		[
			tenant_name, "user_attributes", "line_of_business",
			generic.sanitize_string(object.get(user_attrs, "line_of_business", "default")),
			"department",
			generic.sanitize_string(object.get(user_attrs, "department", "default")),
			"sub_department",
			generic.sanitize_string(object.get(user_attrs, "sub_department", "default")), "rules",
		],
		# Level 4: Line of Business + Department + Sub Department + Designation
		[
			tenant_name, "user_attributes", "line_of_business",
			generic.sanitize_string(object.get(user_attrs, "line_of_business", "default")),
			"department",
			generic.sanitize_string(object.get(user_attrs, "department", "default")),
			"sub_department",
			generic.sanitize_string(object.get(user_attrs, "sub_department", "default")),
			"designation",
			generic.sanitize_string(object.get(user_attrs, "designation", "default")), "rules",
		],
	]

	# Override with custom paths if provided
	paths := array.concat(default_paths, build_custom_paths(tenant_name, user_attrs, custom_paths))
}

# Build custom paths from configuration
build_custom_paths(tenant_name, user_attrs, custom_config) := [path |
	some config_key, config_value in custom_config
	config_key == substitute_single_segment(config_key, user_attrs)
	path := build_dynamic_path(tenant_name, user_attrs, config_value)
]

# Build dynamic path with variable substitution
build_dynamic_path(tenant_name, user_attrs, path_template) := dynamic_path if {
	# Replace variables in path template
	substituted_path := substitute_path_variables(path_template, user_attrs)
	dynamic_path := array.concat(array.concat([tenant_name], substituted_path), ["rules"])
}

# Substitute variables in path template - fixed to combine both types
substitute_path_variables(path_template, user_attrs) := [segment |
	some path in path_template
	segment := substitute_single_segment(path, user_attrs)
]

# Helper function to substitute a single segment
substitute_single_segment(path_segment, user_attrs) := segment if {
	# If it's a template variable, substitute it
	startswith(path_segment, "user_attribute_template:")
	var_name := trim_prefix(path_segment, "user_attribute_template:")
	segment := generic.sanitize_string(object.get(user_attrs, var_name, "default"))
} else := segment if {
	# If it's not a template variable, return as-is
	not startswith(path_segment, "user_attribute_template:")
	segment := path_segment
}

# Enhanced user attribute rule loading with regex support
user_attribute_rule_arrays(tenant, user_attributes) := rules_arrays if {
	paths := build_user_attribute_paths(tenant, user_attributes)

	rules_arrays := [modified_rules_array |
		some path in paths
		original_rules_array := object.get(data.configurations, path, [])
		count(original_rules_array) > 0

		path_tenant := path[0]

		modified_rules_array := add_tenant_to_rules(path_tenant, original_rules_array)
	]
}

# Get regex-based user attribute rules
regex_user_attribute_rules(tenant, user_attributes) := all_matched_rules if {
	# Get regex configurations
	regex_config := object.get(data.configurations, [tenant, "user_attribute_configuration", "regex_user_attributes"], {})

	# Collect all matching rules into a single flattened array
	all_matched_rules := [rule |
		some attr_name, attr_patterns in regex_config
		user_value := object.get(user_attributes, attr_name, "")
		user_value != ""
		some pattern, rule_path in attr_patterns
		custom_regex.match_entire(pattern, user_value)
		rules := object.get(data.configurations, array.concat(array.concat([tenant], rule_path), ["rules"]), [])
		modified_rules_array := add_tenant_to_rules(tenant, rules)
		some rule in modified_rules_array
	]
} else := []
