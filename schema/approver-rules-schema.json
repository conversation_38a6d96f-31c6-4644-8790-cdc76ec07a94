{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://zomato.com/schemas/opa-approver-rules.json", "title": "OPA Approver Rules Configuration Schema", "description": "JSON Schema for validating YAML configuration files containing OPA Approver rules", "type": "array", "items": {"$ref": "#/definitions/ApproverRule"}, "definitions": {"ApproverRule": {"type": "object", "title": "Approvers Rule", "description": "Defines rules for alloting approvers to tables/schemas/catalogs.", "required": ["catalog", "max_lease", "primary_owner", "owners"], "properties": {"catalog": {"type": "string", "description": "Regex pattern for catalog name matching"}, "max_lease": {"type": "string", "enum": ["1d", "3d", "1w", "3w", "1m", "3m"], "description": "Max lease duration allowed for the entity"}, "primary_owner": {"type": "string", "description": "Email of the primary owner of the entity", "format": "email"}, "owners": {"type": "array", "description": "Emails of the owners of the entity", "items": {"type": "string", "format": "email"}}, "schema": {"type": "string", "description": "Regex pattern for schema name matching"}, "table": {"type": "string", "description": "Regex pattern for table name matching"}, "priority": {"type": "integer", "description": "Rule Priority"}}, "additionalProperties": false}}}