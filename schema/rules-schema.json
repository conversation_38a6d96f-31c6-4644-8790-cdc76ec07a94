{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://zomato.com/schemas/opa-rules.json", "title": "OPA Rules Configuration Schema", "description": "JSON Schema for validating YAML configuration files containing OPA authorization rules", "type": "array", "items": {"$ref": "#/definitions/Rule"}, "definitions": {"Rule": {"description": "A rule can be one of several types, discriminated by the 'type' property.", "oneOf": [{"$ref": "#/definitions/ImpersonationRule"}, {"$ref": "#/definitions/GenericRule"}]}, "ImpersonationRule": {"type": "object", "title": "Impersonation Rule", "description": "Defines rules for user impersonation.", "required": ["type", "entity_category", "new_user", "original_user"], "properties": {"type": {"const": "impersonation", "description": "The type of rule defining what resource it applies to"}, "entity_category": {"const": "query_engine_entity", "description": "Category for impersonation is always 'query_engine_entity'"}, "new_user": {"type": "string", "description": "Regex pattern for the user being impersonated."}, "original_user": {"type": "string", "description": "Regex pattern for the user performing the impersonation."}, "user": {"type": "string", "description": "Regex pattern for user who is granted permission to impersonate."}, "group": {"type": "string", "description": "Regex pattern for group that is granted permission to impersonate."}, "user_negated": {"type": "boolean", "description": "Whether to negate the user pattern (exclude matching users)", "default": false}, "group_negated": {"type": "boolean", "description": "Whether to negate the group pattern (exclude matching groups)", "default": false}, "tenant": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}], "description": "Tenant(s) this rule applies to"}, "allow": {"type": "boolean", "description": "Allow or deny the impersonation."}}, "additionalProperties": false}, "GenericRule": {"type": "object", "title": "Generic Access Rule", "description": "Defines rules for accessing various data and system entities.", "required": ["type", "entity_category"], "properties": {"type": {"type": "string", "enum": ["catalog", "schema", "table", "function", "procedure", "session_property_operation", "system_information", "query"], "description": "The type of rule defining what resource it applies to"}, "entity_category": {"type": "string", "enum": ["data_entity", "query_engine_entity"], "description": "Category of the entity - data_entity for tables/schemas/catalogs, query_engine_entity for functions/procedures/queries"}, "catalog": {"type": "string", "description": "Regex pattern for catalog name matching"}, "schema": {"type": "string", "description": "Regex pattern for schema name matching"}, "table": {"type": "string", "description": "Regex pattern for table name matching"}, "function": {"type": "string", "description": "Regex pattern for function name matching"}, "procedure": {"type": "string", "description": "Regex pattern for procedure name matching"}, "user": {"type": "string", "description": "Regex pattern for user matching"}, "group": {"type": "string", "description": "Regex pattern for group matching"}, "user_negated": {"type": "boolean", "description": "Whether to negate the user pattern (exclude matching users)", "default": false}, "group_negated": {"type": "boolean", "description": "Whether to negate the group pattern (exclude matching groups)", "default": false}, "privileges": {"type": "array", "items": {"type": "string", "enum": ["SELECT", "INSERT", "UPDATE", "DELETE", "CREATE", "DROP", "ALTER", "GRANT", "REVOKE", "SHOW", "EXECUTE", "OWNERSHIP", "GRANT_SELECT", "CREATE_TABLE", "CREATE_VIEW", "CREATE_SCHEMA", "DROP_TABLE", "DROP_VIEW", "DROP_SCHEMA", "ALTER_TABLE", "ALTER_VIEW", "ALTER_SCHEMA", "SHOW_TABLES", "SHOW_SCHEMAS", "SHOW_COLUMNS", "SHOW_CREATE_TABLE", "SHOW_CREATE_VIEW", "SHOW_CREATE_SCHEMA", "GRANT_EXECUTE"]}, "description": "List of privileges granted by this rule", "uniqueItems": true}, "tenant": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}], "description": "Tenant(s) this rule applies to"}, "allow": {"oneOf": [{"type": "boolean"}, {"type": "string", "enum": ["all", "read-only"]}, {"type": "array", "items": {"type": "string", "enum": ["READ", "WRITE"]}, "minItems": 1, "uniqueItems": true}, {"type": "array", "items": {"type": "string", "enum": ["EXECUTE", "KILL", "VIEW"]}, "minItems": 1, "uniqueItems": true}], "description": "General allow flag or access level for the rule"}, "owner": {"type": "boolean", "description": "Whether the user/group is an owner of the resource"}, "filter": {"type": "string", "description": "Row-level filter expression (SQL WHERE clause)"}, "filter_substitution": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Variable substitutions for the filter template", "default": {}}, "filter_template": {"type": "boolean", "description": "Whether the filter uses template substitution", "default": false}, "priority": {"type": "integer", "description": "Rule Priority"}, "filter_environment": {"type": "string", "description": "Environment-specific filter identifier"}, "columns": {"type": "array", "items": {"$ref": "#/definitions/ColumnSpec"}, "description": "Column-specific specifications including masks and permissions"}, "queryOwner": {"type": "string", "description": "Query ownership pattern"}, "property": {"type": "string", "description": "session property pattern"}}, "additionalProperties": false}, "ColumnSpec": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Column name or regex pattern"}, "allow": {"type": "boolean", "description": "Whether access to this column is allowed", "default": true}, "mask": {"type": "string", "description": "Masking expression for this column"}, "mask_environment": {"type": "string", "description": "Environment-specific mask identifier"}}, "additionalProperties": false}}}