package tools.generic.main

import data.configurations.global.sources

# Policies
import data.policies.global.main as global_main

# Default Import
import rego.v1

# This is the default rule for all requests
default allow := false

runtime := opa.runtime()

tenant := object.get(runtime.env, "TENANT", "zomato")

request_context := {
	"tenant_map": {"runtime_tenant": tenant},
	"user": input.user,
	"groups": [],
	"operation": input.operation,
	"tool": "generic",
	"catalog": replace(input.catalog, "zomato", "hive"),
	"schema": input.schema,
	"table": input.table,
	"action": {},
	"columns": [],
}

# METADATA
# description: Common entrypoint for all requests
# entrypoint: true
allow if {
	global_main.allow with input as request_context
}
