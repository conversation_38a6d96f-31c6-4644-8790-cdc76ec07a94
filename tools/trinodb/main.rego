package tools.trinodb.main

import data.configurations.global.sources
import data.configurations.global.system_users

# Policies
import data.policies.global.main as global_main

# Utilities
import data.tools.trinodb.utils.extraction
import data.utils.generic

# Default Import
import rego.v1

# This is the default rule for all requests
default allow := false

default catalog := null

# Populate the environment variables
runtime := opa.runtime()

tenant := object.get(runtime.env, "TENANT", "zomato")

source := object.get(runtime.env, "SOURCE", "common_cluster")

# Check if request is coming from a valid source
valid_source if {
	is_source_enabled := object.get(sources, [tenant, source, "enabled"], false)
	is_source_enabled == true
}

valid_source if {
	# Check if the source is bypassed
	is_source_bypass := object.get(sources, ["global", "bypass", "enabled"], false)
	is_source_bypass == true
}

valid_source if {
	# Check if this is a test request
	input.is_test == true
}

# Tool specific objects
user := object.get(input, ["context", "identity", "user"], null)

groups := object.get(input, ["context", "identity", "groups"], [])

operation := object.get(input, ["action", "operation"], null)

action := object.get(input, "action", {})

resource_obj := object.get(input, ["action", "resource"], null)

# Extract resource details
catalog := generic.handle_catalog_migration(extraction._get_catalog(resource_obj), tenant)

schema := extraction._get_schema(resource_obj)

table := extraction._get_table(resource_obj)

columns := extraction._get_columns(resource_obj)

# Extract common attributes once for batch processing
common_attrs := {
	"tenant_map": {"runtime_tenant": tenant},
	"user": user,
	"groups": groups,
	"operation": operation,
	"tool": "trinodb",
	"source": source,
}

request_context := object.union(
	common_attrs,
	{
		"catalog": catalog,
		"schema": schema,
		"table": table,
		"columns": columns,
		"action": action,
	},
)

is_system_user if {
	user in system_users
}

# skip computation for system users for filter tables because it can cause timeouts for schemas with higher number of tables
skip_computation_filter_tables if {
	is_system_user
	input.action.operation == "FilterTables"
}

# METADATA
# description: Common entrypoint for all requests
# entrypoint: true
allow if {
	valid_source
	global_main.allow with input as request_context
}

# METADATA
# description: Common entrypoint for batch requests
# entrypoint: true
batch contains index if {
	input.action.operation != "FilterColumns"
	input.action.operation != "FilterTables"

	some index, resource in input.action.filterResources

	# regal ignore:with-outside-test-context
	allow with input.action.resource as resource
}

batch contains index if {
	input.action.operation == "FilterColumns"

	table := input.action.filterResources[0].table
	some index, column_name in table.columns

	# regal ignore:with-outside-test-context
	allow with input.action.resource as {"table": {
		"catalogName": generic.handle_catalog_migration(table.catalogName, tenant),
		"schemaName": table.schemaName,
		"tableName": table.tableName,
		"columnName": column_name,
	}}
}

batch contains index if {
	input.action.operation == "FilterTables"
	not skip_computation_filter_tables
	some index, resource in input.action.filterResources
	allow with input.action.resource as resource
}

batch contains index if {
	input.action.operation == "FilterTables"
	skip_computation_filter_tables
	some index, resource in input.action.filterResources
}

# METADATA
# description: Common entrypoint for row filter requests
# entrypoint: true
row_filters contains rf if {
	valid_source
	rf := global_main.row_filter with input as request_context
	is_object(rf)
}

# METADATA
# description: Common entrypoint for column masking requests
# entrypoint: true
column_masking := mask if {
	valid_source
	mask := global_main.column_mask with input as request_context
}

# METADATA
# description: Common entrypoint for batch column masking requests
# entrypoint: true
batch_column_masking contains {
	"index": index,
	"viewExpression": batch_column_masks,
} if {
	some index, resource in input.action.filterResources

	# regal ignore:with-outside-test-context
	batch_column_masks := column_masking with input.action.resource as resource
	is_object(batch_column_masks)
}
