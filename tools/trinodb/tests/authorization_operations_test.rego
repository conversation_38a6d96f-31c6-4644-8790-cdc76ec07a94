package tools.trinodb.tests.authorization_operations_test

import data.tools.trinodb.main
import rego.v1

# Table Authorization Tests
test_table_authorization_success if {
	user := "table-owner"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetTableAuthorization",
			"grantee": {
				"name": "data-scientist",
				"type": "USER",
			},
			"resource": {"table": {
				"catalogName": "hive",
				"schemaName": "analytics",
				"tableName": "metrics",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"allow": "all",
				"catalog": "hive",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(table-owner)",
			},
			{
				"catalog": "hive",
				"entity_category": "data_entity",
				"group": "(data_admins)",
				"privileges": ["OWNERSHIP"],
				"schema": "analytics",
				"table": "metrics",
				"type": "table",
				"user": "(table-owner)",
			},
			{
				"allow": true,
				"entity_category": "data_entity",
				"new_user": "data-scientist",
				"original_user": "(table-owner)",
				"type": "authorization",
			},
		]
}

# Table Authorization Tests - denial
test_table_authorization_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetTableAuthorization",
			"grantee": {
				"name": "data-scientist",
				"type": "USER",
			},
			"resource": {"table": {
				"catalogName": "hive",
				"schemaName": "analytics",
				"tableName": "metrics",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"allow": "read-only",
				"catalog": "hive",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(analyst)",
			},
			{
				"catalog": "hive",
				"entity_category": "data_entity",
				"group": "(analysts)",
				"privileges": ["SELECT"],
				"schema": "analytics",
				"table": "metrics",
				"type": "table",
				"user": "(analyst)",
			},
		]

	result == false
}

# View Authorization Tests - success
test_view_authorization_success if {
	user := "view-owner"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetViewAuthorization",
			"grantee": {
				"name": "data-scientist",
				"type": "USER",
			},
			"resource": {"table": {
				"catalogName": "hive",
				"schemaName": "analytics",
				"tableName": "metrics_view",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"allow": "all",
				"catalog": "hive",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(view-owner)",
			},
			{
				"catalog": "hive",
				"entity_category": "data_entity",
				"group": "(data_admins)",
				"privileges": ["OWNERSHIP"],
				"schema": "analytics",
				"table": "metrics_view",
				"type": "table",
				"user": "(view-owner)",
			},
			{
				"allow": true,
				"entity_category": "data_entity",
				"new_user": "data-scientist",
				"original_user": "(view-owner)",
				"type": "authorization",
			},
		]
}

# View Authorization Tests - denial
test_view_authorization_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetViewAuthorization",
			"grantee": {
				"name": "data-scientist",
				"type": "USER",
			},
			"resource": {"table": {
				"catalogName": "hive",
				"schemaName": "analytics",
				"tableName": "metrics_view",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"allow": "read-only",
				"catalog": "hive",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(analyst)",
			},
			{
				"catalog": "hive",
				"entity_category": "data_entity",
				"group": "(analysts)",
				"privileges": ["SELECT"],
				"schema": "analytics",
				"table": "metrics_view",
				"type": "table",
				"user": "(analyst)",
			},
		]

	result == false
}
