package tools.trinodb.tests.catalog_operations

import data.tools.trinodb.main
import rego.v1

# Test AccessCatalog operation - success with read-only access
test_access_catalog_read_only_success if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "AccessCatalog",
			"resource": {"catalog": {"name": "sales"}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "analyst",
			"catalog": "sales",
			"allow": "read-only",
		}]

	result == true
}

# Test AccessCatalog operation - success with full access
test_access_catalog_full_access_success if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "AccessCatalog",
			"resource": {"catalog": {"name": "data_lake"}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "data_engineer",
			"catalog": "data_lake",
			"allow": "all",
		}]

	result == true
}

# Test AccessCatalog operation - access denied
test_access_catalog_denied if {
	user := "external_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "AccessCatalog",
			"resource": {"catalog": {"name": "internal_data"}},
		},
		"context": {
			"identity": {
				"groups": ["external"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "(employee|contractor)",
			"catalog": "internal_data",
			"allow": "read-only",
		}]

	result == false
}

# Test CreateCatalog operation - success
test_create_catalog_success if {
	user := "admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateCatalog",
			"resource": {"catalog": {"name": "new_catalog"}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "admin",
			"catalog": "*",
			"allow": "all",
			"owner": true,
		}]

	result == true
}

# Test CreateCatalog operation - insufficient privileges
test_create_catalog_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateCatalog",
			"resource": {"catalog": {"name": "analyst_catalog"}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "analyst",
			"catalog": "(sales|marketing)",
			"allow": "read-only",
		}]

	result == false
}

# Test DropCatalog operation - success
test_drop_catalog_success if {
	user := "system_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropCatalog",
			"resource": {"catalog": {"name": "deprecated_catalog"}},
		},
		"context": {
			"identity": {
				"groups": ["system_admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "system_admin",
			"catalog": "deprecated_catalog",
			"allow": "all",
			"owner": true,
		}]

	result == true
}

# Test DropCatalog operation - access denied
test_drop_catalog_denied if {
	user := "team_lead"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropCatalog",
			"resource": {"catalog": {"name": "production"}},
		},
		"context": {
			"identity": {
				"groups": ["team_leads"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "team_lead",
			"catalog": "production",
			"allow": "read-only",
		}]

	result == false
}
