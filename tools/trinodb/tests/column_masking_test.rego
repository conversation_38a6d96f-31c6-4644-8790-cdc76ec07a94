package tools.trinodb.tests.column_masking

import data.tools.trinodb.main
import rego.v1

# Test basic column masking operation
test_column_mask_basic if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "GetColumnMask",
			"resource": {"column": {
				"catalogName": "customer_data",
				"schemaName": "public",
				"tableName": "users",
				"columnName": "email",
				"columnType": "varchar",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test with mock rules that mask the email column
	column_mask := main.column_masking with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "customer_data",
			"schema": "public",
			"table": "users",
			"privileges": ["SELECT"],
			"columns": [{
				"name": "email",
				"allow": true,
				"mask": "regexp_replace(email, '(.*)@(.*)\\\\.com', '****@$2.com')",
			}],
		}]

	# Verify the mask expression is correctly applied
	column_mask.expression == "regexp_replace(email, '(.*)@(.*)\\\\.com', '****@$2.com')"
}

# Test column masking with template substitution
test_column_mask_with_template if {
	user := "user1"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "GetColumnMask",
			"resource": {"column": {
				"catalogName": "finance",
				"schemaName": "private",
				"tableName": "salaries",
				"columnName": "amount",
				"columnType": "decimal(10,2)",
			}},
		},
		"context": {
			"identity": {
				"groups": ["finance_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test with mock rules that use a template for masking
	column_mask := main.column_masking with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"user": "user1",
			"catalog": "finance",
			"schema": "private",
			"table": "salaries",
			"privileges": ["SELECT"],
			"columns": [{
				"name": "amount",
				"allow": true,
				"mask": "CASE WHEN user = 'user1' THEN amount ELSE CAST(amount * 0.9 AS decimal(10,2)) END",
				"mask_template": "salary_mask",
				"mask_substitution": {"user_name": "user"},
			}],
		}]
		with data.configurations.tenant as {"templates": {"column_masks": {"salary_mask": "CASE WHEN user = '${user_name}' THEN amount ELSE CAST(amount * 0.9 AS decimal(10,2)) END"}}}

	# Verify the mask expression with template substitution
	column_mask.expression == "CASE WHEN user = 'user1' THEN amount ELSE CAST(amount * 0.9 AS decimal(10,2)) END"
}

# Test batch column masking
test_batch_column_masking if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "FilterColumns",
			"filterResources": [{"table": {
				"catalogName": "customer_data",
				"schemaName": "public",
				"tableName": "users",
				"columns": ["id", "name", "email", "phone", "address"],
			}}],
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Mock rules that define column-level permissions and masks
	batch_results := main.batch with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "customer_data",
			"schema": "public",
			"table": "users",
			"privileges": ["SELECT"],
			"columns": [
				{"name": "id", "allow": true},
				{"name": "name", "allow": true},
				{"name": "email", "allow": true, "mask": "regexp_replace(email, '(.*)@(.*)\\\\.com', '****@$2.com')"},
				{"name": "phone", "allow": true, "mask": "regexp_replace(phone, '(\\\\d{3})(\\\\d{3})(\\\\d{4})', '$1-XXX-XXXX')"},
				{"name": "address", "allow": false},
			],
		}]

	# The batch results should contain indices for allowed columns
	0 in batch_results # id
	1 in batch_results # name
	2 in batch_results # email
	3 in batch_results # phone
	not 4 in batch_results # address should be filtered out

	# Verify correct column count (4 out of 5 allowed)
	count(batch_results) == 4
}

# Test batch column masking with complex rules
test_batch_column_masking_complex if {
	user := "data_scientist"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "FilterColumns",
			"filterResources": [{"table": {
				"catalogName": "customer_data",
				"schemaName": "public",
				"tableName": "users",
				"columns": ["id", "name", "email", "phone", "address", "credit_card", "ssn"],
			}}],
		},
		"context": {
			"identity": {
				"groups": ["data_team", "research"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Mock rules with complex column permissions based on user roles
	batch_results := main.batch with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"user": "data_scientist",
				"catalog": "customer_data",
				"schema": "public",
				"table": "users",
				"privileges": ["SELECT"],
				"columns": [
					{"name": "id", "allow": true},
					{"name": "name", "allow": true},
					{"name": "email", "allow": true, "mask": "regexp_replace(email, '(.*)@(.*)\\\\.com', '****@$2.com')"},
					# {"name": "phone", "allow": false},
					{"name": "address", "allow": false},
					{"name": "credit_card", "allow": false},
					{"name": "ssn", "allow": false},
				],
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"group": "research",
				"catalog": "customer_data",
				"schema": "public",
				"table": "users",
				"privileges": ["SELECT"],
				"columns": [{"name": "phone", "allow": true, "mask": "'XXX-XXX-XXXX'"}],
			},
		]

	# Expected results based on combined rules
	0 in batch_results # id
	1 in batch_results # name
	2 in batch_results # email with mask
	3 in batch_results # phone (allowed via group rule with mask)
	not 4 in batch_results # address should be filtered out
	not 5 in batch_results # credit_card should be filtered out
	not 6 in batch_results # ssn should be filtered out

	# Verify correct column count (4 out of 7 allowed)
	count(batch_results) == 4
}

# Test for batch_column_masking operation
test_batch_column_masking_operation if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "GetColumnMask",
			"filterResources": [
				{"column": {
					"catalogName": "customer_data",
					"schemaName": "public",
					"tableName": "users",
					"columnName": "email",
					"columnType": "varchar",
				}},
				{"column": {
					"catalogName": "customer_data",
					"schemaName": "public",
					"tableName": "users",
					"columnName": "phone",
					"columnType": "varchar",
				}},
			],
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Mock rules for different column masks
	batch_column_masks := main.batch_column_masking with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "customer_data",
			"schema": "public",
			"table": "users",
			"privileges": ["SELECT"],
			"columns": [
				{"name": "email", "allow": true, "mask": "regexp_replace(email, '(.*)@(.*)\\\\.com', '****@$2.com')"},
				{"name": "phone", "allow": true, "mask": "regexp_replace(phone, '(\\\\d{3})(\\\\d{3})(\\\\d{4})', '$1-XXX-XXXX')"},
			],
		}]

	# Verify masks for both columns
	count(batch_column_masks) == 2

	# Verify email mask exists in the set
	email_mask_exists := count([mask |
		mask := batch_column_masks[_]
		mask.viewExpression.expression == "regexp_replace(email, '(.*)@(.*)\\\\.com', '****@$2.com')"
		mask.index == 0
	]) == 1

	# Verify phone mask exists in the set
	phone_mask_exists := count([mask |
		mask := batch_column_masks[_]
		mask.viewExpression.expression == "regexp_replace(phone, '(\\\\d{3})(\\\\d{3})(\\\\d{4})', '$1-XXX-XXXX')"
		mask.index == 1
	]) == 1

	email_mask_exists == true
	phone_mask_exists == true
}
