package tools.trinodb.tests.cross_tenant_test

import data.tools.trinodb.main

import rego.v1

# Test cross-tenant access: Source tenant zomato accessing blinkit resources
test_cross_tenant_zomato_to_blinkit_access if {
	user := "data_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "blinkit_catalog",
				"schemaName": "orders",
				"tableName": "customer_orders",
				"columns": ["order_id", "customer_id"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {
			"food_delivery": ["zomato"],
			"quick_commerce": ["blinkit"],
		}
		with data.configurations.global.catalog_tenant_mapping as {
			"blinkit_catalog": ["blinkit"],
			"zomato_orders": ["zomato"],
		}
		with data.user_attributes as {"data_analyst": {"line_of_business": "food_delivery"}}
		with data.configurations.global.rules as []
		with data.configurations.blinkit.rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"catalog": "blinkit_catalog",
				"user": "data_analyst",
				"allow": "read-only",
				"tenant": ["zomato", "blinkit"],
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "blinkit_catalog",
				"schema": "orders",
				"table": "customer_orders",
				"user": "data_analyst",
				"privileges": ["SELECT"],
				"tenant": ["zomato"],
				"columns": [
					{"name": "order_id", "allow": true},
					{"name": "customer_id", "allow": true},
				],
			},
		]

	result == true
}

# Test cross-tenant access denied: Source tenant not in rule's tenant list
test_cross_tenant_access_denied_not_in_tenant_list if {
	user := "restricted_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "grofers_catalog",
				"schemaName": "inventory",
				"tableName": "products",
				"columns": ["product_id"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {
			"food_delivery": ["zomato"],
			"grocery": ["grofers"],
		}
		with data.configurations.global.catalog_tenant_mapping as {
			"grofers_catalog": ["grofers"],
			"zomato_orders": ["zomato"],
		}
		with data.user_attributes as {"restricted_analyst": {"line_of_business": "food_delivery"}}
		with data.configurations.global.rules as []
		with data.configurations.grofers.rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"catalog": "grofers_catalog",
			"schema": "inventory",
			"table": "products",
			"user": "restricted_analyst",
			"privileges": ["SELECT"],
			"tenant": ["grofers"], # Only grofers tenant allowed, zomato not included
		}]

	result == false
}

# Test multi-destination tenant scenario
test_cross_tenant_multiple_destinations if {
	user := "platform_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "shared_analytics",
				"schemaName": "metrics",
				"tableName": "kpi_dashboard",
				"columns": ["metric_name", "metric_value"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["platform_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {
			"food_delivery": ["zomato"],
			"quick_commerce": ["blinkit"],
			"grocery": ["grofers"],
			"platform": ["platform"],
		}
		with data.configurations.global.catalog_tenant_mapping as {
			"shared_analytics": ["blinkit"],
			"zomato_orders": ["zomato"],
		}
		with data.dynamic_data.user_attributes as {"platform_engineer": {"line_of_business": "platform"}}
		with data.configurations.global.rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"catalog": "shared_analytics",
			"user": "platform_engineer",
			"allow": "read-only",
			"tenant": ["zomato", "blinkit", "grofers", "platform"],
		}]
		with data.configurations.blinkit.rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"catalog": "shared_analytics",
			"schema": "metrics",
			"table": "kpi_dashboard",
			"user": "platform_engineer",
			"privileges": ["SELECT"],
			"tenant": ["platform"],
		}]

	result == true
}

# Test user attribute rules in cross-tenant scenario
test_cross_tenant_with_user_attributes if {
	user := "senior_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "hyperpure_catalog",
				"schemaName": "supply_chain",
				"tableName": "vendor_data",
				"columns": ["vendor_id", "performance_score"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["senior_analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {
			"food_delivery": ["zomato"],
			"b2b": ["hyperpure"],
		}
		with data.configurations.global.catalog_tenant_mapping as {
			"hyperpure_catalog": ["hyperpure"],
			"zomato_orders": ["zomato"],
		}
		with data.dynamic_data.user_attributes as {"senior_analyst": {"line_of_business": "b2b"}}
		with data.configurations.global.rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"catalog": "hyperpure_catalog",
			"user": "senior_analyst",
			"allow": "read-only",
			"tenant": ["zomato", "hyperpure"],
		}]
		with data.configurations.hyperpure.rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"catalog": "hyperpure_catalog",
			"schema": "supply_chain",
			"table": "vendor_data",
			"user": "senior_analyst",
			"privileges": ["SELECT"],
			"columns": [
				{"name": "vendor_id", "allow": true},
				{"name": "performance_score", "allow": true},
			],
		}]

	result == true
}

# Test rule priority in cross-tenant scenario
test_cross_tenant_rule_priority if {
	user := "ops_manager"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "UpdateTableColumns",
			"resource": {"table": {
				"catalogName": "logistics_shared",
				"schemaName": "operations",
				"tableName": "delivery_routes",
				"columns": ["route_status"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["operations"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {
			"food_delivery": ["zomato"],
			"logistics": ["blinkit"],
		}
		with data.configurations.global.catalog_tenant_mapping as {
			"logistics_shared": ["blinkit"],
			"zomato_orders": ["zomato"],
		}
		with data.user_attributes as {"ops_manager": {"line_of_business": "food_delivery"}}
		with data.configurations.global.rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"catalog": "logistics_shared",
			"user": "ops_manager",
			"allow": "all",
			"tenant": ["zomato", "blinkit"],
		}]
		with data.configurations.blinkit.rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"catalog": "logistics_shared",
			"schema": "operations",
			"table": "delivery_routes",
			"user": "ops_manager",
			"privileges": ["UPDATE"],
			"tenant": ["zomato"],
			"columns": [{"name": "route_status", "allow": true}],
		}]

	result == true
}

# Test cross-tenant access with schema authorization
test_cross_tenant_schema_authorization if {
	user := "schema_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetSchemaAuthorization",
			"grantee": {
				"name": "data_team_lead",
				"type": "USER",
			},
			"resource": {"schema": {
				"catalogName": "shared_dwh",
				"schemaName": "common_metrics",
			}},
		},
		"context": {
			"identity": {
				"groups": ["schema_admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {
			"food_delivery": ["zomato"],
			"data_platform": ["blinkit"],
		}
		with data.configurations.global.catalog_tenant_mapping as {
			"shared_dwh": ["blinkit"],
			"zomato_orders": ["zomato"],
		}
		with data.user_attributes as {"schema_admin": {"line_of_business": "food_delivery"}}
		with data.configurations.global.rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"catalog": "shared_dwh",
			"user": "schema_admin",
			"allow": "all",
			"tenant": ["zomato", "blinkit"],
		}]
		with data.configurations.blinkit.rules as [
			{
				"entity_category": "data_entity",
				"type": "schema",
				"catalog": "shared_dwh",
				"schema": "common_metrics",
				"user": "schema_admin",
				"owner": true,
				"tenant": ["zomato"],
			},
			{
				"entity_category": "data_entity",
				"type": "authorization",
				"original_user": "schema_admin",
				"new_user": "data_team_lead",
				"allow": true,
				"tenant": ["zomato"],
			},
		]

	result == true
}

# Test cross-tenant function access
test_cross_tenant_function_execution if {
	user := "ml_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ExecuteFunction",
			"resource": {"function": {
				"catalogName": "ml_platform",
				"schemaName": "models",
				"functionName": "recommendation_engine",
			}},
		},
		"context": {
			"identity": {
				"groups": ["ml_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {
			"food_delivery": ["zomato"],
			"ml_platform": ["blinkit"],
		}
		with data.configurations.global.catalog_tenant_mapping as {
			"ml_platform": ["blinkit"],
			"zomato_orders": ["zomato"],
		}
		with data.user_attributes as {"ml_engineer": {"line_of_business": "food_delivery"}}
		with data.configurations.global.rules as []
		with data.configurations.blinkit.rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"catalog": "ml_platform",
				"user": "ml_engineer",
				"allow": "read-only",
				"tenant": ["zomato"],
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"catalog": "ml_platform",
				"schema": "models",
				"function": "recommendation_engine",
				"user": "ml_engineer",
				"privileges": ["EXECUTE"],
				"tenant": ["zomato"],
			},
		]

	result == true
}

# Test edge case: Empty destination tenant list
test_cross_tenant_empty_destination_list if {
	user := "test_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
				"columns": ["id"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["test_group"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {"food_delivery": "zomato"}
		with data.configurations.global.catalog_tenant_mapping as {"zomato_orders": "zomato"}
		# test_catalog not present - will map to "default"

with 		data.user_attributes as {"test_user": {"line_of_business": "food_delivery"}}
		with data.configurations.global.rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"catalog": "test_catalog",
			"schema": "test_schema",
			"table": "test_table",
			"user": "test_user",
			"privileges": ["SELECT"],
			"tenant": ["zomato"],
		}]

	# Should fail because no destination tenant rules will be loaded
	result == false
}
