package tools.trinodb.tests.entity_operations

import data.tools.trinodb.main
import rego.v1

# Test catalog access permissions
test_catalog_access_permissions if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "AccessCatalog",
			"resource": {"catalog": {"name": "sales"}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test catalog access with read-only permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "analyst",
			"catalog": "sales",
			"allow": "read-only",
		}]

	# Verify read-only access is granted
	result == true
}

# Test catalog access denial
test_catalog_access_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "AccessCatalog",
			"resource": {"catalog": {"name": "admin_data"}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test catalog access with a rule that doesn't match this catalog
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "analyst",
			"catalog": "sales|marketing",
			"allow": "read-only",
		}]

	# Verify access is denied
	result == false
}

# Test schema creation permission
test_schema_creation_permission if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateSchema",
			"resource": {"schema": {
				"catalogName": "data_lake",
				"schemaName": "new_project",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test schema creation permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "data_engineer",
				"catalog": "data_lake",
				"allow": "all", # Need full access to create schema
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "data_engineer",
				"catalog": "data_lake",
				"schema": ".*",
				"owner": true,
			},
		]

	# Verify schema creation is allowed
	result == true
}

# Test table creation permission
test_table_creation_permission if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateTable",
			"resource": {"table": {
				"catalogName": "hive",
				"schemaName": "analytics",
				"tableName": "new_metrics",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test table creation permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "data_engineer",
				"catalog": "hive",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"user": "data_engineer",
				"catalog": "hive",
				"schema": "analytics",
				"privileges": ["INSERT", "OWNERSHIP"],
			},
		]

	# Verify table creation is allowed
	result == true
}

# Test table insert permission
test_table_insert_permission if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "InsertIntoTable",
			"resource": {"table": {
				"catalogName": "data_lake",
				"schemaName": "staging",
				"tableName": "daily_imports",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test INSERT permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "data_engineer",
				"catalog": "data_lake",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"user": "data_engineer",
				"catalog": "data_lake",
				"schema": "staging",
				"table": "daily_imports",
				"privileges": ["INSERT", "SELECT"],
			},
		]

	# Verify INSERT permission is granted
	result == true
}

# Test view creation permission
test_view_creation_permission if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateView",
			"resource": {"table": {
				"catalogName": "analytics",
				"schemaName": "dashboards",
				"tableName": "monthly_summary",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test view creation permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analyst",
				"catalog": "analytics",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"user": "analyst",
				"catalog": "analytics",
				"schema": "dashboards",
				"privileges": ["OWNERSHIP"],
			},
		]

	# Verify view creation is allowed
	result == true
}

# Test table drop permission
test_table_drop_permission if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropTable",
			"resource": {"table": {
				"catalogName": "data_lake",
				"schemaName": "staging",
				"tableName": "outdated_data",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test DROP permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "data_engineer",
				"catalog": "data_lake",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"user": "data_engineer",
				"catalog": "data_lake",
				"schema": "staging",
				"table": "outdated_data",
				"privileges": ["OWNERSHIP"],
			},
		]

	# Verify DROP permission is granted
	result == true
}

# Test batch catalog filtering
test_batch_catalog_filtering if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "FilterCatalogs",
			"filterResources": [
				{"catalog": {"name": "sales"}},
				{"catalog": {"name": "marketing"}},
				{"catalog": {"name": "hr"}},
				{"catalog": {"name": "finance"}},
			],
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test batch catalog filtering with permissions for specific catalogs
	batch_results := main.batch with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analyst",
				"catalog": "sales|marketing",
				"allow": "read-only",
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"user": "analyst",
				"catalog": "sales|marketing",
				"schema": "staging",
				"table": "outdated_data",
				"privileges": ["OWNERSHIP"],
			},
		]

	# Verify only allowed catalogs appear in the results
	0 in batch_results # sales
	1 in batch_results # marketing
	not 2 in batch_results # hr should be filtered out
	not 3 in batch_results # finance should be filtered out

	# Verify count matches expected
	count(batch_results) == 2
}

# Test batch schema filtering
test_batch_schema_filtering if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "FilterSchemas",
			"filterResources": [
				{"schema": {
					"catalogName": "sales",
					"schemaName": "public",
				}},
				{"schema": {
					"catalogName": "sales",
					"schemaName": "internal",
				}},
				{"schema": {
					"catalogName": "sales",
					"schemaName": "history",
				}},
			],
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test batch schema filtering with rules for specific schemas
	batch_results := main.batch with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analyst",
				"catalog": "sales",
				"allow": "read-only",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "analyst",
				"catalog": "sales",
				"schema": "public|history",
				"owner": true,
			},
		]

	# Verify allowed schemas appear in results
	0 in batch_results # public
	not 1 in batch_results # internal should be filtered out
	2 in batch_results # history

	# Verify count matches expected
	count(batch_results) == 2
}

# Test batch table filtering
test_batch_table_filtering if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "FilterTables",
			"filterResources": [
				{"table": {
					"catalogName": "sales",
					"schemaName": "public",
					"tableName": "orders",
				}},
				{"table": {
					"catalogName": "sales",
					"schemaName": "public",
					"tableName": "customers",
				}},
				{"table": {
					"catalogName": "sales",
					"schemaName": "public",
					"tableName": "internal_metrics",
				}},
			],
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test batch table filtering with rules for specific tables
	batch_results := main.batch with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analyst",
				"catalog": "sales",
				"allow": "read-only",
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"user": "analyst",
				"catalog": "sales",
				"schema": "public",
				"table": "orders|customers",
				"privileges": ["SELECT"],
			},
		]

	# Verify allowed tables appear in results
	0 in batch_results # orders
	1 in batch_results # customers
	not 2 in batch_results # internal_metrics should be filtered out

	# Verify count matches expected
	count(batch_results) == 2
}

# Test ShowCreateSchema operation - success
test_show_create_schema_success if {
	user := "schema_owner"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowCreateSchema",
			"resource": {"schema": {
				"catalogName": "analytics",
				"schemaName": "reporting",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "schema_owner",
				"catalog": "analytics",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "schema_owner",
				"catalog": "analytics",
				"schema": "reporting",
				"owner": true,
			},
		]

	result == true
}

# Test ShowCreateSchema operation - denied
test_show_create_schema_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowCreateSchema",
			"resource": {"schema": {
				"catalogName": "private",
				"schemaName": "confidential",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analyst",
				"catalog": "private",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "analyst",
				"catalog": "private",
				"schema": "public",
				"owner": true,
			},
		]

	result == false
}

# Test DropSchema operation - success
test_drop_schema_success if {
	user := "schema_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropSchema",
			"resource": {"schema": {
				"catalogName": "test_env",
				"schemaName": "temp_schema",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "schema_admin",
				"catalog": "test_env",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "schema_admin",
				"catalog": "test_env",
				"schema": "temp_schema",
				"owner": true,
			},
		]

	result == true
}

# Test DropSchema operation - denied
test_drop_schema_denied if {
	user := "developer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropSchema",
			"resource": {"schema": {
				"catalogName": "production",
				"schemaName": "core_data",
			}},
		},
		"context": {
			"identity": {
				"groups": ["developers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "developer",
				"catalog": "production",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "developer",
				"catalog": "production",
				"schema": "dev_*",
				"owner": true,
			},
		]

	result == false
}
