package tools.trinodb.tests.function_operations

import data.tools.trinodb.main
import rego.v1

# Test ShowFunctions operation - success
test_show_functions_success if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowFunctions",
			"resource": {"schema": {
				"catalogName": "system",
				"schemaName": "builtin",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analyst",
				"catalog": "system",
				"allow": "read-only",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "analyst",
				"catalog": "system",
				"schema": "builtin",
				"function": "*",
				"privileges": ["SELECT"],
			},
		]

	result == true
}

# Test ShowFunctions operation - access denied
test_show_functions_denied if {
	user := "restricted_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowFunctions",
			"resource": {"schema": {
				"catalogName": "admin",
				"schemaName": "internal",
			}},
		},
		"context": {
			"identity": {
				"groups": ["restricted"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "function",
			"user": "(admin|trusted_user)",
			"catalog": "admin",
			"schema": "internal",
			"function": "*",
			"privileges": ["SELECT"],
		}]

	result == false
}

# Test ExecuteFunction operation - success
test_execute_function_success if {
	user := "data_scientist"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ExecuteFunction",
			"resource": {"function": {
				"catalogName": "ml",
				"schemaName": "models",
				"functionName": "predict_churn",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_scientists"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "data_scientist",
				"catalog": "ml",
				"allow": "read-only",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "data_scientist",
				"catalog": "ml",
				"schema": "models",
				"function": "predict_.*",
				"privileges": ["EXECUTE"],
			},
		]

	result == true
}

# Test ExecuteFunction operation - insufficient privileges
test_execute_function_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ExecuteFunction",
			"resource": {"function": {
				"catalogName": "admin",
				"schemaName": "system",
				"functionName": "admin_function",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "function",
			"user": "admin",
			"catalog": "admin",
			"schema": "system",
			"function": "admin_.*",
			"privileges": ["EXECUTE"],
		}]

	result == false
}

# Test ExecuteTableProcedure operation - success
test_execute_table_procedure_success if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ExecuteTableProcedure",
			"resource": {"function": {
				"catalogName": "hive",
				"schemaName": "default",
				"functionName": "optimize_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "data_engineer",
				"catalog": "hive",
				"allow": "all",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "data_engineer",
				"catalog": "hive",
				"schema": "default",
				"function": "optimize_.*",
				"privileges": ["EXECUTE"],
			},
		]

	result == true
}

# Test ExecuteTableProcedure operation - denial for insufficient privileges
test_execute_table_procedure_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ExecuteTableProcedure",
			"resource": {"function": {
				"catalogName": "admin",
				"schemaName": "procedures",
				"functionName": "critical_procedure",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "regular_user",
				"catalog": "admin",
				"allow": "read-only",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "(admin|super_user)", # Only admins can execute
				"catalog": "admin",
				"schema": "procedures",
				"function": "critical_procedure",
				"privileges": ["EXECUTE"],
			},
		]
}

# Test ExecuteTableProcedure operation - catalog access denied
test_execute_table_procedure_denied_catalog_access if {
	user := "external_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ExecuteTableProcedure",
			"resource": {"function": {
				"catalogName": "restricted",
				"schemaName": "procedures",
				"functionName": "any_procedure",
			}},
		},
		"context": {
			"identity": {
				"groups": ["external"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [] # No catalog access
}

# Test ExecuteProcedure operation - success
test_execute_procedure_success if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ExecuteProcedure",
			"resource": {"function": {
				"catalogName": "data_processing",
				"schemaName": "etl",
				"functionName": "refresh_tables",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "data_engineer",
				"catalog": "data_processing",
				"allow": "all",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "data_engineer",
				"catalog": "data_processing",
				"schema": "etl",
				"function": "refresh_tables",
				"privileges": ["EXECUTE"],
			},
		]

	result == true
}

# Test ExecuteProcedure operation - denial for insufficient privileges
test_execute_procedure_denied_insufficient_privileges if {
	user := "intern"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ExecuteProcedure",
			"resource": {"function": {
				"catalogName": "production",
				"schemaName": "admin",
				"functionName": "system_maintenance",
			}},
		},
		"context": {
			"identity": {
				"groups": ["interns"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "intern",
				"catalog": "production",
				"allow": "read-only",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "(admin|senior_engineer)", # Intern doesn't have access
				"catalog": "production",
				"schema": "admin",
				"function": "system_maintenance",
				"privileges": ["EXECUTE"],
			},
		]
}

# Test CreateViewWithExecuteFunction operation - success
test_create_view_with_execute_function_success if {
	user := "analytics_lead"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateViewWithExecuteFunction",
			"resource": {"function": {
				"catalogName": "analytics",
				"schemaName": "functions",
				"functionName": "calculate_metrics",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analytics_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analytics_lead",
				"catalog": "analytics",
				"allow": "all",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "analytics_lead",
				"catalog": "analytics",
				"schema": "functions",
				"function": "calculate_.*",
				"privileges": ["GRANT_EXECUTE"],
			},
		]

	result == true
}

# Test CreateViewWithExecuteFunction operation - denial for insufficient privileges
test_create_view_with_execute_function_denied_insufficient_privileges if {
	user := "junior_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateViewWithExecuteFunction",
			"resource": {"function": {
				"catalogName": "advanced",
				"schemaName": "ml",
				"functionName": "complex_model",
			}},
		},
		"context": {
			"identity": {
				"groups": ["junior_analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "junior_analyst",
				"catalog": "advanced",
				"allow": "read-only",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "(senior_analyst|ml_engineer)", # Junior doesn't have GRANT_EXECUTE
				"catalog": "advanced",
				"schema": "ml",
				"function": "complex_model",
				"privileges": ["GRANT_EXECUTE"],
			},
		]
}

# Test ShowCreateFunction operation - success
test_show_create_function_success if {
	user := "function_developer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowCreateFunction",
			"resource": {"function": {
				"catalogName": "custom",
				"schemaName": "udfs",
				"functionName": "my_function",
			}},
		},
		"context": {
			"identity": {
				"groups": ["developers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "function_developer",
				"catalog": "custom",
				"allow": "all",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "function_developer",
				"catalog": "custom",
				"schema": "udfs",
				"function": "my_function",
				"privileges": ["OWNERSHIP"],
			},
		]

	result == true
}

# Test ShowCreateFunction operation - denial for insufficient privileges
test_show_create_function_denied_insufficient_privileges if {
	user := "viewer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowCreateFunction",
			"resource": {"function": {
				"catalogName": "proprietary",
				"schemaName": "secret",
				"functionName": "confidential_function",
			}},
		},
		"context": {
			"identity": {
				"groups": ["viewers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "viewer",
				"catalog": "proprietary",
				"allow": "read-only",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "(owner|admin)", # Viewer doesn't have ownership
				"catalog": "proprietary",
				"schema": "secret",
				"function": "confidential_function",
				"privileges": ["OWNERSHIP"],
			},
		]
}

# Test CreateFunction operation - success
test_create_function_success if {
	user := "function_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateFunction",
			"resource": {"function": {
				"catalogName": "development",
				"schemaName": "udfs",
				"functionName": "new_function",
			}},
		},
		"context": {
			"identity": {
				"groups": ["function_admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "function_admin",
				"catalog": "development",
				"allow": "all",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "function_admin",
				"catalog": "development",
				"schema": "udfs",
				"function": "*",
				"privileges": ["OWNERSHIP"],
			},
		]

	result == true
}

# Test CreateFunction operation - denial for insufficient privileges
test_create_function_denied_insufficient_privileges if {
	user := "regular_developer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateFunction",
			"resource": {"function": {
				"catalogName": "production",
				"schemaName": "critical",
				"functionName": "production_function",
			}},
		},
		"context": {
			"identity": {
				"groups": ["developers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "regular_developer",
				"catalog": "production",
				"allow": "read-only", # No write access
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "(senior_admin|function_admin)", # Regular dev doesn't have ownership
				"catalog": "production",
				"schema": "critical",
				"function": "*",
				"privileges": ["OWNERSHIP"],
			},
		]
}

# Test DropFunction operation - success
test_drop_function_success if {
	user := "function_owner"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropFunction",
			"resource": {"function": {
				"catalogName": "development",
				"schemaName": "deprecated",
				"functionName": "old_function",
			}},
		},
		"context": {
			"identity": {
				"groups": ["function_owners"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "function_owner",
				"catalog": "development",
				"allow": "all",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "function_owner",
				"catalog": "development",
				"schema": "deprecated",
				"function": "old_function",
				"privileges": ["OWNERSHIP"],
			},
		]

	result == true
}

# Test DropFunction operation - denial for insufficient privileges
test_drop_function_denied_insufficient_privileges if {
	user := "contributor"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropFunction",
			"resource": {"function": {
				"catalogName": "shared",
				"schemaName": "library",
				"functionName": "important_function",
			}},
		},
		"context": {
			"identity": {
				"groups": ["contributors"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "contributor",
				"catalog": "shared",
				"allow": "read-only",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "(owner|admin)", # Contributor doesn't have ownership
				"catalog": "shared",
				"schema": "library",
				"function": "important_function",
				"privileges": ["OWNERSHIP"],
			},
		]
}

# Test FilterFunctions operation - batch filtering
test_filter_functions_batch_filtering if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "FilterFunctions",
			"filterResources": [
				{"function": {
					"catalogName": "analytics",
					"schemaName": "public",
					"functionName": "sum_function",
				}},
				{"function": {
					"catalogName": "analytics",
					"schemaName": "public",
					"functionName": "avg_function",
				}},
				{"function": {
					"catalogName": "analytics",
					"schemaName": "private",
					"functionName": "restricted_function",
				}},
			],
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test batch function filtering with rules for specific functions
	batch_results := main.batch with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analyst",
				"catalog": "analytics",
				"allow": "read-only",
			},
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "analyst",
				"catalog": "analytics",
				"schema": "public",
				"function": "sum_function|avg_function",
				"privileges": ["EXECUTE"],
			},
		]

	# Verify allowed functions appear in results
	0 in batch_results # sum_function
	1 in batch_results # avg_function
	not 2 in batch_results # restricted_function should be filtered out

	# Verify count matches expected
	count(batch_results) == 2
}
