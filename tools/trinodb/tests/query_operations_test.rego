package tools.trinodb.tests.query_operations

import data.tools.trinodb.main
import rego.v1

# Test ExecuteQuery operation - success
test_execute_query_success if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {"operation": "ExecuteQuery"},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "(analyst|data_scientist)",
			"allow": ["EXECUTE"],
		}]

	result == true
}

# Test ExecuteQuery operation - access denied
test_execute_query_denied if {
	user := "restricted_user"
	request_context := {
		"is_test": true,
		"action": {"operation": "ExecuteQuery"},
		"context": {
			"identity": {
				"groups": ["restricted"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "(analyst|trusted_user)",
			"allow": ["EXECUTE"],
		}]

	result == false
}

# Test KillQueryOwnedBy operation - success as admin
test_kill_query_owned_by_admin_success if {
	user := "admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "KillQueryOwnedBy",
			"resource": {"user": {
				"groups": ["analysts"],
				"user": "problem_user",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "admin",
			"allow": ["KILL"],
		}]

	result == true
}

# Test KillQueryOwnedBy operation - success for own queries
test_kill_query_owned_by_self_success if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "KillQueryOwnedBy",
			"resource": {"user": {
				"groups": ["analysts"],
				"user": "analyst",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "analyst",
			"queryOwner": "analyst",
			"allow": ["KILL"],
		}]

	result == true
}

# Test KillQueryOwnedBy operation - access denied for other users
test_kill_query_owned_by_others_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "KillQueryOwnedBy",
			"resource": {"user": {
				"groups": ["data_scientists"],
				"user": "other_user",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "analyst",
			"queryOwner": "analyst",
			"allow": ["KILL"],
		}]

	result == false
}

# Test KillQueryOwnedBy operation - insufficient privileges for regular user
test_kill_query_owned_by_insufficient_privileges if {
	user := "junior_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "KillQueryOwnedBy",
			"resource": {"user": {
				"groups": ["senior_analysts"],
				"user": "senior_analyst",
			}},
		},
		"context": {
			"identity": {
				"groups": ["junior_staff"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "(admin|manager)", # Only admin/manager can kill others' queries
			"allow": ["KILL"],
		}]

	result == false
}

# Test ViewQueryOwnedBy operation - success as admin
test_view_query_owned_by_admin_success if {
	user := "admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ViewQueryOwnedBy",
			"resource": {"user": {
				"groups": ["analysts"],
				"user": "team_member",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "admin",
			"allow": ["VIEW"],
		}]

	result == true
}

# Test ViewQueryOwnedBy operation - success for team leads
test_view_query_owned_by_team_lead_success if {
	user := "team_lead"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ViewQueryOwnedBy",
			"resource": {"user": {
				"groups": ["team_members"],
				"user": "team_analyst",
			}},
		},
		"context": {
			"identity": {
				"groups": ["team_leads"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "(team_lead|manager)",
			"queryOwner": "(team_.*|analyst_.*)",
			"allow": ["VIEW"],
		}]

	result == true
}

# Test ViewQueryOwnedBy operation - success for own queries
test_view_query_owned_by_self_success if {
	user := "data_scientist"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ViewQueryOwnedBy",
			"resource": {"user": {
				"groups": ["data_scientists"],
				"user": "data_scientist",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_scientists"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "data_scientist",
			"queryOwner": "data_scientist",
			"allow": ["VIEW"],
		}]

	result == true
}

# Test ViewQueryOwnedBy operation - access denied
test_view_query_owned_by_denied if {
	user := "junior_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ViewQueryOwnedBy",
			"resource": {"user": {
				"groups": ["senior_analysts"],
				"user": "senior_analyst",
			}},
		},
		"context": {
			"identity": {
				"groups": ["junior_analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "junior_analyst",
			"queryOwner": "junior_analyst",
			"allow": ["VIEW"],
		}]

	result == false
}

# Test FilterViewQueryOwnedBy operation - success
test_filter_view_query_owned_by_success if {
	user := "manager"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "FilterViewQueryOwnedBy",
			"resource": {"user": {
				"groups": ["team_members"],
				"user": "subordinate",
			}},
		},
		"context": {
			"identity": {
				"groups": ["managers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "manager",
			"queryOwner": "(team_.*|subordinate)",
			"allow": ["VIEW"],
		}]

	result == true
}

# Test FilterViewQueryOwnedBy operation - filtered access
test_filter_view_query_owned_by_filtered if {
	user := "department_head"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "FilterViewQueryOwnedBy",
			"resource": {"user": {
				"groups": ["department_analysts"],
				"user": "dept_analyst",
			}},
		},
		"context": {
			"identity": {
				"groups": ["department_heads"],
				"user": user,
			},
			"environment": "production",
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "query_engine_entity",
				"type": "query",
				"user": "department_head",
				"queryOwner": "dept_.*",
				"environment": "production",
				"allow": ["VIEW"],
			},
			{
				"entity_category": "query_engine_entity",
				"type": "query",
				"user": "department_head",
				"queryOwner": "contractor_.*",
				"environment": "production",
				"allow": [],
			},
		]

	result == true
}

# Test FilterViewQueryOwnedBy operation - pattern-based filtering
test_filter_view_query_owned_by_pattern_based if {
	user := "audit_manager"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "FilterViewQueryOwnedBy",
			"resource": {"user": {
				"groups": ["data_users"],
				"user": "analyst_finance",
			}},
		},
		"context": {
			"identity": {
				"groups": ["audit_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "audit_manager",
			"queryOwner": "(analyst_.*|developer_.*|manager_.*)", # Pattern matching
			"allow": ["VIEW"],
		}]

	result == true
}
