package tools.trinodb.tests.row_filters

import data.tools.trinodb.main
import data.rules.main as rules_main
import rego.v1

# Test basic row filtering
test_basic_row_filtering if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "GetRowFilters",
			"resource": {"table": {
				"catalogName": "sales",
				"schemaName": "public",
				"tableName": "orders",
			}},
		},
		"source": "common_cluster",
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test with mock rules that limit the analyst to only see their region
	row_filter := main.row_filters with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "sales",
			"schema": "public",
			"table": "orders",
			"filter": "region = 'EMEA'",
			"filter_environment": null,
			"privileges": ["SELECT"],
		}]

	# Verify the filter expression is correctly applied

	row_filter_expression_valid := count([rf |
		rf := row_filter[_]
		rf.expression == "region = 'EMEA'"
	]) == 1
	row_filter_expression_valid
}

# Test row filtering with variable substitution
test_row_filter_with_substitution if {
	user := "user1"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "GetRowFilters",
			"resource": {"table": {
				"catalogName": "customer_data",
				"schemaName": "public",
				"tableName": "customers",
			}},
		},
		"source": "common_cluster",
		"context": {
			"identity": {
				"groups": ["sales_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test with mock rules that use variable substitution
	row_filter := main.row_filters with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"user": "user1",
			"catalog": "customer_data",
			"schema": "public",
			"table": "customers",
			"filter": "kam_region",
			"filter_substitution": {"sub_region": "DELHI_NCR"},
			"privileges": ["SELECT"],
			"filter_template": true,
		}]
		with data.configurations.zomato as {"templates": {"row_level_filters": {"kam_region": {
			"template": "region = 'sub_region'",
			"default": "BHARAT",
		}}}}

	# Verify the filter expression is correctly substituted
	row_filter_expression_valid := count([rf |
		rf := row_filter[_]
		rf.expression == "region = 'DELHI_NCR'"
	]) == 1
	row_filter_expression_valid
}

# Test combining multiple row filters
test_combined_row_filters if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "GetRowFilters",
			"resource": {"table": {
				"catalogName": "sales",
				"schemaName": "public",
				"tableName": "orders",
			}},
		},
		"source": "common_cluster",
		"context": {
			"identity": {
				"groups": ["analysts", "emea_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test with multiple mock rules that should be combined
	row_filter := main.row_filters with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"user": "analyst",
				"catalog": "sales",
				"schema": "public",
				"table": "orders",
				"filter": "region = 'EMEA'",
				"filter_environment": null,
				"filter_substitution": {},
				"privileges": ["SELECT"],
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"group": "emea_team",
				"catalog": "sales",
				"schema": "public",
				"table": "orders",
				"filter": "order_date > '2023-01-01'",
				"filter_environment": null,
				"filter_substitution": {},
				"privileges": ["SELECT"],
			},
		]

	# Verify the combined filter expression
	# The implementation will combine these filters with AND
	row_filter_expression_valid := count([rf |
		rf := row_filter[_]
		contains(rf.expression, "region = 'EMEA'")
		contains(rf.expression, "order_date > '2023-01-01'")
		contains(rf.expression, " AND ")
	]) == 1
	row_filter_expression_valid
}

# Test row filter with environment variables
test_row_filter_with_environment if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "GetRowFilters",
			"resource": {"table": {
				"catalogName": "finance",
				"schemaName": "public",
				"tableName": "transactions",
			}},
		},
		"source": "common_cluster",
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test with a filter that uses environment variables
	row_filter := main.row_filters with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "finance",
			"schema": "public",
			"table": "transactions",
			"filter": "user=current_user()",
			"filter_environment": {"user": "presto_user"},
			"filter_substitution": {},
			"privileges": ["SELECT"],
		}]

	# The implementation should use the environment variable to construct the filter
	row_filter_expression_valid := count([rf |
		rf := row_filter[_]
		rf.expression == "user=current_user()"
		rf.identity == "presto_user"
	]) == 1
	row_filter_expression_valid
}

# Test blinkit-superset source filtering directly in rules module
test_blinkit_superset_source_filtering_direct if {
	# Test the row filter logic directly in the rules module
	request_context := {
		"source": "blinkit-superset",
		"user": "analyst",
		"groups": ["analysts"],
		"operation": "GetRowFilters",
		"tool": "trinodb",
		"catalog": "sales",
		"schema": "public",
		"table": "orders",
		"tenant_map": {"runtime_tenant": "zomato"},
		"action": {},
	}

	# Test with mock rules - one with blinkit-superset source and one without
	mock_rules := [
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "sales",
			"schema": "public",
			"table": "orders",
			"filter": "region = 'EMEA'",
			"filter_environment": null,
			"privileges": ["SELECT"],
			"source": "blinkit-superset",
		},
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "sales",
			"schema": "public",
			"table": "orders",
			"filter": "region = 'US'",
			"filter_environment": null,
			"privileges": ["SELECT"],
		},
	]

	# Test that row filter returns the blinkit-superset rule
	row_filter := rules_main.row_filter with input as request_context
		with rules_main.compiled_rules as mock_rules

	# Should get the EMEA filter (from blinkit-superset source rule)
	row_filter.expression == "region = 'EMEA'"
}

# Test allow rule for blinkit-superset source
test_blinkit_superset_allow if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "sales",
				"schemaName": "public",
				"tableName": "orders",
			}},
		},
		"source": "blinkit-superset",
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test that allow returns true for blinkit-superset source regardless of rules
	# Create a direct request context for the rules module
	rules_request_context := {
		"source": "blinkit-superset",
		"user": "analyst",
		"groups": ["analysts"],
		"operation": "SelectFromColumns",
		"tool": "trinodb",
		"catalog": "sales",
		"schema": "public",
		"table": "orders",
		"tenant_map": {"runtime_tenant": "zomato"},
		"action": {},
	}

	# Test that allow returns true for blinkit-superset source
	allow_result := rules_main.allow with input as rules_request_context

	allow_result == true
}

# Test that non-blinkit-superset source uses normal row filter logic
test_non_blinkit_superset_source_filtering if {
	# Test the row filter logic for non-blinkit-superset source
	request_context := {
		"source": "common_cluster",
		"user": "analyst",
		"groups": ["analysts"],
		"operation": "GetRowFilters",
		"tool": "trinodb",
		"catalog": "sales",
		"schema": "public",
		"table": "orders",
		"tenant_map": {"runtime_tenant": "zomato"},
		"action": {},
	}

	# Test with mock rules - one with blinkit-superset source and one without
	mock_rules := [
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "sales",
			"schema": "public",
			"table": "orders",
			"filter": "region = 'EMEA'",
			"filter_environment": null,
			"privileges": ["SELECT"],
			"source": "blinkit-superset",
		},
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "sales",
			"schema": "public",
			"table": "orders",
			"filter": "region = 'US'",
			"filter_environment": null,
			"privileges": ["SELECT"],
		},
	]

	# Test that row filter returns the normal rule (not filtered by source)
	row_filter := rules_main.row_filter with input as request_context
		with rules_main.compiled_rules as mock_rules

	# Should get one of the filters (normal logic applies both rules)
	row_filter.expression != null
}

# Test that blinkit-superset source only picks superset rules for row filtering
test_blinkit_superset_only_picks_superset_rules if {
	request_context := {
		"source": "blinkit-superset",
		"user": "analyst",
		"groups": ["analysts"],
		"operation": "GetRowFilters",
		"tool": "trinodb",
		"catalog": "sales",
		"schema": "public",
		"table": "orders",
		"tenant_map": {"runtime_tenant": "zomato"},
		"action": {},
	}

	# Mock rules with multiple sources - only blinkit-superset should be picked
	mock_rules := [
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "sales",
			"schema": "public",
			"table": "orders",
			"filter": "superset_filter = 'SUPERSET_ONLY'",
			"filter_environment": null,
			"privileges": ["SELECT"],
			"source": "blinkit-superset",
		},
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "sales",
			"schema": "public",
			"table": "orders",
			"filter": "common_filter = 'COMMON_CLUSTER'",
			"filter_environment": null,
			"privileges": ["SELECT"],
			"source": "common_cluster",
		},
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "sales",
			"schema": "public",
			"table": "orders",
			"filter": "no_source_filter = 'NO_SOURCE'",
			"filter_environment": null,
			"privileges": ["SELECT"],
		},
	]

	row_filter := rules_main.row_filter with input as request_context
		with rules_main.compiled_rules as mock_rules

	# Should only get the superset rule
	row_filter.expression == "superset_filter = 'SUPERSET_ONLY'"
}

# Test that non-blinkit-superset source includes all rules (normal behavior)
test_non_blinkit_superset_includes_all_rules if {
	request_context := {
		"source": "common_cluster",
		"user": "analyst",
		"groups": ["analysts"],
		"operation": "GetRowFilters",
		"tool": "trinodb",
		"catalog": "sales",
		"schema": "public",
		"table": "orders",
		"tenant_map": {"runtime_tenant": "zomato"},
		"action": {},
	}

	# Mock rules with superset and non-superset sources
	mock_rules := [
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "sales",
			"schema": "public",
			"table": "orders",
			"filter": "superset_filter = 'SUPERSET_ONLY'",
			"filter_environment": null,
			"privileges": ["SELECT"],
			"source": "blinkit-superset",
		},
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "sales",
			"schema": "public",
			"table": "orders",
			"filter": "no_source_filter = 'NO_SOURCE'",
			"filter_environment": null,
			"privileges": ["SELECT"],
		},
	]

	row_filter := rules_main.row_filter with input as request_context
		with rules_main.compiled_rules as mock_rules

	# Should get one of the rules (priority logic will pick one)
	# Both rules are valid for non-blinkit-superset sources
	row_filter.expression == "no_source_filter = 'NO_SOURCE'"
}

# Test that blinkit-superset source allows access even with explicit deny rules
test_blinkit_superset_overrides_explicit_deny if {
	request_context := {
		"source": "blinkit-superset",
		"user": "restricted_user",
		"groups": ["restricted_group"],
		"operation": "SelectFromColumns",
		"tool": "trinodb",
		"catalog": "restricted_catalog",
		"schema": "restricted_schema",
		"table": "restricted_table",
		"tenant_map": {"runtime_tenant": "zomato"},
		"action": {},
	}

	# Mock rules with explicit deny for this user/table
	mock_rules := [
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "restricted_user",
			"catalog": "restricted_catalog",
			"schema": "restricted_schema",
			"table": "restricted_table",
			"privileges": [],  # No privileges = explicit deny
			"source": "blinkit-superset",
		},
	]

	# Test that allow returns true despite explicit deny when source is blinkit-superset
	allow_result := rules_main.allow with input as request_context
		with rules_main.compiled_rules as mock_rules

	allow_result == true
}

# Test that non-blinkit-superset source respects explicit deny rules
test_non_blinkit_superset_respects_explicit_deny if {
	request_context := {
		"source": "common_cluster",
		"user": "restricted_user",
		"groups": ["restricted_group"],
		"operation": "SelectFromColumns",
		"tool": "trinodb",
		"catalog": "restricted_catalog",
		"schema": "restricted_schema",
		"table": "restricted_table",
		"tenant_map": {"runtime_tenant": "zomato"},
		"action": {},
	}

	# Mock rules with explicit deny for this user/table
	mock_rules := [
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "restricted_user",
			"catalog": "restricted_catalog",
			"schema": "restricted_schema",
			"table": "restricted_table",
			"privileges": [],  # No privileges = explicit deny
		},
	]

	# Test that allow returns false for explicit deny when source is not blinkit-superset
	allow_result := rules_main.allow with input as request_context
		with rules_main.compiled_rules as mock_rules

	allow_result == false
}

# Test mixed source rules - blinkit-superset source should only get superset rules
test_mixed_source_rules_blinkit_superset_filtering if {
	request_context := {
		"source": "blinkit-superset",
		"user": "analyst",
		"groups": ["analysts"],
		"operation": "GetRowFilters",
		"tool": "trinodb",
		"catalog": "mixed_catalog",
		"schema": "mixed_schema",
		"table": "mixed_table",
		"tenant_map": {"runtime_tenant": "zomato"},
		"action": {},
	}

	# Multiple rules with different sources for same table
	mock_rules := [
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "mixed_catalog",
			"schema": "mixed_schema",
			"table": "mixed_table",
			"filter": "superset_rule_1 = 'RULE1'",
			"filter_environment": null,
			"privileges": ["SELECT"],
			"source": "blinkit-superset",
		},
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "mixed_catalog",
			"schema": "mixed_schema",
			"table": "mixed_table",
			"filter": "common_rule_1 = 'COMMON1'",
			"filter_environment": null,
			"privileges": ["SELECT"],
			"source": "common_cluster",
		},
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "mixed_catalog",
			"schema": "mixed_schema",
			"table": "mixed_table",
			"filter": "superset_rule_2 = 'RULE2'",
			"filter_environment": null,
			"privileges": ["SELECT"],
			"source": "blinkit-superset",
		},
		{
			"entity_category": "data_entity",
			"type": "table",
			"user": "analyst",
			"catalog": "mixed_catalog",
			"schema": "mixed_schema",
			"table": "mixed_table",
			"filter": "no_source_rule = 'NO_SOURCE'",
			"filter_environment": null,
			"privileges": ["SELECT"],
		},
	]

	row_filter := rules_main.row_filter with input as request_context
		with rules_main.compiled_rules as mock_rules

	# Should get the combined superset rules (priority logic will pick one)
	# Check that we got a superset rule and not the others
	row_filter.expression in {"superset_rule_1 = 'RULE1' AND superset_rule_2 = 'RULE2'"}

	# Should NOT get common cluster or no-source rules
	row_filter.expression != "common_rule_1 = 'COMMON1'"
	row_filter.expression != "no_source_rule = 'NO_SOURCE'"
}

# Test that blinkit-superset source denies access for not allowed operations
test_blinkit_superset_denies_not_allowed_operation if {
	request_context := {
		"source": "blinkit-superset",
		"user": "superset_user",
		"groups": ["superset_group"],
		"operation": "WriteSystemInformation", # This operation is NOT allowed for blinkit-superset
		"tool": "trinodb",
		"catalog": "test_catalog",
		"schema": "test_schema",
		"table": "test_table",
		"tenant_map": {"runtime_tenant": "zomato"},
		"action": {},
		"privilege": "WRITE",
		"op_type": "system_operations",
		"entity_category": "query_engine_entity",
	}

	# Test with mock rules that would normally allow this operation
	mock_rules := [
		{
			"entity_category": "query_engine_entity",
			"type": "system_information",
			"user": "superset_user",
			"allow": ["write"],
		},
	]

	# Test that allow returns false for not allowed operation even with matching rules
	allow_result := rules_main.allow with input as request_context
		with rules_main.compiled_rules as mock_rules

	# Should be denied because WriteSystemInformation is not in the allowed operations list
	allow_result == false
}
