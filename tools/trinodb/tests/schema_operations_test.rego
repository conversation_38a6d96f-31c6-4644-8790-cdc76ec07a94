package tools.trinodb.tests.schema_operations

import data.tools.trinodb.main
import rego.v1

# Test ShowSchemas operation - success
test_show_schemas_success if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowSchemas",
			"resource": {"catalog": {"name": "sales"}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analyst",
				"catalog": "sales",
				"allow": "read-only",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "analyst",
				"catalog": "sales",
				"schema": "*",
				"owner": true,
			},
		]

	result == true
}

# Test ShowSchemas operation - access denied
test_show_schemas_denied if {
	user := "restricted_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowSchemas",
			"resource": {"catalog": {"name": "sensitive_data"}},
		},
		"context": {
			"identity": {
				"groups": ["restricted"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "analyst",
			"catalog": "sales",
			"allow": "read-only",
		}]

	result == false
}

# Test ShowCreateSchema operation - success
test_show_create_schema_success if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowCreateSchema",
			"resource": {"schema": {
				"catalogName": "analytics",
				"schemaName": "reports",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "data_engineer",
				"catalog": "analytics",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "data_engineer",
				"catalog": "analytics",
				"schema": "reports",
				"owner": true,
			},
		]

	result == true
}

# Test CreateSchema operation - success
test_create_schema_success if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateSchema",
			"resource": {"schema": {
				"catalogName": "data_lake",
				"schemaName": "new_project",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "data_engineer",
				"catalog": "data_lake",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "data_engineer",
				"catalog": "data_lake",
				"schema": "*",
				"owner": true,
			},
		]

	result == true
}

# Test CreateSchema operation - insufficient privileges
test_create_schema_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateSchema",
			"resource": {"schema": {
				"catalogName": "production",
				"schemaName": "sensitive",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "analyst",
			"catalog": "production",
			"allow": "read-only",
		}]

	result == false
}

# Test DropSchema operation - success
test_drop_schema_success if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropSchema",
			"resource": {"schema": {
				"catalogName": "staging",
				"schemaName": "temp_data",
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_team"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "data_engineer",
				"catalog": "staging",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "data_engineer",
				"catalog": "staging",
				"schema": "temp_data",
				"owner": true,
			},
		]

	result == true
}

# Test DropSchema operation - access denied
test_drop_schema_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropSchema",
			"resource": {"schema": {
				"catalogName": "production",
				"schemaName": "critical_data",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "analyst",
			"catalog": "production",
			"allow": "read-only",
		}]

	result == false
}

# Test RenameSchema operation - success
test_rename_schema_success if {
	user := "schema_owner"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameSchema",
			"resource": {"schema": {
				"catalogName": "analytics",
				"schemaName": "old_name",
			}},
			"targetResource": {"schema": {
				"catalogName": "analytics",
				"schemaName": "new_name",
			}},
		},
		"context": {
			"identity": {
				"groups": ["schema_admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "schema_owner",
				"catalog": "analytics",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "schema_owner",
				"catalog": "analytics",
				"schema": "old_name",
				"owner": true,
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "schema_owner",
				"catalog": "analytics",
				"schema": "new_name",
				"owner": true,
			},
		]

	result == true
}

# Test SetSchemaAuthorization operation - success
test_set_schema_authorization_success if {
	user := "admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetSchemaAuthorization",
			"grantee": {
				"name": "team_lead",
				"type": "USER",
			},
			"resource": {"schema": {
				"catalogName": "shared",
				"schemaName": "team_data",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "admin",
				"catalog": "shared",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "admin",
				"catalog": "shared",
				"schema": "team_data",
				"owner": true,
			},
			{
				"entity_category": "data_entity",
				"type": "authorization",
				"original_user": "admin",
				"new_user": "team_lead",
				"allow": true,
			},
		]

	result == true
}

# Test SetSchemaAuthorization operation - insufficient privileges
test_set_schema_authorization_denied if {
	user := "team_lead"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetSchemaAuthorization",
			"resource": {"schema": {
				"catalogName": "production",
				"schemaName": "core_data",
			}},
		},
		"context": {
			"identity": {
				"groups": ["team_leads"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "schema",
			"user": "team_lead",
			"catalog": "production",
			"schema": "core_data",
			"owner": true,
		}]

	result == false
}

# --- Enhanced Schema Operations Tests with Denial Cases ---

# Test ShowCreateSchema operation - access denied
test_show_create_schema_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowCreateSchema",
			"resource": {"schema": {
				"catalogName": "sensitive",
				"schemaName": "private",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "regular_user",
				"catalog": "sensitive",
				"allow": "read-only",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "(admin|owner)", # Regular user doesn't match
				"catalog": "sensitive",
				"schema": "private",
				"owner": true,
			},
		]
}

# Test ShowCreateSchema operation - catalog access denied
test_show_create_schema_denied_catalog_access if {
	user := "external_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowCreateSchema",
			"resource": {"schema": {
				"catalogName": "restricted_catalog",
				"schemaName": "any_schema",
			}},
		},
		"context": {
			"identity": {
				"groups": ["external"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [] # No catalog access rules
}

# Test RenameSchema operation - denial for insufficient privileges
test_rename_schema_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameSchema",
			"resource": {"schema": {
				"catalogName": "production",
				"schemaName": "old_schema_name",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "regular_user",
				"catalog": "production",
				"allow": "read-only",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "(admin|schema_owner)", # Regular user doesn't have ownership
				"catalog": "production",
				"schema": "old_schema_name",
				"owner": true,
			},
		]
}

# Test RenameSchema operation - cross-catalog denial
test_rename_schema_denied_cross_catalog if {
	user := "schema_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameSchema",
			"resource": {"schema": {
				"catalogName": "catalog_a",
				"schemaName": "schema_to_rename",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "schema_admin",
				"catalog": "catalog_a",
				"allow": "read-only", # Only read access, not all
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "schema_admin",
				"catalog": "catalog_a",
				"schema": "schema_to_rename",
				"owner": true,
			},
		]
}

# Test SetSchemaAuthorization operation - denial for non-admin
test_set_schema_authorization_denied_non_admin if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetSchemaAuthorization",
			"resource": {"schema": {
				"catalogName": "production",
				"schemaName": "important_schema",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "regular_user",
				"catalog": "production",
				"allow": "read-only",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "(admin|super_admin)", # Only admins can change ownership
				"catalog": "production",
				"schema": "important_schema",
				"owner": true,
			},
		]
}

# Test SetSchemaAuthorization operation - grantee validation failure
test_set_schema_authorization_denied_invalid_grantee if {
	user := "schema_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetSchemaAuthorization",
			"resource": {"schema": {
				"catalogName": "analytics",
				"schemaName": "public",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "schema_admin",
				"catalog": "analytics",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "schema_admin",
				"catalog": "analytics",
				"schema": "public",
				"owner": true,
			},
			# No grantee rule - simulating invalid target user
		]
}

# Test DropSchema operation - denial for insufficient privileges
test_drop_schema_denied_insufficient_privileges if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropSchema",
			"resource": {"schema": {
				"catalogName": "production",
				"schemaName": "critical_schema",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analyst",
				"catalog": "production",
				"allow": "read-only",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "(admin|schema_owner)", # Only admin/owner can drop
				"catalog": "production",
				"schema": "critical_schema",
				"owner": true,
			},
		]
}

# Test DropSchema operation - catalog access denied
test_drop_schema_denied_catalog_access if {
	user := "external_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropSchema",
			"resource": {"schema": {
				"catalogName": "restricted",
				"schemaName": "any_schema",
			}},
		},
		"context": {
			"identity": {
				"groups": ["external"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [] # No access to restricted catalog
}

# Test edge case: Empty catalog name
test_schema_operations_edge_case_empty_catalog if {
	user := "test_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowSchemas",
			"resource": {"catalog": {"name": ""}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as []
}

# Test edge case: Schema operations with special characters
test_schema_operations_edge_case_special_chars if {
	user := "special_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateSchema",
			"resource": {"schema": {
				"catalogName": "test-catalog-2024",
				"schemaName": "schema_with_underscores",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "special_user",
				"catalog": "test-catalog-2024",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "schema",
				"user": "special_user",
				"catalog": "test-catalog-2024",
				"schema": "*",
				"owner": true,
			},
		]

	result == true
}
