package tools.trinodb.tests.system_operations

import data.tools.trinodb.main
import rego.v1

# Test system information access
test_system_information_access if {
	user := "admin"
	request_context := {
		"is_test": true,
		"action": {"operation": "ReadSystemInformation"},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test system information access permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "system_information",
			"user": "admin",
			"allow": ["READ"],
		}]

	# Verify the result
	result == true
}

# Test system information access denial
test_system_information_access_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {"operation": "ReadSystemInformation"},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test with rules that don't grant system info access to analysts
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "system_information",
			"user": "admin",
			"allow": ["READ"],
		}]

	# Verify the result
	result == false
}

# Test executing query operation
test_execute_query if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {"operation": "ExecuteQuery"},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test query execution permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "(analyst|data_scientist)",
			"allow": ["EXECUTE"],
		}]

	# Verify the result
	result == true
}

# Test impersonation permission
test_impersonation_permission if {
	user := "adhoc_jhub"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ImpersonateUser",
			"resource": {"user": {"user": "<EMAIL>"}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test impersonation permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "impersonation",
			"original_user": "(adhoc_jhub|bi_redash|blinkit_superset|bi_common|blinkit_backend_application)",
			"new_user": "(^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+)",
			"allow": true,
		}]

	# Verify the result
	result == true
}

# Test impersonation permission denial
test_impersonation_permission_denied if {
	user := "admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ImpersonateUser",
			"resource": {"user": {"user": "ceo"}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test impersonation with rules that don't allow impersonating CEO
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "impersonation",
			"original_user": "admin",
			"new_user": "(analyst|data_scientist)",
			"allow": true,
		}]

	# Verify the result
	result == false
}

# Test kill query permission
test_kill_query_permission if {
	user := "admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "KillQueryOwnedBy",
			"resource": {"user": {
				"groups": ["sample_group"],
				"user": "dummyabce",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test kill query permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "admin",
			"allow": ["KILL"],
		}]

	# Verify the result
	result == true
}

# Test view query permission
test_view_query_permission if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ViewQueryOwnedBy",
			"resource": {"user": {
				"groups": ["sample_group"],
				"user": "dummyabce",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test view query permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "query",
			"user": "(analyst|admin)",
			"allow": ["VIEW"],
		}]

	# Verify the result
	result == true
}

# Test function execution permission
test_function_execution_permission if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ExecuteFunction",
			"resource": {"function": {
				"catalogName": "system",
				"schemaName": "builtin",
				"functionName": "current_date",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test function execution permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "query_engine_entity",
				"type": "function",
				"user": "analyst",
				"catalog": "system",
				"schema": "builtin",
				"function": "current_date",
				"privileges": ["EXECUTE"],
			},
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analyst",
				"catalog": "system",
				"allow": "read-only",
			},
		]

	# Verify the result
	result == true
}

# Test setting system property
test_set_system_property_permission if {
	user := "admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetSystemSessionProperty",
			"resource": {"systemSessionProperty": {"name": "query_max_memory"}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test system property setting permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "session_property_operation",
			"user": "admin",
			"property": "(query_max_.*|task_.*)",
			"allow": true,
		}]

	# Verify the result
	result == true
}

# Test setting catalog property
test_set_catalog_property_permission if {
	user := "admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetCatalogSessionProperty",
			"resource": {"catalogSessionProperty": {
				"catalogName": "hive",
				"propertyName": "optimize_metadata_queries",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	# Test catalog property setting permissions
	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "session_property_operation",
				"user": "admin",
				"catalog": "hive",
				"property": "optimize_.*",
				"allow": true,
			},
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "admin",
				"catalog": "hive",
				"allow": "read-only",
			},
		]

	# Verify the result
	result == true
}

# Test WriteSystemInformation operation - success
test_write_system_information_success if {
	user := "admin"
	request_context := {
		"is_test": true,
		"action": {"operation": "WriteSystemInformation"},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "system_information",
			"user": "admin",
			"allow": ["WRITE"],
		}]

	result == true
}

# Test WriteSystemInformation operation - denied
test_write_system_information_denied if {
	user := "analyst"
	request_context := {
		"is_test": true,
		"action": {"operation": "WriteSystemInformation"},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "system_information",
			"user": "admin",
			"allow": "write",
		}]

	result == false
}

# --- Enhanced System Operations Tests with Edge Cases ---

# Test ReadSystemInformation operation - success with multiple permissions
test_read_system_information_multiple_permissions if {
	user := "system_admin"
	request_context := {
		"is_test": true,
		"action": {"operation": "ReadSystemInformation"},
		"context": {
			"identity": {
				"groups": ["system_admins", "monitoring"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "query_engine_entity",
				"type": "system_information",
				"user": "system_admin",
				"allow": ["READ", "WRITE"],
			},
			{
				"entity_category": "query_engine_entity",
				"type": "monitoring",
				"user": "system_admin",
				"allow": ["READ"],
			},
		]

	result == true
}

# Test SetSystemSessionProperty operation - enhanced success
test_set_system_session_property_enhanced_success if {
	user := "session_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetSystemSessionProperty",
			"resource": {"systemSessionProperty": {"name": "query_timeout"}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "session_property_operation",
			"user": "session_admin",
			"property": "query_timeout",
			"allow": true,
		}]

	result == true
}

# Test SetSystemSessionProperty operation - denied for security properties
test_set_system_session_property_denied_security if {
	user := "regular_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetSystemSessionProperty",
			"resource": {"systemSessionProperty": {
				"name": "security.authentication",
				"value": "disabled",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "session_property_operation",
			"user": "(security_admin|super_admin)", # Only security admins can modify security properties
			"property": "security.*",
			"allow": true,
		}]
}

# Test SetCatalogSessionProperty operation - enhanced success
test_set_session_property_operation_enhanced_success if {
	user := "catalog_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetCatalogSessionProperty",
			"resource": {"catalogSessionProperty": {
				"catalogName": "analytics",
				"propertyName": "hive.metastore.timeout",
			}},
		},
		"context": {
			"identity": {
				"groups": ["catalog_admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "catalog_admin",
				"catalog": "analytics",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "session_property_operation",
				"user": "catalog_admin",
				"catalog": "analytics",
				"property": "hive.metastore.timeout",
				"allow": true,
			},
		]

	result == true
}

# Test SetCatalogSessionProperty operation - denied for wrong catalog
test_set_session_property_operation_denied_wrong_catalog if {
	user := "analytics_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetCatalogSessionProperty",
			"resource": {"catalogSessionProperty": {
				"catalogName": "production",
				"propertyName": "connector.timeout",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analytics_admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "analytics_admin",
				"catalog": "analytics", # Only has access to analytics catalog, not production
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "session_property_operation",
				"user": "analytics_admin",
				"catalog": "analytics",
				"allow": true,
			},
		]
}

# Test SetCatalogSessionProperty operation - denied for read-only catalog access
test_set_session_property_operation_denied_read_only if {
	user := "read_only_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetCatalogSessionProperty",
			"resource": {"catalogSessionProperty": {
				"catalogName": "shared",
				"propertyName": "memory.limit",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "read_only_user",
			"catalog": "shared",
			"allow": "read-only", # Read-only access, cannot modify properties
		}]
}

# Test edge case: System operations with empty or invalid parameters
test_system_operations_edge_case_empty_property if {
	user := "test_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetSystemSessionProperty",
			"resource": {"systemSessionProperty": {"name": ""}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	not main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "system_session_property",
			"user": "test_admin",
			"allow": true,
		}]
}

# Test edge case: Multiple group membership for system operations
test_system_operations_edge_case_multiple_groups if {
	user := "multi_role_admin"
	request_context := {
		"is_test": true,
		"action": {"operation": "ReadSystemInformation"},
		"context": {
			"identity": {
				"groups": ["admins", "monitoring", "operators"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"entity_category": "query_engine_entity",
			"type": "system_information",
			"user": "multi_role_admin",
			"groups": ["monitoring"], # Matches one of the user's groups
			"allow": ["READ"],
		}]

	result == true
}
