package tools.trinodb.tests.table_operations

import data.tools.trinodb.main
import rego.v1

# --- SelectFromColumns Tests ---
test_select_from_columns_success if {
	user := "data_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
				"columns": ["id", "name", "email"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_analyst)",
				"privileges": ["SELECT"],
				"columns": [
					{"name": "id", "allow": true},
					{"name": "name", "allow": true},
					{"name": "email", "allow": true},
				],
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_analyst)",
			},
		]
}

test_select_from_columns_denied_no_column_access if {
	user := "data_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
				"columns": ["id", "name", "email", "ssn"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_analyst)",
				"privileges": ["SELECT"],
				"columns": [
					{"name": "id", "allow": true},
					{"name": "name", "allow": true},
					{"name": "email", "allow": true},
					{"name": "ssn", "allow": false}, # Failure Point: ssn column not allowed
				],
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_analyst)",
			},
		]
}

# --- CreateViewWithSelectFromColumns Tests ---
test_create_view_with_select_from_columns_success if {
	user := "data_engineer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateViewWithSelectFromColumns",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
				"columns": ["id", "name", "email"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["engineers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_engineer)",
				"privileges": ["GRANT_SELECT"],
				"columns": [
					{"name": "id", "allow": true},
					{"name": "name", "allow": true},
					{"name": "email", "allow": true},
				],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_engineer)",
			},
		]
}

# --- UpdateTableColumns Tests ---
test_update_table_columns_success if {
	user := "data_writer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "UpdateTableColumns",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
				"columns": ["status", "updated_at"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["writers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_writer)",
				"privileges": ["UPDATE"],
				"columns": [
					{"name": "status", "allow": true},
					{"name": "updated_at", "allow": true},
				],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_writer)",
			},
		]
}

# --- InsertIntoTable Tests ---
test_insert_into_table_success if {
	user := "data_writer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "InsertIntoTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["writers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_writer)",
				"privileges": ["INSERT"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_writer)",
			},
		]
}

# --- DeleteFromTable Tests ---
test_delete_from_table_success if {
	user := "data_writer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DeleteFromTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["writers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_writer)",
				"privileges": ["DELETE"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_writer)",
			},
		]
}

# --- DropTable Tests ---
test_drop_table_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- DropView Tests ---
test_drop_view_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_view",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_view",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- DropMaterializedView Tests ---
test_drop_materialized_view_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropMaterializedView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_mv",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_mv",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- TruncateTable Tests ---
test_truncate_table_success if {
	user := "data_writer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "TruncateTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["writers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_writer)",
				"privileges": ["DELETE"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_writer)",
			},
		]
}

# --- AddColumn Tests ---
test_add_column_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "AddColumn",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- AlterColumn Tests ---
test_alter_column_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "AlterColumn",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- CreateView Tests ---
test_create_view_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_view",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_view",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- DropColumn Tests ---
test_drop_column_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropColumn",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- RefreshMaterializedView Tests ---
test_refresh_materialized_view_success if {
	user := "data_writer"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RefreshMaterializedView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_mv",
			}},
		},
		"context": {
			"identity": {
				"groups": ["writers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_mv",
				"user": "(data_writer)",
				"privileges": ["UPDATE"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_writer)",
			},
		]
}

# --- RenameColumn Tests ---
test_rename_column_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameColumn",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- SetColumnComment Tests ---
test_set_column_comment_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetColumnComment",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- SetTableComment Tests ---
test_set_table_comment_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetTableComment",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- SetViewComment Tests ---
test_set_view_comment_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetViewComment",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_view",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_view",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- ShowColumns Tests ---
test_show_columns_success if {
	user := "data_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowColumns",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_analyst)",
				"privileges": ["SELECT"],
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_analyst)",
			},
		]
}

# --- ShowCreateTable Tests ---
test_show_create_table_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowCreateTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- ShowTables Tests ---
test_show_tables_success if {
	user := "data_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowTables",
			"resource": {"schema": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": ".*",
				"user": "(data_analyst)",
				"privileges": ["SELECT"],
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_analyst)",
			},
		]
}

# --- CreateMaterializedView Tests ---
test_create_materialized_view_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateMaterializedView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_mv",
				"properties": {},
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_mv",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- CreateTable Tests ---
test_create_table_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "new_table",
				"properties": {},
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"catalog": "test_catalog",
				"user": "(data_admin)",
				"allow": "all",
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
		]
}

# --- SetMaterializedViewProperties Tests ---
test_set_materialized_view_properties_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetMaterializedViewProperties",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_mv",
				"properties": {},
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_mv",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- SetTableProperties Tests ---
test_set_table_properties_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetTableProperties",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
				"properties": {},
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- RenameMaterializedView Tests ---
test_rename_materialized_view_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameMaterializedView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "old_mv_name",
			}},
			"targetResource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "new_mv_name",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "old_mv_name",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "new_mv_name",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- RenameTable Tests ---
test_rename_table_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "old_table_name",
			}},
			"targetResource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "new_table_name",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "old_table_name",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "new_table_name",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- RenameView Tests ---
test_rename_view_success if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "old_view_name",
			}},
			"targetResource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "new_view_name",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "old_view_name",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "new_view_name",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
}

# --- Negative Test Cases ---
# Test case: Operation with insufficient privileges
test_insufficient_privileges_denied if {
	user := "data_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropTable", # Requires OWNERSHIP
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_analyst)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_analyst)",
			},
		]
}

# Test case: User not authorized for catalog
test_catalog_access_denied if {
	user := "data_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "restricted_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
				"columns": ["id", "name"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["analysts"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "catalog",
				"catalog": "test_catalog", # Different catalog
				"user": "(data_analyst)",
				"allow": "read-only",
			},
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog", # Different catalog
				"schema": "test_schema",
				"table": "test_table",
				"user": "(data_analyst)",
				"privileges": ["SELECT"],
				"columns": [
					{"name": "id", "allow": true},
					{"name": "name", "allow": true},
				],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_analyst)",
			},
		]
}

# Test case: Table rename with access to source but not target
test_rename_table_partial_access_denied if {
	user := "data_admin"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "old_table_name",
			}},
			"targetResource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "restricted_schema", # Different schema
				"tableName": "new_table_name",
			}},
		},
		"context": {
			"identity": {
				"groups": ["admins"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "old_table_name",
				"user": "(data_admin)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(data_admin)",
			},
		]
	# Missing rule for the target schema/table

}

# === Enhanced Table Operations Tests - Adding Denial/Edge Cases ===

# --- DropTable Tests (Enhanced) ---
test_drop_table_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

test_drop_table_denied_no_catalog_access if {
	user := "unauthorized_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropTable",
			"resource": {"table": {
				"catalogName": "restricted_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as []
	# No catalog access rule for unauthorized_user

}

# --- DropView Tests (Enhanced) ---
test_drop_view_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_view",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_view",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- DropMaterializedView Tests (Enhanced) ---
test_drop_materialized_view_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropMaterializedView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_mv",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_mv",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- TruncateTable Tests (Enhanced) ---
test_truncate_table_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "TruncateTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not DELETE
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- AddColumn Tests (Enhanced) ---
test_add_column_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "AddColumn",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- AlterColumn Tests (Enhanced) ---
test_alter_column_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "AlterColumn",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- CreateView Tests (Enhanced) ---
test_create_view_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "new_view",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "new_view",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- DropColumn Tests (Enhanced) ---
test_drop_column_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "DropColumn",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- RefreshMaterializedView Tests (Enhanced) ---
test_refresh_materialized_view_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RefreshMaterializedView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_mv",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_mv",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not UPDATE
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- RenameColumn Tests (Enhanced) ---
test_rename_column_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameColumn",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- Comment Operations Tests (Enhanced) ---
test_set_column_comment_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetColumnComment",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

test_set_table_comment_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetTableComment",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

test_set_view_comment_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetViewComment",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_view",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_view",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- Show Operations Tests (Enhanced) ---
test_show_create_table_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "ShowCreateTable",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- Materialized View Operations Tests (Enhanced) ---
test_create_materialized_view_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateMaterializedView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "new_mv",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "new_mv",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- Properties Operations Tests (Enhanced) ---
test_set_materialized_view_properties_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetMaterializedViewProperties",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_mv",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_mv",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

test_set_table_properties_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SetTableProperties",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "table",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"table": "test_table",
				"user": "(regular_user)",
				"privileges": ["SELECT"], # Only SELECT, not OWNERSHIP
			},
			{
				"allow": "read-only",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- Rename Operations Tests (Enhanced) ---
test_rename_materialized_view_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameMaterializedView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "old_mv",
			}},
			"targetResource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "new_mv",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "materializedView",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"materializedView": "old_mv",
				"user": "(regular_user)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"entity_category": "data_entity",
				"type": "materializedView",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"materializedView": "new_mv",
				"user": "(regular_user)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

test_rename_view_denied_insufficient_privileges if {
	user := "regular_user"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "RenameView",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "old_view",
			}},
			"targetResource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "new_view",
			}},
		},
		"context": {
			"identity": {
				"groups": ["users"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [
			{
				"entity_category": "data_entity",
				"type": "view",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"view": "old_view",
				"user": "(regular_user)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"entity_category": "data_entity",
				"type": "view",
				"catalog": "test_catalog",
				"schema": "test_schema",
				"view": "new_view",
				"user": "(regular_user)",
				"privileges": ["OWNERSHIP"],
			},
			{
				"allow": "all",
				"catalog": "test_catalog",
				"entity_category": "data_entity",
				"type": "catalog",
				"user": "(regular_user)",
			},
		]
}

# --- Dynamic Rule Test ---
test_select_from_columns_success_dynamic if {
	user := "data_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
				"columns": ["id", "name", "email"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["dynamic_rule:test_catalog|test_schema|test_table:table"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"allow": "read-only",
			"catalog": "test_catalog",
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "(data_analyst)",
		}]
}

# --- Dynamic Rule Test ---
test_select_from_columns_failed_dynamic if {
	user := "data_analyst"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "test_catalog",
				"schemaName": "test_schema",
				"tableName": "test_table",
				"columns": ["id", "name", "email"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["dynamic_rule:xxxxxxx|test_schema|test_table:table"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}
	not main.allow with input as request_context
		with data.configurations.mock_rules as [{
			"allow": "read-only",
			"catalog": "test_catalog",
			"entity_category": "data_entity",
			"type": "catalog",
			"user": "(data_analyst)",
		}]
}
