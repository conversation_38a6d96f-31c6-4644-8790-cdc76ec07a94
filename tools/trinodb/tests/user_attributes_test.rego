package tools.trinodb.tests.user_attributes_test

import rego.v1

import data.tools.trinodb.main

# Test hierarchical user attribute rule resolution
test_hierarchical_user_attribute_rules if {
	user := "senior_data_scientist"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "temp_ml_features",
				"schemaName": "models",
				"tableName": "experimental_model_v2",
				"columns": ["model_id"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["data_scientists"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {"food_delivery": "zomato"}
		with data.configurations.global.catalog_tenant_mapping as {"temp_ml_features": "zomato"}
		with data.dynamic_data.user_attributes as {"senior_data_scientist": {
			"line_of_business": "food_delivery",
			"department": "analytics",
			"sub_department": "data_science",
			"designation": "senior_data_scientist",
		}}
		# Level 1: LOB rules
with 		data.configurations.zomato.user_attributes.line_of_business.food_delivery.rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"catalog": "temp_ml_features",
			"user": "*",
			"allow": "read-only",
		}]
		# Level 2: LOB + Department rules
with 		data.configurations.zomato.user_attributes.line_of_business.food_delivery.department.analytics.rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"catalog": "temp_ml_features",
			"schema": "*",
			"table": "*",
			"user": "*",
			"privileges": ["SELECT"],
		}]
		# Level 4: Full hierarchy rules
with 		data.configurations.zomato.user_attributes.line_of_business.food_delivery.department.analytics.sub_department.data_science.designation.senior_data_scientist.rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"catalog": "temp_ml_features",
			"schema": "models",
			"table": "experimental_*",
			"user": "*",
			"privileges": ["SELECT", "INSERT", "UPDATE", "DELETE"],
		}]

	result == true
}

# Test regex-based user attribute rules
test_regex_user_attribute_rules if {
	user := "key_account_manager_north"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "SelectFromColumns",
			"resource": {"table": {
				"catalogName": "temp_customers",
				"schemaName": "premium",
				"tableName": "vip_customers",
				"columns": ["customer_id"],
			}},
		},
		"context": {
			"identity": {
				"groups": ["account_managers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
		"user_attributes": {
			"line_of_business": "sales",
			"designation": "key_account_manager_north",
			"employee_id": "EMP1234",
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {"sales": "zomato"}
		with data.configurations.global.catalog_tenant_mapping as {"temp_customers": "zomato"}
		with data.dynamic_data.user_attributes as {"key_account_manager_north": {
			"line_of_business": "sales",
			"designation": "key_account_manager_north",
			"employee_id": "EMP1234",
		}}
		# Regex configuration
with 		data.configurations.zomato.user_attribute_configuration as {"regex_user_attributes": {
			"designation": {".*key_account_manager.*": ["specialized_rules", "key_accounts"]},
			"employee_id": {"^EMP[0-9]{4}$": ["employee_rules", "regular_employees"]},
		}}
		# Regex-matched rules
with 		data.configurations.zomato.specialized_rules.key_accounts.rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"catalog": "temp_customers",
			"schema": "premium",
			"table": "*",
			"user": "*",
			"privileges": ["SELECT", "UPDATE"],
		}]

	result == true
}

# Test custom path configuration
test_custom_path_user_attribute_rules if {
	user := "regional_manager_mumbai"
	request_context := {
		"is_test": true,
		"action": {
			"operation": "CreateTable",
			"resource": {"table": {
				"catalogName": "temp_regional",
				"schemaName": "mumbai",
				"tableName": "restaurant_metrics",
			}},
		},
		"context": {
			"identity": {
				"groups": ["regional_managers"],
				"user": user,
			},
			"softwareStack": {"trinoVersion": "455"},
		},
	}

	result := main.allow with input as request_context
		with data.configurations.global.lob_tenant_mapping as {"operations": "zomato"}
		with data.configurations.global.catalog_tenant_mapping as {"temp_regional": "zomato"}
		with data.dynamic_data.user_attributes as {"regional_manager_mumbai": {
			"line_of_business": "operations",
			"region": "west",
			"city": "mumbai",
			"role": "regional_manager",
		}}
		# Custom path configuration
with 		data.configurations.zomato.user_attribute_configuration as {"user_attribute_paths": {
			"location_based_path": ["location_rules", "user_attribute_template:region", "user_attribute_template:city"],
			"role_based_path": ["role_based_rules", "user_attribute_template:line_of_business", "user_attribute_template:role"],
		}}
		# Custom path rules
with 		data.configurations.zomato.location_rules.west.mumbai.rules as [{
			"entity_category": "data_entity",
			"type": "catalog",
			"catalog": "temp_regional",
			"user": "*",
			"allow": "all",
		}]
		with data.configurations.zomato.role_based_rules.operations.regional_manager.rules as [{
			"entity_category": "data_entity",
			"type": "table",
			"catalog": "temp_regional",
			"schema": "mumbai",
			"table": "*",
			"user": "*",
			"privileges": ["OWNERSHIP"],
		}]

	result == true
}
