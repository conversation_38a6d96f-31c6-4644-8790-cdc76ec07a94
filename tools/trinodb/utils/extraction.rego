package tools.trinodb.utils.extraction

import rego.v1

# Helper Rules

_get_catalog(resource_obj) := catalog if {
	# Relevant Ops: AccessCatalog, ShowSchemas (potentially FilterCatalogs via filterResources)
	catalog := object.get(resource_obj, ["catalog", "name"], null)
	catalog != null
} else := catalog if {
	# Relevant Ops: CreateSchema, RenameSchema, SchemaResourceActions, SetSchemaAuthorization
	catalog := object.get(resource_obj, ["schema", "catalogName"], null)
	catalog != null
} else := catalog if {
	# Relevant Ops: ColumnOperationsOnTableLikeObject, ExecuteTableProcedure, GetRowFilters,
	#               RenameTableLikeObject, SetAuthorizationOnTableLikeObject,
	#               TableResourceActions, TableWithPropertiesActions (potentially FilterColumns/Tables via filterResources)
	catalog := object.get(resource_obj, ["table", "catalogName"], null)
	catalog != null
} else := catalog if {
	# Relevant Ops: FunctionResourceActions (potentially FilterFunctions via filterResources)
	catalog := object.get(resource_obj, ["function", "catalogName"], null)
	catalog != null
} else := catalog if {
	# Relevant Ops: GetColumnMask
	catalog := object.get(resource_obj, ["column", "catalogName"], null)
	catalog != null
} else := catalog if {
	# Relevant Ops: SetCatalogSessionPropertyAction
	catalog := object.get(resource_obj, ["catalogSessionProperty", "catalogName"], null)
	catalog != null
} # Handle default value

else := null

# Extraction helpers are Trino-specific and should remain here. No changes needed.

# Get Schema Name
_get_schema(resource_obj) := schema if {
	# Relevant Ops: CreateSchema, RenameSchema, SchemaResourceActions, SetSchemaAuthorization
	schema := object.get(resource_obj, ["schema", "schemaName"], null)
	schema != null
} else := schema if {
	# Relevant Ops: ColumnOperationsOnTableLikeObject, ExecuteTableProcedure, GetRowFilters,
	#               RenameTableLikeObject, SetAuthorizationOnTableLikeObject,
	#               TableResourceActions, TableWithPropertiesActions (potentially FilterColumns/Tables via filterResources)
	schema := object.get(resource_obj, ["table", "schemaName"], null)
	schema != null
} else := schema if {
	# Relevant Ops: FunctionResourceActions (potentially FilterFunctions via filterResources)
	schema := object.get(resource_obj, ["function", "schemaName"], null)
	schema != null
} else := schema if {
	# Relevant Ops: GetColumnMask
	schema := object.get(resource_obj, ["column", "schemaName"], null)
	schema != null
} # Default value

else := null

# Get Table Name
_get_table(resource_obj) := table if {
	# Check path ["table", "tableName"]
	# Relevant Ops: ColumnOperationsOnTableLikeObject, ExecuteTableProcedure, GetRowFilters,
	#               RenameTableLikeObject, SetAuthorizationOnTableLikeObject,
	#               TableResourceActions, TableWithPropertiesActions (potentially FilterColumns/Tables via filterResources)
	table := object.get(resource_obj, ["table", "tableName"], null)
	table != null
} else := table if {
	# Check path ["column", "tableName"]
	# Relevant Ops: GetColumnMask
	table := object.get(resource_obj, ["column", "tableName"], null)
	table != null
} # Default value

else := null

# Get Columns (returns a list)
_get_columns(resource_obj) := columns if {
	# Check path ["table", "columns"], expecting array
	# Relevant Ops: ColumnOperationsOnTableLikeObject (potentially FilterColumns via filterResources)
	cols := object.get(resource_obj, ["table", "columns"], null)
	is_array(cols)

	# regal ignore:pointless-reassignment
	columns := cols
} else := columns if {
	# Check path ["column", "columnName"], expecting string
	# Relevant Ops: GetColumnMask
	col := object.get(resource_obj, ["column", "columnName"], null)
	col != null
	columns := [col] # Return as list
} # Default value (empty list)

else := []

_get_privilege(operation, op_priv_mapping) := privilege if {
	priv := op_priv_mapping[operation][0]
	privilege := {
		"op_type": priv.op_type,
		"privilege_type": priv.privilege_type,
		"entity_category": priv.entity_category,
	}
}
