# METADATA
# description: Utility package which extends the built-in functions
package utils.custom_regex

import rego.v1

# METADATA
# description: |
#   Matches the entire string against a regular expression.
#
#   pattern (string)  regular expression
#   value (string)    value to match against pattern
#
#   Returns:
#     result (boolean)
match_entire(pattern, value) if {
	pattern == ".*"
} else if {
	pattern == value
} else if {
	pattern_with_anchors := concat("", ["^", pattern, "$"])

	regex.match(pattern_with_anchors, value)
}

match_any_user(rule, user_pattern_field, user_value) if {
	pattern := object.get(rule, user_pattern_field, ".*")
	is_negated := object.get(rule, concat("", [user_pattern_field, "_negated"]), false)

	# Case 1: Not negated - subject must match pattern
	not is_negated
	match_entire(pattern, user_value)
} else if {
	pattern := object.get(rule, user_pattern_field, ".*")
	is_negated := object.get(rule, concat("", [user_pattern_field, "_negated"]), false)

	# Case 2: Negated - subject must NOT match pattern
	is_negated
	not match_entire(pattern, user_value)
}

# Match for any group in a list against a pattern
match_any_group(rule, group_pattern_field, groups) if {
	# Get the pattern and whether it's negated
	group_pattern := object.get(rule, group_pattern_field, ".*")
	is_negated := object.get(rule, concat("", [group_pattern_field, "_negated"]), false)

	# Case 1: Not negated - at least one group must match
	not is_negated
	some group_value in groups
	match_entire(group_pattern, group_value)
} else if {
	# Case 2: Negated - NO group should match
	group_pattern := object.get(rule, group_pattern_field, ".*")
	is_negated := object.get(rule, concat("", [group_pattern_field, "_negated"]), false)

	is_negated

	# Check that no group in the list matches the pattern
	count([g | some g in groups; match_entire(group_pattern, g)]) == 0
}
