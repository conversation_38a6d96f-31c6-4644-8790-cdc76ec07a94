package utils.generic

import rego.v1

sanitize_string(str) := regex.replace(replace(lower(str), " ", "_"), `[^a-z0-9_]`, "")

# Check if privileges are empty for any of the rules
any_empty_privileges(rules) if {
	some rule in rules
	count(object.get(rule, "privileges", [])) == 0
}

fetch_operations(tool, op_type) := object.get(data.configurations, ["global", "tools", tool, "operations", op_type], [])

fetch_priority_rule(rules) := rules if {
	has_priority := {true | some rule in rules; object.get(rule, "priority", 0) > 0}
	count(has_priority) == 0
} else := filtered_rules if {
	max_priority := max([object.get(rule, "priority", 0) | some rule in rules])
	filtered_rules := {rule |
		some rule in rules
		object.get(rule, "priority", 0) == max_priority
	}
}

substitute_values(values, template) := strings.replace_n(values, template)

handle_null_value(value, default_value) := default_value if is_null(value)

# Convert single string to array
normalize_to_array(x) := [x] if {
	is_string(x)
}

# Keep array as is
normalize_to_array(x) := x if {
	is_array(x)
}

# Handle catalog migration

handle_catalog_migration(catalog, tenant) := processed_catalog if {
	is_string(catalog)
	catalog == "zomato"
	tenant == "zomato"
	processed_catalog := replace(catalog, "zomato", "hive")
} else := processed_catalog if {
	processed_catalog := catalog
}
