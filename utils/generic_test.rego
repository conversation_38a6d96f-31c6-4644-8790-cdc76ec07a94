# METADATA
# title: Tests for String Sanitization
# description: Unit tests for the sanitize.string function.
package utils.generic_test

import data.utils.generic
import rego.v1

# Group 1: Basic functionality tests
test_basic_functionality if {
	# Tests strings that require no changes, simple lowercasing, and space replacement.
	generic.sanitize_string("hello_world_123") == "hello_world_123"
	generic.sanitize_string("Hello World") == "hello_world"
	generic.sanitize_string("MULTIPLE   SPACES") == "multiple___spaces"
	generic.sanitize_string("ALLCAPS") == "allcaps"
}

# Group 2: Special character handling tests
test_special_character_handling if {
	# Tests that various special symbols are correctly removed.
	generic.sanitize_string("My-App:v1.0!") == "myappv10"
	generic.sanitize_string("<EMAIL>") == "useremailcom"
	generic.sanitize_string("file(1)&copy") == "file1copy"
	generic.sanitize_string("Key/Value-Pairs") == "keyvaluepairs"
	generic.sanitize_string("$%^#*") == ""
}

# Group 3: Edge case tests
test_edge_cases if {
	# Tests empty strings, leading/trailing spaces, and non-ASCII characters.
	generic.sanitize_string("") == ""
	generic.sanitize_string("  leading and trailing  ") == "__leading_and_trailing__"
	generic.sanitize_string("München") == "mnchen" # Non-ASCII 'ü' is removed
	generic.sanitize_string("你好世界") == "" # Other unicode characters are removed
}

# Group 4: Complex combination test
test_complex_combination if {
	# A single test combining all transformations.
	bcd := generic.sanitize_string(" (Release) Candidate/v3.0 @ 2025! ")
	bcd == "_release_candidatev30__2025_"
}
