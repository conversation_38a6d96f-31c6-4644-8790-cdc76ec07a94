package utils.negation_test

import data.utils.custom_regex as util
import rego.v1

# Test basic user negation
test_user_negated_excludes_matching_user if {
	rule := {"user": "admin", "user_negated": true}

	# Should exclude the matching user
	not util.match_any_user(rule, "user", "admin")

	# Should include non-matching users
	util.match_any_user(rule, "user", "regular_user")
}

# Test basic group negation
test_group_negated_excludes_matching_groups if {
	rule := {"group": "banned", "group_negated": true}

	# Should exclude groups containing the banned group
	not util.match_any_group(rule, "group", ["banned", "users"])

	# Should include groups not containing the banned group
	util.match_any_group(rule, "group", ["admin", "users"])
}

# Test regex patterns with negation
test_negated_regex_patterns if {
	rule := {"user": "(admin|root)", "user_negated": true}

	# Should exclude users matching the regex
	not util.match_any_user(rule, "user", "admin")
	not util.match_any_user(rule, "user", "root")

	# Should include users not matching the regex
	util.match_any_user(rule, "user", "analyst")
}

# Test default behavior (no negated flag)
test_default_not_negated if {
	user_rule := {"user": "admin"}
	group_rule := {"group": "admin"}

	# Should work as normal matching when negated flag is missing
	util.match_any_user(user_rule, "user", "admin")
	not util.match_any_user(user_rule, "user", "other")

	util.match_any_group(group_rule, "group", ["admin"])
	not util.match_any_group(group_rule, "group", ["other"])
}

# Test empty groups with negation
test_empty_groups_with_negation if {
	rule := {"group": "admin", "group_negated": true}

	# Empty groups list should match (no excluded groups present)
	util.match_any_group(rule, "group", [])
}
