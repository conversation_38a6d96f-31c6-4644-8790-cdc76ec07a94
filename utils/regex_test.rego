# METADATA
# title: Tests for util.match_entire
# description: Unit tests for the util.match_entire function.
package utils.test

import data.utils.custom_regex as regex_handler
import rego.v1

test_match_entire_success if {
	# Exact match: pattern and value are identical
	regex_handler.match_entire("hello", "hello")
}

test_match_entire_no_match if {
	# Pattern does not match value
	not regex_handler.match_entire("hello", "world")
	not regex_handler.match_entire("start", "middle start")
}

test_match_entire_partial_match_fails if {
	# <PERSON><PERSON> partially matches the value, but should fail due to anchors
	not regex_handler.match_entire("hello", "hello world")
	not regex_handler.match_entire("hello", "world hello")
}

test_match_entire_empty_string if {
	# Matching an empty string
	regex_handler.match_entire("", "")
	not regex_handler.match_entire("", "non-empty")
	not regex_handler.match_entire("non-empty", "")
}

test_match_entire_special_characters if {
	# <PERSON>tern with special regex characters
	regex_handler.match_entire("a.c", "a.c") # Literal dot

	not regex_handler.match_entire("a\\.c", "abc") # Matches only literal dot, not regex wildcard

	# Pattern with regex anchors
	regex_handler.match_entire("^start$", "start") # Should match literal ^ and $
	not regex_handler.match_entire("^start$", "start middle")
}

test_match_entire_regex_patterns if {
	# Pattern uses valid regex with anchors added automatically
	regex_handler.match_entire("a.*z", "abcz") # Match: regex handles wildcard
	not regex_handler.match_entire("a.*z", "abc") # No match: doesn't end with 'z'
}

test_match_entire_numeric_strings if {
	# Numeric strings as input
	regex_handler.match_entire("\\d+", "12345") # Regex for one or more digits
	not regex_handler.match_entire("\\d+", "12345abc") # No match: includes non-digits
}
