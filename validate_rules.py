#!/usr/bin/env python3
"""
YAML Rules Validator

Validates YAML configuration files against the OPA rules JSON schema.
Usage: python validate_rules.py <yaml_file> [schema_file]
"""

import json
import yaml
import sys
import argparse
import glob
from pathlib import Path
try:
    import jsonschema
    from jsonschema import validate, ValidationError, Draft7Validator
except ImportError:
    print("Error: jsonschema package not found. Install it with:")
    print("pip install jsonschema")
    sys.exit(1)


def load_yaml_file(file_path):
    """Load and parse YAML file."""
    try:
        with open(file_path, 'r') as f:
            return yaml.safe_load(f)
    except yaml.YAMLError as e:
        print(f"Error parsing YAML file {file_path}: {e}")
        return None
    except FileNotFoundError:
        print(f"Error: YAML file {file_path} not found")
        return None


def load_schema_file(file_path):
    """Load and parse JSON schema file."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON schema file {file_path}: {e}")
        return None
    except FileNotFoundError:
        print(f"Error: Schema file {file_path} not found")
        return None


def validate_yaml_against_schema(yaml_data, schema):
    """Validate YAML data against JSON schema."""
    try:
        # Create validator
        validator = Draft7Validator(schema)
        
        # Check if schema itself is valid
        try:
            Draft7Validator.check_schema(schema)
        except jsonschema.SchemaError as e:
            print(f"Error: Invalid schema: {e}")
            return False
        
        # Validate the data
        errors = list(validator.iter_errors(yaml_data))
        
        if not errors:
            print("✅ Validation successful! YAML file conforms to the schema.")
            return True
        else:
            print("❌ Validation failed with the following errors:")
            for i, error in enumerate(errors, 1):
                path = " -> ".join(str(p) for p in error.absolute_path) if error.absolute_path else "root"
                print(f"\n{i}. Path: {path}")
                print(f"   Error: {error.message}")
                if error.validator_value:
                    print(f"   Expected: {error.validator_value}")
                if hasattr(error, 'instance') and error.instance is not None:
                    print(f"   Found: {error.instance}")
            return False
            
    except Exception as e:
        print(f"Error during validation: {e}")
        return False


def get_rule_statistics(yaml_data):
    """Get statistics about the rules in the YAML data."""
    if not isinstance(yaml_data, list):
        return None
    
    stats = {
        'total_rules': len(yaml_data),
        'by_type': {},
        'by_entity_category': {},
        'with_filters': 0,
        'with_masks': 0,
        'with_columns': 0,
        'with_privileges': 0
    }
    
    for rule in yaml_data:
        if isinstance(rule, dict):
            # Count by type
            rule_type = rule.get('type', 'unknown')
            stats['by_type'][rule_type] = stats['by_type'].get(rule_type, 0) + 1
            
            # Count by entity category
            entity_category = rule.get('entity_category', 'unknown')
            stats['by_entity_category'][entity_category] = stats['by_entity_category'].get(entity_category, 0) + 1
            
            # Count features
            if rule.get('filter'):
                stats['with_filters'] += 1
            if rule.get('mask') or (rule.get('columns') and any(col.get('mask') for col in rule['columns'] if isinstance(col, dict))):
                stats['with_masks'] += 1
            if rule.get('columns'):
                stats['with_columns'] += 1
            if rule.get('privileges'):
                stats['with_privileges'] += 1
    
    return stats


def print_statistics(stats):
    """Print rule statistics."""
    if not stats:
        return
    
    print(f"\n📊 Rule Statistics:")
    print(f"   Total rules: {stats['total_rules']}")
    
    print(f"\n   By type:")
    for rule_type, count in sorted(stats['by_type'].items()):
        print(f"     {rule_type}: {count}")
    
    print(f"\n   By entity category:")
    for category, count in sorted(stats['by_entity_category'].items()):
        print(f"     {category}: {count}")
    
    print(f"\n   Features:")
    print(f"     Rules with row filters: {stats['with_filters']}")
    print(f"     Rules with column masks: {stats['with_masks']}")
    print(f"     Rules with column specs: {stats['with_columns']}")
    print(f"     Rules with privileges: {stats['with_privileges']}")


def find_rules_yaml_files(base_path="configurations"):
    """Find all data.yaml files inside rules folders at any nested level."""
    base_path = Path(base_path)
    if not base_path.exists():
        print(f"Warning: Base path {base_path} does not exist")
        return []
    
    # Find all data.yaml files that are inside a 'rules' directory
    pattern = str(base_path / "**/rules/data.yaml")
    yaml_files = glob.glob(pattern, recursive=True)
    
    # Convert to Path objects and sort
    yaml_files = [Path(f) for f in yaml_files]
    yaml_files.sort()
    
    return yaml_files


def validate_multiple_files(yaml_files, schema, show_stats=False):
    """Validate multiple YAML files against schema."""
    results = []
    total_stats = {
        'total_rules': 0,
        'by_type': {},
        'by_entity_category': {},
        'with_filters': 0,
        'with_masks': 0,
        'with_columns': 0,
        'with_privileges': 0
    }
    
    print(f"Found {len(yaml_files)} YAML files to validate:\n")
    
    for yaml_file in yaml_files:
        print(f"📁 Validating: {yaml_file}")
        
        # Load YAML data
        yaml_data = load_yaml_file(yaml_file)
        if yaml_data is None:
            results.append((yaml_file, False, None))
            continue
        
        # Validate
        is_valid = validate_yaml_against_schema(yaml_data, schema)
        
        # Get statistics
        stats = get_rule_statistics(yaml_data) if show_stats else None
        if stats:
            # Aggregate statistics
            total_stats['total_rules'] += stats['total_rules']
            for rule_type, count in stats['by_type'].items():
                total_stats['by_type'][rule_type] = total_stats['by_type'].get(rule_type, 0) + count
            for category, count in stats['by_entity_category'].items():
                total_stats['by_entity_category'][category] = total_stats['by_entity_category'].get(category, 0) + count
            total_stats['with_filters'] += stats['with_filters']
            total_stats['with_masks'] += stats['with_masks']
            total_stats['with_columns'] += stats['with_columns']
            total_stats['with_privileges'] += stats['with_privileges']
        
        results.append((yaml_file, is_valid, stats))
        print()  # Add spacing between files
    
    # Print summary
    valid_count = sum(1 for _, is_valid, _ in results if is_valid)
    invalid_count = len(results) - valid_count
    
    print("=" * 60)
    print("📋 VALIDATION SUMMARY")
    print("=" * 60)
    print(f"✅ Valid files: {valid_count}")
    print(f"❌ Invalid files: {invalid_count}")
    print(f"📊 Total files processed: {len(results)}")
    
    if invalid_count > 0:
        print(f"\n❌ Files with validation errors:")
        for yaml_file, is_valid, _ in results:
            if not is_valid:
                print(f"   • {yaml_file}")
    
    if show_stats and total_stats['total_rules'] > 0:
        print(f"\n📊 AGGREGATE STATISTICS:")
        print_statistics(total_stats)
    
    return all(is_valid for _, is_valid, _ in results)


def check_dependencies():
    """Check if required dependencies are installed."""
    missing_deps = []
    
    try:
        import jsonschema
    except ImportError:
        missing_deps.append("jsonschema")
    
    try:
        import yaml
    except ImportError:
        missing_deps.append("PyYAML")
    
    if missing_deps:
        print("❌ Missing required dependencies:")
        for dep in missing_deps:
            print(f"   • {dep}")
        print("\nInstall them with:")
        print(f"   pip install {' '.join(missing_deps)}")
        return False
    
    return True


def validate_rules(args, schema_under_check, config_dir):
    # Load schema
    print(f"Loading schema from: {schema_under_check}")
    schema = load_schema_file(schema_under_check)
    if schema is None:
        sys.exit(1)

    # Determine which files to validate
    if args.yaml_file:
        # Validate single specific file
        print(f"Validating single file: {args.yaml_file}")
        print(f"Using schema: {schema_under_check}\n")
        
        yaml_data = load_yaml_file(args.yaml_file)
        if yaml_data is None:
            sys.exit(1)

        is_valid = validate_yaml_against_schema(yaml_data, schema)

        if args.stats:
            stats = get_rule_statistics(yaml_data)
            print_statistics(stats)
        
        return is_valid

    else:
        # Auto-discover and validate all data.yaml files in rules folders
        print(f"Auto-discovering data.yaml files in {config_dir}/*/rules/ at any nested level...")
        yaml_files = find_rules_yaml_files(config_dir)
        
        if not yaml_files:
            print(f"No data.yaml files found in rules folders under {config_dir}/")
            print("Directory structure should be like: configurations/tenant/rules/data.yaml")
            print("                                    or: configurations/global/rules/data.yaml")
            print("                                    or: approvers/configurations/data.yaml")
            sys.exit(1)
        
        print(f"Using schema: {schema_under_check}\n")
        is_all_valid = validate_multiple_files(yaml_files, schema, args.stats)
        
        return is_all_valid

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description='Validate YAML rules against JSON schema',
        epilog='''
Examples:
  python validate_rules.py                           # Auto-discover and validate all data.yaml files
  python validate_rules.py --stats                   # Same as above but with statistics
  python validate_rules.py file.yaml                 # Validate specific file
  python validate_rules.py --config-dir ./configs    # Search in different directory
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument('yaml_file', nargs='?', 
                      help='Path to specific YAML file to validate (optional - if not provided, auto-discovers all)')
    parser.add_argument('--schema', '-s', 
                      default='schema/rules-schema.json',
                      help='Path to the JSON schema file (default: schema/rules-schema.json)')
    parser.add_argument('--approver-schema', '-a', 
                      default='schema/approver-rules-schema.json',
                      help='Path to the JSON schema file (default: schema/approver-rules-schema.json)')
    parser.add_argument('--stats', action='store_true',
                      help='Show statistics about the rules')
    parser.add_argument('--config-dir', '-c',
                      default='configurations',
                      help='Base configurations directory to search (default: configurations)')
    parser.add_argument('--approver-config-dir', '-ac',
                      default='approvers/configurations',
                      help='Base approver configurations directory to search (default: approvers/configurations)')

    args = parser.parse_args()

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    is_all_valid = validate_rules(args, args.schema, args.config_dir)
    is_all_valid = is_all_valid and validate_rules(args, args.approver_schema, args.approver_config_dir)

    sys.exit(0 if is_all_valid else 1)


if __name__ == "__main__":
    main()
